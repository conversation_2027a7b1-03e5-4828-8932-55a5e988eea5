# Git
.git
.gitignore
README.md

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Documentation
docs/
*.md

# Tests
tests/
test_*
*_test.py

# Development files
docker-compose.override.yml
docker-compose.dev.yml

# Temporary files
tmp/
temp/
