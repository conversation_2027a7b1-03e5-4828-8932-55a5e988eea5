# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
test-results/
coverage/
*.min.js
*.min.css

# Dependency directories
node_modules/
jspm_packages/
.pnpm-store/
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz

# Build output
dist/
dist-ssr/
build/
out/
.cache/
.temp/

# Local development
.env
.env.local
.env.*.local
*.local
.cert
.pem

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*.swo
*~
~$*
.DS_Store
Thumbs.db

# System Files
*.pid
*.seed
*.pid.lock
*.tsbuildinfo

# Debug logs
*.log*
error_log

# Package manager specific
package-lock.json
yarn.lock
pnpm-lock.yaml
.vscode