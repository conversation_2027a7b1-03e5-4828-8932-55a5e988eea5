# 使用Python 3.11官方镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# 使用国内镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libaio1 \
    libaio-dev \
    unzip \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制Oracle Instant Client文件
COPY instantclient-basic-linux.x64-*********.0dbru.zip /tmp/
RUN mkdir -p /opt/oracle && \
    cd /opt/oracle && \
    unzip /tmp/instantclient-basic-linux.x64-*********.0dbru.zip && \
    rm -f /tmp/*.zip

# 设置Oracle环境变量
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_19_23:$LD_LIBRARY_PATH
ENV PATH=/opt/oracle/instantclient_19_23:$PATH

# 复制requirements文件
COPY requirements.txt .

# 使用国内pip镜像源安装Python依赖
RUN pip install --no-cache-dir --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制项目文件
COPY . .

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
