# 新架构迁移实战教程

## 📖 教程概述

本教程将通过一个完整的实际功能迁移示例，手把手教您如何将现有代码重构到新的架构模式。我们将以**用户管理功能**为例，展示从旧架构到新架构的完整迁移过程。

## 🎯 学习目标

通过本教程，您将学会：
- 如何分析和拆解现有代码
- 如何按照新架构创建各层代码
- 如何使用新的异常处理和监控系统
- 如何编写测试验证迁移结果
- 如何逐步迁移而不影响现有功能

## 📋 迁移前准备

### 1. 创建迁移分支
```bash
git checkout -b feature/user-management-refactor
```

### 2. 分析现有代码
假设我们有一个现有的用户管理路由文件：

```python
# app/routers/system/user_old.py (现有代码)
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from app.database.session import get_db
import logging

router = APIRouter(prefix="/users", tags=["users"])
logger = logging.getLogger(__name__)

@router.post("/")
async def create_user(user_data: dict, db: AsyncSession = Depends(get_db)):
    try:
        # 验证用户名是否存在
        existing_user = await db.execute(
            text("SELECT * FROM users WHERE username = :username"),
            {"username": user_data["username"]}
        )
        if existing_user.fetchone():
            raise HTTPException(status_code=400, detail="用户名已存在")
        
        # 密码加密
        hashed_password = hash_password(user_data["password"])
        
        # 插入用户
        result = await db.execute(
            text("""
                INSERT INTO users (username, email, hashed_password, is_active)
                VALUES (:username, :email, :password, :is_active)
            """),
            {
                "username": user_data["username"],
                "email": user_data["email"],
                "password": hashed_password,
                "is_active": True
            }
        )
        await db.commit()
        
        return {"status": "success", "message": "用户创建成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.get("/{user_id}")
async def get_user(user_id: int, db: AsyncSession = Depends(get_db)):
    try:
        result = await db.execute(
            text("SELECT * FROM users WHERE id = :id"),
            {"id": user_id}
        )
        user = result.fetchone()
        
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "is_active": user.is_active
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户失败: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
```

### 3. 识别问题
现有代码存在的问题：
- ❌ 业务逻辑和HTTP处理混合
- ❌ 直接使用原生SQL，缺乏抽象
- ❌ 异常处理重复且不统一
- ❌ 缺乏类型提示和数据验证
- ❌ 没有性能监控和结构化日志

## 🏗️ 步骤一：创建数据模型和Schema

### 1.1 创建Pydantic Schema
```python
# app/schemas/system/user.py
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    is_active: bool = Field(default=True, description="是否激活")

class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., min_length=6, description="密码")

class UserUpdate(BaseModel):
    """更新用户模型"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None
    password: Optional[str] = Field(None, min_length=6)

class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    """用户列表响应"""
    users: list[UserResponse]
    total: int
    page: int
    size: int
```

### 1.2 确认SQLAlchemy模型
```python
# app/models/system/user.py (确认现有模型符合要求)
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.sql import func
from app.database.session import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
```

## 🏗️ 步骤二：创建仓储层

```python
# app/repositories/system/user.py
from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from app.repositories.base import BaseRepository
from app.models.system.user import User
from app.schemas.system.user import UserCreate, UserUpdate
from app.utils.database import handle_database_exceptions
from app.utils.monitoring import timing_decorator

class UserRepository(BaseRepository[User]):
    """用户仓储类"""
    
    def __init__(self):
        super().__init__(User)
    
    @timing_decorator(metric_name="user.repository.find_by_username")
    @handle_database_exceptions("查询用户名")
    async def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User).where(User.username == username)
            )
            return result.scalar_one_or_none()
    
    @timing_decorator(metric_name="user.repository.find_by_email")
    @handle_database_exceptions("查询邮箱")
    async def find_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查找用户"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User).where(User.email == email)
            )
            return result.scalar_one_or_none()
    
    @timing_decorator(metric_name="user.repository.exists_by_username")
    @handle_database_exceptions("检查用户名存在")
    async def exists_by_username(self, username: str) -> bool:
        """检查用户名是否存在"""
        user = await self.find_by_username(username)
        return user is not None
    
    @timing_decorator(metric_name="user.repository.exists_by_email")
    @handle_database_exceptions("检查邮箱存在")
    async def exists_by_email(self, email: str) -> bool:
        """检查邮箱是否存在"""
        user = await self.find_by_email(email)
        return user is not None
    
    @timing_decorator(metric_name="user.repository.get_active_users")
    @handle_database_exceptions("查询活跃用户")
    async def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """获取活跃用户列表"""
        async with self.get_session() as session:
            result = await session.execute(
                select(User)
                .where(User.is_active == True)
                .offset(skip)
                .limit(limit)
                .order_by(User.created_at.desc())
            )
            return result.scalars().all()
```

## 🏗️ 步骤三：创建服务层

```python
# app/services/system/user.py
from typing import Optional, List
from passlib.context import CryptContext

from app.services.base import BaseService
from app.repositories.system.user import UserRepository
from app.schemas.system.user import UserCreate, UserUpdate, UserResponse
from app.models.system.user import User
from app.exceptions import BusinessException, ValidationException
from app.utils.monitoring import timing_decorator, performance_context
from app.utils.exception_decorators import handle_business_errors
from app.utils.logger import get_logger
from app.utils.types import PaginationParams, PaginationResult

logger = get_logger("user_service")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class UserService(BaseService[User, UserCreate, UserUpdate]):
    """用户服务类"""
    
    def __init__(self):
        self.repository = UserRepository()
        super().__init__(self.repository)
    
    @timing_decorator(metric_name="user.service.create", slow_threshold=2.0)
    @handle_business_errors
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """创建用户"""
        async with performance_context("create_user", username=user_data.username):
            # 验证用户名唯一性
            if await self.repository.exists_by_username(user_data.username):
                raise ValidationException("用户名已存在")
            
            # 验证邮箱唯一性
            if await self.repository.exists_by_email(user_data.email):
                raise ValidationException("邮箱已被使用")
            
            # 密码加密
            hashed_password = self._hash_password(user_data.password)
            
            # 创建用户对象
            user_dict = user_data.dict(exclude={"password"})
            user_dict["hashed_password"] = hashed_password
            
            # 保存到数据库
            user = await self.repository.create(user_dict)
            
            logger.info(f"用户创建成功: {user.username}", extra={
                "user_id": user.id,
                "username": user.username,
                "email": user.email
            })
            
            return UserResponse.from_orm(user)
    
    @timing_decorator(metric_name="user.service.get_by_id")
    @handle_business_errors
    async def get_user_by_id(self, user_id: int) -> Optional[UserResponse]:
        """根据ID获取用户"""
        user = await self.repository.get_by_id(user_id)
        if not user:
            return None
        
        return UserResponse.from_orm(user)
    
    @timing_decorator(metric_name="user.service.get_by_username")
    @handle_business_errors
    async def get_user_by_username(self, username: str) -> Optional[UserResponse]:
        """根据用户名获取用户"""
        user = await self.repository.find_by_username(username)
        if not user:
            return None
        
        return UserResponse.from_orm(user)
    
    @timing_decorator(metric_name="user.service.list_users")
    @handle_business_errors
    async def list_users(self, pagination: PaginationParams) -> PaginationResult[UserResponse]:
        """获取用户列表"""
        # 获取总数
        total = await self.repository.count()
        
        # 获取用户列表
        users = await self.repository.get_all(
            skip=pagination.offset,
            limit=pagination.size
        )
        
        # 转换为响应模型
        user_responses = [UserResponse.from_orm(user) for user in users]
        
        return PaginationResult.create(
            items=user_responses,
            total=total,
            page=pagination.page,
            size=pagination.size
        )
    
    @timing_decorator(metric_name="user.service.update")
    @handle_business_errors
    async def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[UserResponse]:
        """更新用户"""
        # 检查用户是否存在
        existing_user = await self.repository.get_by_id(user_id)
        if not existing_user:
            raise BusinessException("用户不存在")
        
        # 如果更新用户名，检查唯一性
        if user_data.username and user_data.username != existing_user.username:
            if await self.repository.exists_by_username(user_data.username):
                raise ValidationException("用户名已存在")
        
        # 如果更新邮箱，检查唯一性
        if user_data.email and user_data.email != existing_user.email:
            if await self.repository.exists_by_email(user_data.email):
                raise ValidationException("邮箱已被使用")
        
        # 准备更新数据
        update_data = user_data.dict(exclude_unset=True)
        
        # 如果更新密码，进行加密
        if "password" in update_data:
            update_data["hashed_password"] = self._hash_password(update_data.pop("password"))
        
        # 执行更新
        updated_user = await self.repository.update(user_id, update_data)
        
        logger.info(f"用户更新成功: {updated_user.username}", extra={
            "user_id": user_id,
            "updated_fields": list(update_data.keys())
        })
        
        return UserResponse.from_orm(updated_user)
    
    @timing_decorator(metric_name="user.service.delete")
    @handle_business_errors
    async def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        user = await self.repository.get_by_id(user_id)
        if not user:
            raise BusinessException("用户不存在")
        
        success = await self.repository.delete(user_id)
        
        if success:
            logger.info(f"用户删除成功: {user.username}", extra={
                "user_id": user_id,
                "username": user.username
            })
        
        return success
    
    def _hash_password(self, password: str) -> str:
        """密码加密"""
        return pwd_context.hash(password)
    
    def _verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
```

## 🏗️ 步骤四：创建控制器层

```python
# app/controllers/system/user.py
from fastapi import HTTPException, status

from app.controllers.base import CRUDController
from app.services.system.user import UserService
from app.schemas.system.user import UserCreate, UserUpdate, UserResponse, UserListResponse
from app.utils.types import APIResponse, PaginationParams, PaginationResult
from app.exceptions import BusinessException, ValidationException
from app.utils.logger import get_logger, set_request_context

logger = get_logger("user_controller")

class UserController(CRUDController[UserService, UserCreate, UserUpdate, UserResponse]):
    """用户控制器"""
    
    def __init__(self):
        super().__init__(UserService)
    
    async def create_user(self, user_data: UserCreate) -> APIResponse[UserResponse]:
        """创建用户"""
        set_request_context(operation="create_user", username=user_data.username)
        
        try:
            user = await self.service.create_user(user_data)
            return APIResponse.success(user, "用户创建成功")
        
        except ValidationException as e:
            logger.warning(f"用户创建验证失败: {e.detail}")
            return APIResponse.error(f"创建失败: {e.detail}")
        
        except BusinessException as e:
            logger.error(f"用户创建业务异常: {e.detail}")
            return APIResponse.error(f"创建失败: {e.detail}")
    
    async def get_user(self, user_id: int) -> APIResponse[UserResponse]:
        """获取用户"""
        set_request_context(operation="get_user", user_id=user_id)
        
        user = await self.service.get_user_by_id(user_id)
        if not user:
            return APIResponse.error("用户不存在", status_code=404)
        
        return APIResponse.success(user, "获取成功")
    
    async def list_users(self, pagination: PaginationParams) -> APIResponse[PaginationResult[UserResponse]]:
        """获取用户列表"""
        set_request_context(operation="list_users", page=pagination.page, size=pagination.size)
        
        try:
            result = await self.service.list_users(pagination)
            return APIResponse.success(result, "获取成功")
        
        except BusinessException as e:
            logger.error(f"获取用户列表失败: {e.detail}")
            return APIResponse.error(f"获取失败: {e.detail}")
    
    async def update_user(self, user_id: int, user_data: UserUpdate) -> APIResponse[UserResponse]:
        """更新用户"""
        set_request_context(operation="update_user", user_id=user_id)
        
        try:
            user = await self.service.update_user(user_id, user_data)
            if not user:
                return APIResponse.error("用户不存在", status_code=404)
            
            return APIResponse.success(user, "更新成功")
        
        except ValidationException as e:
            logger.warning(f"用户更新验证失败: {e.detail}")
            return APIResponse.error(f"更新失败: {e.detail}")
        
        except BusinessException as e:
            logger.error(f"用户更新业务异常: {e.detail}")
            return APIResponse.error(f"更新失败: {e.detail}")
    
    async def delete_user(self, user_id: int) -> APIResponse[bool]:
        """删除用户"""
        set_request_context(operation="delete_user", user_id=user_id)
        
        try:
            success = await self.service.delete_user(user_id)
            return APIResponse.success(success, "删除成功")
        
        except BusinessException as e:
            logger.error(f"用户删除失败: {e.detail}")
            return APIResponse.error(f"删除失败: {e.detail}")
```

## 🏗️ 步骤五：更新路由层

```python
# app/routers/system/user.py (新版本)
from fastapi import APIRouter, Query
from typing import Optional

from app.controllers.system.user import UserController
from app.schemas.system.user import UserCreate, UserUpdate
from app.utils.types import PaginationParams

router = APIRouter(prefix="/users", tags=["users"])
controller = UserController()

@router.post("/", summary="创建用户")
async def create_user(user_data: UserCreate):
    """创建新用户"""
    return await controller.create_user(user_data)

@router.get("/{user_id}", summary="获取用户")
async def get_user(user_id: int):
    """根据ID获取用户信息"""
    return await controller.get_user(user_id)

@router.get("/", summary="获取用户列表")
async def list_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取用户列表"""
    pagination = PaginationParams(page=page, size=size)
    return await controller.list_users(pagination)

@router.put("/{user_id}", summary="更新用户")
async def update_user(user_id: int, user_data: UserUpdate):
    """更新用户信息"""
    return await controller.update_user(user_id, user_data)

@router.delete("/{user_id}", summary="删除用户")
async def delete_user(user_id: int):
    """删除用户"""
    return await controller.delete_user(user_id)
```

## 🧪 步骤六：编写测试

### 6.1 单元测试
```python
# tests/test_user_service.py
import pytest
from unittest.mock import AsyncMock, MagicMock

from app.services.system.user import UserService
from app.schemas.system.user import UserCreate, UserUpdate
from app.exceptions import ValidationException

@pytest.fixture
def user_service():
    service = UserService()
    service.repository = AsyncMock()
    return service

@pytest.fixture
def sample_user_create():
    return UserCreate(
        username="testuser",
        email="<EMAIL>",
        password="password123"
    )

@pytest.mark.asyncio
async def test_create_user_success(user_service, sample_user_create):
    """测试创建用户成功"""
    # Mock repository methods
    user_service.repository.exists_by_username.return_value = False
    user_service.repository.exists_by_email.return_value = False
    
    mock_user = MagicMock()
    mock_user.id = 1
    mock_user.username = "testuser"
    mock_user.email = "<EMAIL>"
    user_service.repository.create.return_value = mock_user
    
    # Execute
    result = await user_service.create_user(sample_user_create)
    
    # Assert
    assert result.username == "testuser"
    assert result.email == "<EMAIL>"
    user_service.repository.create.assert_called_once()

@pytest.mark.asyncio
async def test_create_user_duplicate_username(user_service, sample_user_create):
    """测试创建用户 - 用户名重复"""
    user_service.repository.exists_by_username.return_value = True
    
    with pytest.raises(ValidationException) as exc_info:
        await user_service.create_user(sample_user_create)
    
    assert "用户名已存在" in str(exc_info.value)
```

### 6.2 集成测试
```python
# tests/test_user_integration.py
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_create_user_endpoint():
    """测试创建用户接口"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post("/users/", json={
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "password123"
        })
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["data"]["username"] == "testuser"

@pytest.mark.asyncio
async def test_get_user_endpoint():
    """测试获取用户接口"""
    async with AsyncClient(app=app, base_url="http://test") as client:
        # 先创建用户
        create_response = await client.post("/users/", json={
            "username": "testuser2",
            "email": "<EMAIL>",
            "password": "password123"
        })
        user_id = create_response.json()["data"]["id"]
        
        # 获取用户
        response = await client.get(f"/users/{user_id}")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["data"]["username"] == "testuser2"
```

## 🔄 步骤七：迁移验证

### 7.1 功能对比测试
创建一个脚本来对比新旧接口的响应：

```python
# scripts/migration_validation.py
import asyncio
import httpx
from typing import Dict, Any

async def test_api_compatibility():
    """测试API兼容性"""
    base_url = "http://localhost:8000"
    
    test_cases = [
        {
            "name": "创建用户",
            "method": "POST",
            "url": "/users/",
            "data": {
                "username": "migration_test",
                "email": "<EMAIL>",
                "password": "password123"
            }
        },
        {
            "name": "获取用户列表",
            "method": "GET",
            "url": "/users/?page=1&size=10"
        }
    ]
    
    async with httpx.AsyncClient() as client:
        for test_case in test_cases:
            print(f"测试: {test_case['name']}")
            
            if test_case["method"] == "POST":
                response = await client.post(
                    f"{base_url}{test_case['url']}", 
                    json=test_case["data"]
                )
            else:
                response = await client.get(f"{base_url}{test_case['url']}")
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.json()}")
            print("-" * 50)

if __name__ == "__main__":
    asyncio.run(test_api_compatibility())
```

### 7.2 性能对比
```python
# scripts/performance_comparison.py
import asyncio
import time
import httpx
from statistics import mean

async def benchmark_endpoint(url: str, data: dict = None, iterations: int = 100):
    """性能基准测试"""
    times = []
    
    async with httpx.AsyncClient() as client:
        for _ in range(iterations):
            start_time = time.time()
            
            if data:
                response = await client.post(url, json=data)
            else:
                response = await client.get(url)
            
            end_time = time.time()
            times.append(end_time - start_time)
    
    return {
        "avg_time": mean(times),
        "min_time": min(times),
        "max_time": max(times),
        "total_requests": iterations
    }

async def compare_performance():
    """对比新旧接口性能"""
    base_url = "http://localhost:8000"
    
    # 测试获取用户列表
    print("测试获取用户列表性能...")
    result = await benchmark_endpoint(f"{base_url}/users/?page=1&size=20")
    print(f"平均响应时间: {result['avg_time']:.3f}s")
    print(f"最小响应时间: {result['min_time']:.3f}s")
    print(f"最大响应时间: {result['max_time']:.3f}s")

if __name__ == "__main__":
    asyncio.run(compare_performance())
```

## 📊 步骤八：监控和观察

### 8.1 查看性能指标
```python
# scripts/check_metrics.py
from app.utils.monitoring import metrics_collector, get_performance_summary

def check_user_metrics():
    """检查用户相关指标"""
    # 获取性能摘要
    summary = get_performance_summary(since_minutes=60)
    
    print("=== 用户服务性能指标 ===")
    for operation in summary["top_slow_operations"]:
        if "user" in operation["operation"]:
            print(f"操作: {operation['operation']}")
            print(f"调用次数: {operation['count']}")
            print(f"平均耗时: {operation['avg_duration']:.3f}s")
            print(f"最大耗时: {operation['max_duration']:.3f}s")
            print("-" * 30)

if __name__ == "__main__":
    check_user_metrics()
```

### 8.2 日志分析
```bash
# 查看结构化日志
tail -f logs/app.log | grep "user_service" | jq '.'

# 查看性能日志
tail -f logs/app.log | grep "慢查询检测" | jq '.'
```

## ✅ 迁移完成检查清单

- [ ] **Schema层**: 创建了完整的Pydantic模型
- [ ] **仓储层**: 实现了数据访问抽象，使用了装饰器
- [ ] **服务层**: 分离了业务逻辑，添加了验证和监控
- [ ] **控制器层**: 统一了HTTP处理，使用了新的响应格式
- [ ] **路由层**: 简化了路由处理，委托给控制器
- [ ] **异常处理**: 使用了新的异常类和处理机制
- [ ] **监控日志**: 添加了性能监控和结构化日志
- [ ] **测试**: 编写了单元测试和集成测试
- [ ] **验证**: 确认功能正常，性能符合预期

## 🎉 总结

通过这个完整的迁移示例，您学会了：

1. **分层架构**: 如何将代码按职责分离到不同层次
2. **异常处理**: 如何使用新的异常处理系统
3. **监控日志**: 如何添加性能监控和结构化日志
4. **测试驱动**: 如何编写测试确保迁移质量
5. **渐进迁移**: 如何在不影响现有功能的情况下进行重构

现在您可以按照这个模式，逐步迁移其他功能模块。记住：
- 一次迁移一个模块
- 充分测试每个步骤
- 监控性能变化
- 保持API兼容性

祝您重构顺利！🚀
