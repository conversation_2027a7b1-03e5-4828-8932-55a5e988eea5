# 后端项目重构迁移指南

## 📋 概述

本指南将帮助您按照新的架构模式，逐步重构和迁移现有的后端代码。新架构采用了分层设计、依赖注入、统一异常处理等现代化模式，显著提升了代码的可维护性和可读性。

## 🏗️ 新架构概览

```
app/
├── controllers/          # 控制器层 - HTTP请求处理
├── services/            # 服务层 - 业务逻辑
├── repositories/        # 仓储层 - 数据访问
├── models/             # 数据模型
├── schemas/            # Pydantic模式
├── core/               # 核心配置
├── middleware/         # 中间件
├── utils/              # 工具类
└── exceptions.py       # 异常定义
```

## 🚀 迁移步骤

### 第一步：准备工作

1. **备份现有代码**
```bash
git checkout -b refactor-backup
git add .
git commit -m "备份重构前代码"
git checkout -b feature/architecture-refactor
```

2. **安装新依赖**（如果需要）
```bash
pip install pydantic-settings
pip install psutil  # 用于内存监控
```

### 第二步：迁移路由文件

#### 2.1 识别需要迁移的路由文件
查找 `app/routers/` 目录下的所有路由文件，优先迁移：
- 业务逻辑复杂的路由
- 异常处理较多的路由
- 数据库操作频繁的路由

#### 2.2 创建对应的控制器

**原有路由文件示例：**
```python
# app/routers/example/old_route.py
@router.post("/items/")
async def create_item(item_data: dict, db: AsyncSession = Depends(get_db)):
    try:
        # 复杂的业务逻辑
        result = await some_complex_logic(item_data, db)
        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"创建失败: {e}")
        raise HTTPException(status_code=500, detail="创建失败")
```

**新的控制器：**
```python
# app/controllers/example/item.py
from app.controllers.base import CRUDController
from app.services.example.item import ItemService
from app.schemas.example.item import ItemCreate, ItemResponse

class ItemController(CRUDController[ItemService, ItemCreate, ItemResponse]):
    def __init__(self):
        super().__init__(ItemService)
    
    async def create_item(self, item_data: ItemCreate) -> APIResponse[ItemResponse]:
        """创建项目"""
        try:
            result = await self.service.create(item_data)
            return APIResponse.success(result, "创建成功")
        except BusinessException as e:
            return APIResponse.error(f"创建失败: {e.detail}")
```

**新的路由文件：**
```python
# app/routers/example/item.py
from fastapi import APIRouter, Depends
from app.controllers.example.item import ItemController
from app.schemas.example.item import ItemCreate

router = APIRouter(prefix="/items", tags=["items"])
controller = ItemController()

@router.post("/")
async def create_item(item_data: ItemCreate):
    return await controller.create_item(item_data)
```

### 第三步：创建服务层

#### 3.1 分析现有业务逻辑
识别路由中的业务逻辑，提取到服务层：

```python
# app/services/example/item.py
from app.services.base import BaseService
from app.repositories.example.item import ItemRepository
from app.schemas.example.item import ItemCreate, ItemUpdate
from app.models.example.item import Item

class ItemService(BaseService[Item, ItemCreate, ItemUpdate]):
    def __init__(self):
        super().__init__(ItemRepository())
    
    async def create_with_validation(self, item_data: ItemCreate) -> Item:
        """带验证的创建方法"""
        # 业务验证逻辑
        if await self.repository.exists_by_name(item_data.name):
            raise BusinessException("项目名称已存在")
        
        # 创建项目
        return await self.repository.create(item_data)
```

#### 3.2 使用装饰器简化异常处理

```python
from app.utils.exception_decorators import handle_business_errors

class ItemService(BaseService):
    @handle_business_errors
    async def complex_operation(self, data):
        # 复杂业务逻辑，异常会被自动处理
        pass
```

### 第四步：创建仓储层

#### 4.1 继承基础仓储类

```python
# app/repositories/example/item.py
from app.repositories.base import BaseRepository
from app.models.example.item import Item
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

class ItemRepository(BaseRepository[Item]):
    def __init__(self):
        super().__init__(Item)
    
    async def find_by_name(self, name: str) -> Optional[Item]:
        """根据名称查找"""
        async with self.get_session() as session:
            result = await session.execute(
                select(Item).where(Item.name == name)
            )
            return result.scalar_one_or_none()
    
    async def exists_by_name(self, name: str) -> bool:
        """检查名称是否存在"""
        item = await self.find_by_name(name)
        return item is not None
```

### 第五步：更新配置使用

#### 5.1 迁移配置访问

**旧方式：**
```python
from app.config import get_settings
settings = get_settings()
database_url = f"mysql://user:pass@host:port/db"
```

**新方式：**
```python
from app.core.config import get_settings
settings = get_settings()
database_url = settings.database.get_database_url()
```

### 第六步：添加监控和日志

#### 6.1 使用性能监控装饰器

```python
from app.utils.monitoring import timing_decorator, performance_context

class ItemService:
    @timing_decorator(metric_name="item.create", slow_threshold=1.0)
    async def create_item(self, data):
        # 自动记录执行时间和慢查询
        pass
    
    async def complex_operation(self):
        async with performance_context("item.complex_operation"):
            # 自动记录操作指标
            pass
```

#### 6.2 使用结构化日志

```python
from app.utils.logger import get_logger, set_request_context

logger = get_logger("item_service")

async def process_request(request_id: str):
    # 设置请求上下文
    set_request_context(request_id=request_id, operation="process_item")
    
    # 日志会自动包含上下文信息
    logger.info("开始处理请求")
```

## 📋 迁移检查清单

### 路由层迁移
- [ ] 创建对应的控制器类
- [ ] 简化路由处理函数
- [ ] 移除路由中的业务逻辑
- [ ] 统一错误响应格式

### 服务层迁移
- [ ] 提取业务逻辑到服务类
- [ ] 使用依赖注入
- [ ] 添加业务验证
- [ ] 使用异常处理装饰器

### 仓储层迁移
- [ ] 继承BaseRepository
- [ ] 实现专门的查询方法
- [ ] 使用事务装饰器
- [ ] 添加数据库异常处理

### 配置迁移
- [ ] 更新配置导入路径
- [ ] 使用新的配置结构
- [ ] 添加环境特定配置

### 监控和日志
- [ ] 添加性能监控装饰器
- [ ] 使用结构化日志
- [ ] 设置请求上下文
- [ ] 配置日志级别

## 🔧 迁移工具和脚本

### 自动化重构脚本

创建一个简单的重构脚本：

```python
# scripts/refactor_helper.py
import os
import re
from pathlib import Path

def find_routes_to_migrate():
    """查找需要迁移的路由文件"""
    routes_dir = Path("app/routers")
    for file_path in routes_dir.rglob("*.py"):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 查找复杂的路由函数
            if len(re.findall(r'async def \w+', content)) > 3:
                print(f"需要迁移: {file_path}")

def check_exception_handling():
    """检查异常处理模式"""
    for file_path in Path("app").rglob("*.py"):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 查找旧的异常处理模式
            if "except Exception as e:" in content:
                print(f"需要更新异常处理: {file_path}")

if __name__ == "__main__":
    print("=== 查找需要迁移的路由 ===")
    find_routes_to_migrate()
    print("\n=== 检查异常处理 ===")
    check_exception_handling()
```

### 代码质量检查

```python
# scripts/quality_check.py
from app.utils.code_quality import quality_checker
import importlib

def check_module_quality(module_name: str):
    """检查模块代码质量"""
    try:
        module = importlib.import_module(module_name)
        issues = quality_checker.check_module(module)
        
        if issues:
            print(f"\n=== {module_name} 质量问题 ===")
            for issue in issues:
                print(f"- {issue.severity.upper()}: {issue.message}")
                if issue.suggestion:
                    print(f"  建议: {issue.suggestion}")
        else:
            print(f"✅ {module_name} 质量检查通过")
            
    except ImportError:
        print(f"❌ 无法导入模块: {module_name}")

# 使用示例
if __name__ == "__main__":
    modules_to_check = [
        "app.routers.system.user",
        "app.services.system.user",
        # 添加更多模块
    ]
    
    for module in modules_to_check:
        check_module_quality(module)
```

## 🎯 迁移优先级建议

### 高优先级（立即迁移）
1. **用户认证相关模块** - 安全性关键
2. **核心业务逻辑模块** - 使用频率高
3. **异常处理复杂的模块** - 受益最大

### 中优先级（逐步迁移）
1. **数据查询密集的模块** - 性能监控受益
2. **配置依赖较多的模块** - 配置管理受益
3. **日志记录重要的模块** - 结构化日志受益

### 低优先级（最后迁移）
1. **简单的CRUD操作** - 改动较小
2. **工具类函数** - 影响范围小
3. **测试相关代码** - 非核心功能

## ⚠️ 注意事项

1. **向后兼容性**：确保迁移过程中API接口保持兼容
2. **渐进式迁移**：一次迁移一个模块，避免大规模改动
3. **测试验证**：每次迁移后进行充分测试
4. **性能监控**：使用新的监控工具观察性能变化
5. **文档更新**：及时更新API文档和开发文档

## 🔍 故障排除

### 常见问题

1. **导入错误**：检查新的导入路径是否正确
2. **配置问题**：确认环境变量和配置文件设置
3. **数据库连接**：验证新的数据库配置
4. **异常处理**：确认异常类型匹配

### 调试技巧

1. 使用新的结构化日志查看详细信息
2. 利用性能监控定位性能问题
3. 检查异常处理中间件的日志输出
4. 使用代码质量检查工具发现问题

## 📚 实战示例：完整迁移一个模块

### 示例：迁移数据比较模块

假设我们要迁移 `app/routers/steel_making/erp/data_comparison.py` 模块：

#### 步骤1：分析现有代码结构
```python
# 原有路由文件分析
@router.post("/compare")
async def compare_data(request: dict, db: AsyncSession = Depends(get_db)):
    try:
        # 复杂的数据比较逻辑
        mes_data = await get_mes_data(request['date'])
        erp_data = await get_erp_data(request['date'])
        differences = compare_datasets(mes_data, erp_data)

        # 保存比较结果
        result = await save_comparison_result(db, differences)
        return {"status": "success", "data": result}
    except Exception as e:
        logger.error(f"数据比较失败: {e}")
        raise HTTPException(status_code=500, detail="比较失败")
```

#### 步骤2：创建数据模型和Schema
```python
# app/schemas/steel_making/data_comparison.py
from pydantic import BaseModel
from datetime import date
from typing import List, Optional

class DataComparisonRequest(BaseModel):
    date: date
    comparison_type: str = "daily"
    include_details: bool = True

class ComparisonDifference(BaseModel):
    field_name: str
    mes_value: Optional[str]
    erp_value: Optional[str]
    difference_type: str  # missing, mismatch, extra

class DataComparisonResponse(BaseModel):
    comparison_id: str
    date: date
    total_differences: int
    differences: List[ComparisonDifference]
    comparison_time: datetime
```

#### 步骤3：创建仓储层
```python
# app/repositories/steel_making/data_comparison.py
from app.repositories.base import BaseRepository
from app.models.steel_making.data_comparison import DataComparison
from app.utils.database import handle_database_exceptions
from sqlalchemy import select, and_
from datetime import date

class DataComparisonRepository(BaseRepository[DataComparison]):
    def __init__(self):
        super().__init__(DataComparison)

    @handle_database_exceptions("查询MES数据")
    async def get_mes_data(self, comparison_date: date) -> List[dict]:
        """获取MES数据"""
        async with self.get_session() as session:
            # 复杂的MES数据查询逻辑
            result = await session.execute(
                select(MESTable).where(MESTable.date == comparison_date)
            )
            return [row._asdict() for row in result.fetchall()]

    @handle_database_exceptions("查询ERP数据")
    async def get_erp_data(self, comparison_date: date) -> List[dict]:
        """获取ERP数据"""
        async with self.get_session() as session:
            # 复杂的ERP数据查询逻辑
            result = await session.execute(
                select(ERPTable).where(ERPTable.date == comparison_date)
            )
            return [row._asdict() for row in result.fetchall()]
```

#### 步骤4：创建服务层
```python
# app/services/steel_making/data_comparison.py
from app.services.base import BaseService
from app.repositories.steel_making.data_comparison import DataComparisonRepository
from app.schemas.steel_making.data_comparison import *
from app.utils.monitoring import timing_decorator
from app.utils.exception_decorators import handle_business_errors
from app.exceptions import BusinessException

class DataComparisonService(BaseService):
    def __init__(self):
        self.repository = DataComparisonRepository()

    @timing_decorator(metric_name="data_comparison.compare", slow_threshold=5.0)
    @handle_business_errors
    async def compare_data(self, request: DataComparisonRequest) -> DataComparisonResponse:
        """执行数据比较"""
        # 获取数据
        mes_data = await self.repository.get_mes_data(request.date)
        erp_data = await self.repository.get_erp_data(request.date)

        if not mes_data and not erp_data:
            raise BusinessException(f"未找到 {request.date} 的数据")

        # 执行比较
        differences = await self._compare_datasets(mes_data, erp_data)

        # 保存结果
        comparison_result = await self._save_comparison_result(
            request.date, differences
        )

        return DataComparisonResponse(
            comparison_id=comparison_result.id,
            date=request.date,
            total_differences=len(differences),
            differences=differences,
            comparison_time=comparison_result.created_at
        )

    async def _compare_datasets(self, mes_data: List[dict], erp_data: List[dict]) -> List[ComparisonDifference]:
        """比较数据集"""
        differences = []
        # 复杂的比较逻辑
        # ...
        return differences
```

#### 步骤5：创建控制器
```python
# app/controllers/steel_making/data_comparison.py
from app.controllers.base import BaseController
from app.services.steel_making.data_comparison import DataComparisonService
from app.schemas.steel_making.data_comparison import *
from app.utils.types import APIResponse

class DataComparisonController(BaseController):
    def __init__(self):
        super().__init__()
        self.service = DataComparisonService()

    async def compare_data(self, request: DataComparisonRequest) -> APIResponse[DataComparisonResponse]:
        """数据比较接口"""
        try:
            result = await self.service.compare_data(request)
            return APIResponse.success(result, "数据比较完成")
        except BusinessException as e:
            return APIResponse.error(f"比较失败: {e.detail}")
```

#### 步骤6：更新路由
```python
# app/routers/steel_making/erp/data_comparison.py
from fastapi import APIRouter
from app.controllers.steel_making.data_comparison import DataComparisonController
from app.schemas.steel_making.data_comparison import DataComparisonRequest

router = APIRouter(prefix="/data-comparison", tags=["data-comparison"])
controller = DataComparisonController()

@router.post("/compare")
async def compare_data(request: DataComparisonRequest):
    """数据比较"""
    return await controller.compare_data(request)

@router.get("/history")
async def get_comparison_history(page: int = 1, size: int = 20):
    """比较历史"""
    return await controller.get_history(page, size)
```

## 🧪 测试策略

### 单元测试示例
```python
# tests/test_data_comparison_service.py
import pytest
from app.services.steel_making.data_comparison import DataComparisonService
from app.schemas.steel_making.data_comparison import DataComparisonRequest

@pytest.fixture
def service():
    return DataComparisonService()

@pytest.mark.asyncio
async def test_compare_data_success(service):
    """测试数据比较成功场景"""
    request = DataComparisonRequest(
        date="2024-01-01",
        comparison_type="daily"
    )

    result = await service.compare_data(request)

    assert result.comparison_id is not None
    assert result.date == request.date
    assert isinstance(result.total_differences, int)

@pytest.mark.asyncio
async def test_compare_data_no_data(service):
    """测试无数据场景"""
    request = DataComparisonRequest(date="2099-01-01")

    with pytest.raises(BusinessException):
        await service.compare_data(request)
```

### 集成测试示例
```python
# tests/test_data_comparison_integration.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_compare_data_endpoint():
    """测试数据比较接口"""
    response = client.post("/data-comparison/compare", json={
        "date": "2024-01-01",
        "comparison_type": "daily"
    })

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "comparison_id" in data["data"]
```

## 📊 性能优化建议

### 数据库查询优化
```python
# 使用批量查询减少数据库往返
async def get_batch_data(self, dates: List[date]) -> Dict[date, List[dict]]:
    """批量获取多日期数据"""
    async with self.get_session() as session:
        result = await session.execute(
            select(DataTable).where(DataTable.date.in_(dates))
        )

        # 按日期分组
        data_by_date = {}
        for row in result.fetchall():
            date_key = row.date
            if date_key not in data_by_date:
                data_by_date[date_key] = []
            data_by_date[date_key].append(row._asdict())

        return data_by_date
```

### 缓存策略
```python
from functools import lru_cache
from app.utils.monitoring import timing_decorator

class DataComparisonService:
    @lru_cache(maxsize=100)
    @timing_decorator(metric_name="data_comparison.get_cached_data")
    async def get_cached_comparison_result(self, date: str) -> Optional[dict]:
        """获取缓存的比较结果"""
        # 实现缓存逻辑
        pass
```

## 🔄 持续改进

### 代码审查检查点
- [ ] 是否正确使用了新的异常处理模式？
- [ ] 是否添加了适当的性能监控？
- [ ] 是否遵循了分层架构原则？
- [ ] 是否添加了必要的类型提示？
- [ ] 是否编写了相应的测试？

### 监控指标
```python
# 在服务中添加业务指标
from app.utils.monitoring import metrics_collector

class DataComparisonService:
    async def compare_data(self, request):
        # 记录业务指标
        metrics_collector.record_metric(
            "business.data_comparison.request",
            1,
            {"comparison_type": request.comparison_type}
        )

        result = await self._do_comparison(request)

        # 记录结果指标
        metrics_collector.record_metric(
            "business.data_comparison.differences",
            len(result.differences),
            {"date": str(request.date)}
        )

        return result
```

### 日志最佳实践
```python
from app.utils.logger import get_logger, set_request_context

logger = get_logger("data_comparison")

class DataComparisonService:
    async def compare_data(self, request):
        # 设置请求上下文
        set_request_context(
            operation="data_comparison",
            date=str(request.date),
            comparison_type=request.comparison_type
        )

        logger.info("开始数据比较", extra={
            "request_params": request.dict()
        })

        try:
            result = await self._do_comparison(request)
            logger.info("数据比较完成", extra={
                "differences_count": len(result.differences)
            })
            return result
        except Exception as e:
            logger.error("数据比较失败", extra={
                "error_type": type(e).__name__,
                "error_message": str(e)
            })
            raise
```

---

通过遵循这个详细的指南和实战示例，您可以系统性地将现有代码迁移到新的架构模式。建议从一个相对简单的模块开始实践，熟悉整个流程后再处理更复杂的模块。记住，重构是一个渐进的过程，每一步都要确保系统的稳定性和功能的完整性。
