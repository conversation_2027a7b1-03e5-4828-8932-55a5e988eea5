
from pydantic_settings import BaseSettings
from typing import Dict, Any, Optional
import os
from functools import lru_cache


class Settings(BaseSettings):
    """应用基础配置设置"""
    APP_NAME: str = "STA Keeper Backend"
    SECRET_KEY: str = "X7zQ2aP8vY3kL1wN9cB4rT6mJ0oG5sHdKfUxIeWqVlOyA_"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # 数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "root"
    DB_PASSWORD: str = ""
    DB_NAME: str = "sta_keeper"

    # Oracle数据库配置
    ERP_ORACLE_CONFIG: Dict[str, Any] = {
        "HOST": "************",
        "PORT": 1521,
        "SERVICE": "jgdb",
        "USER": "ERP_FB01",
        "PASSWORD": "erp_fb01"
    }

    MES_ORACLE_CONFIG: Dict[str, Any] = {
        "HOST": "************",
        "PORT": 1521,
        "SERVICE": "jgdb",
        "USER": "imes",
        "PASSWORD": "sa"
    }

    MES_MSG_ORACLE_CONFIG: Dict[str, Any] = {
        "HOST": "************",
        "PORT": 1521,
        "SERVICE": "jgdb",
        "USER": "INTERFACE_ERP_2",
        "PASSWORD": "INTERFACE_ERP_2"
    }

    # 日志配置
    LOG_TO_FILE: bool = False
    LOG_FILE: str = "app.log"
    LOG_LEVEL: str = "INFO"

    # 环境配置
    DEBUG: bool = False
    PRO: bool = False

    # ERP Web系统配置
    ERP_WEB_USERNAME: str = "J039760"
    ERP_WEB_PASSWORD: str = "2012qwer"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


class DevSettings(Settings):
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"


class ProSettings(Settings):
    PRO: bool = True
    LOG_TO_FILE: bool = True


@lru_cache()
def get_settings():
    """获取配置单例"""
    env = os.getenv("APP_ENV", "dev").lower()
    if env == "pro":
        return ProSettings()
    else:
        return DevSettings()