

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置设置"""
    APP_NAME: str = "STA Keeper Backend"
    SECRET_KEY: str = "X7zQ2aP8vY3kL1wN9cB4rT6mJ0oG5sHdKfUxIeWqVlOyA_"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Oracle数据库配置
    ERP_ORACLE_HOST: str = "************"
    ERP_ORACLE_PORT: int = 1521
    ERP_ORACLE_SERVICE: str = "jgdb"
    ERP_ORACLE_USER: str = "ERP_FB01"
    ERP_ORACLE_PASSWORD: str = "erp_fb01"

    MES_ORACLE_HOST: str = "************"
    MES_ORACLE_PORT: int = 1521
    MES_ORACLE_SERVICE: str = "jgdb"
    MES_ORACLE_USER: str = "imes"
    MES_ORACLE_PASSWORD: str = "sa"

    MES_MSG_ORACLE_HOST: str = "************"
    MES_MSG_ORACLE_PORT: int = 1521
    MES_MSG_ORACLE_SERVICE: str = "jgdb"
    MES_MSG_ORACLE_USER: str = "INTERFACE_ERP_2"
    MES_MSG_ORACLE_PASSWORD: str = "INTERFACE_ERP_2"


    # ERP Web系统配置
    ERP_WEB_USERNAME: str = "J039760"
    ERP_WEB_PASSWORD: str = "2012qwer"

settings = Settings()