"""系统权限定义"""

PERMISSIONS = {
    "USER": {
        "VIEW": "user:view",
        "CREATE": "user:create",
        "UPDATE": "user:update",
        "DELETE": "user:delete"
    },
    "ROLE": {
        "VIEW": "role:view",
        "CREATE": "role:create",
        "UPDATE": "role:update",
        "DELETE": "role:delete"
    },
    "LOG": {
        "VIEW": "log:view"
    },
    # TODO 补充炼钢维护、炼钢维护历史下的权限，防止所有人都能执行
}

def get_all_permissions():
    """获取所有权限列表"""
    permissions = []
    for category in PERMISSIONS.values():
        permissions.extend(category.values())
    return permissions