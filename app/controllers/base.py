"""
基础控制器类
提供统一的请求响应处理和通用功能
"""

from abc import ABC
from typing import Any, Dict, Optional, List
from fastapi import Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.database.session import get_db
from app.services.service_factory import get_service_factory, ServiceFactory
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.utils.response import success, fail
from app.utils.logger import logger
from app.exceptions import ValidationException


class PaginationParams(BaseModel):
    """分页参数模型"""
    page: int = 1
    page_size: int = 20
    
    def __init__(self, **data):
        super().__init__(**data)
        if self.page < 1:
            self.page = 1
        if self.page_size < 1:
            self.page_size = 20
        if self.page_size > 100:
            self.page_size = 100


class SearchParams(BaseModel):
    """搜索参数模型"""
    keyword: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None
    order_by: Optional[str] = None
    order_desc: bool = False


class BaseController(ABC):
    """基础控制器类"""
    
    def __init__(
        self,
        request: Request,
        db: AsyncSession = Depends(get_db),
        current_user: Optional[User] = Depends(get_current_user)
    ):
        self.request = request
        self.db = db
        self.current_user = current_user
        self.service_factory = get_service_factory(db)
        
        # 记录请求信息
        self._log_request_info()
    
    def _log_request_info(self):
        """记录请求信息"""
        logger.info(
            f"API请求: {self.request.method} {self.request.url.path}",
            extra={
                "method": self.request.method,
                "path": self.request.url.path,
                "query_params": dict(self.request.query_params),
                "user_id": self.current_user.id if self.current_user else None,
                "user_name": self.current_user.username if self.current_user else None,
                "client_ip": self.request.client.host if self.request.client else None
            }
        )
    
    def success_response(
        self, 
        data: Any = None, 
        msg: str = "操作成功",
        extra: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """成功响应"""
        response = success(data=data, msg=msg)
        if extra:
            response.update(extra)
        return response
    
    def fail_response(
        self, 
        msg: str = "操作失败", 
        data: Any = None,
        extra: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """失败响应"""
        response = fail(msg=msg, data=data)
        if extra:
            response.update(extra)
        return response
    
    def paginated_response(
        self,
        items: List[Any],
        total: int,
        page: int,
        page_size: int,
        msg: str = "查询成功"
    ) -> Dict[str, Any]:
        """分页响应"""
        total_pages = (total + page_size - 1) // page_size
        
        return self.success_response(
            data={
                "items": items,
                "pagination": {
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            },
            msg=msg
        )
    
    def validate_pagination_params(self, params: PaginationParams) -> PaginationParams:
        """验证分页参数"""
        if params.page < 1:
            raise ValidationException("页码必须大于0")
        
        if params.page_size < 1:
            raise ValidationException("每页大小必须大于0")
        
        if params.page_size > 100:
            raise ValidationException("每页大小不能超过100")
        
        return params
    
    def validate_search_params(self, params: SearchParams) -> SearchParams:
        """验证搜索参数"""
        if params.keyword is not None:
            params.keyword = params.keyword.strip()
            if len(params.keyword) > 100:
                raise ValidationException("搜索关键词长度不能超过100字符")
        
        return params
    
    async def log_operation(
        self,
        operation_type: str,
        operation_content: str,
        result: str = "success",
        error_message: Optional[str] = None
    ):
        """记录操作日志"""
        try:
            from app.models.system.operation_log import OperationLog
            from datetime import datetime
            
            log = OperationLog(
                operation_type=operation_type,
                operation_content=operation_content,
                operator_id=self.current_user.id if self.current_user else None,
                operator_name=self.current_user.username if self.current_user else "系统",
                ip_address=self.request.client.host if self.request.client else None,
                user_agent=self.request.headers.get("user-agent"),
                status=result,
                error_message=error_message,
                created_at=datetime.now()
            )
            
            self.db.add(log)
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"记录操作日志失败: {str(e)}")
    
    def get_client_info(self) -> Dict[str, Any]:
        """获取客户端信息"""
        return {
            "ip": self.request.client.host if self.request.client else None,
            "user_agent": self.request.headers.get("user-agent"),
            "referer": self.request.headers.get("referer"),
            "accept_language": self.request.headers.get("accept-language")
        }
    
    def require_permission(self, permission: str):
        """检查权限"""
        if not self.current_user:
            from app.exceptions import PermissionDeniedException
            raise PermissionDeniedException("需要登录")
        
        # 这里可以添加具体的权限检查逻辑
        # 例如检查用户角色和权限
        pass


class CRUDController(BaseController):
    """CRUD控制器基类"""
    
    async def create_item(self, item_data: BaseModel) -> Dict[str, Any]:
        """创建项目的通用方法"""
        raise NotImplementedError("子类必须实现create_item方法")
    
    async def get_item(self, item_id: int) -> Dict[str, Any]:
        """获取单个项目的通用方法"""
        raise NotImplementedError("子类必须实现get_item方法")
    
    async def update_item(self, item_id: int, item_data: BaseModel) -> Dict[str, Any]:
        """更新项目的通用方法"""
        raise NotImplementedError("子类必须实现update_item方法")
    
    async def delete_item(self, item_id: int) -> Dict[str, Any]:
        """删除项目的通用方法"""
        raise NotImplementedError("子类必须实现delete_item方法")
    
    async def list_items(
        self, 
        pagination: PaginationParams,
        search: Optional[SearchParams] = None
    ) -> Dict[str, Any]:
        """列出项目的通用方法"""
        raise NotImplementedError("子类必须实现list_items方法")


class AsyncTaskController(BaseController):
    """异步任务控制器基类"""
    
    async def start_task(self, task_params: BaseModel) -> Dict[str, Any]:
        """启动异步任务"""
        raise NotImplementedError("子类必须实现start_task方法")
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        raise NotImplementedError("子类必须实现get_task_status方法")
    
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务"""
        raise NotImplementedError("子类必须实现cancel_task方法")
    
    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果"""
        raise NotImplementedError("子类必须实现get_task_result方法")
