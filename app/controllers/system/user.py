"""
用户控制器
处理用户相关的HTTP请求
"""

from typing import Optional, List
from fastapi import Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.controllers.base import CRUDController, PaginationParams, SearchParams
from app.database.session import get_db
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.exceptions import ValidationException


class UserCreateRequest(BaseModel):
    """用户创建请求模型"""
    username: str
    password: str
    nickname: Optional[str] = None
    remark: Optional[str] = None


class UserUpdateRequest(BaseModel):
    """用户更新请求模型"""
    nickname: Optional[str] = None
    remark: Optional[str] = None
    status: Optional[int] = None


class UserResponse(BaseModel):
    """用户响应模型"""
    id: int
    username: str
    nickname: Optional[str]
    remark: Optional[str]
    status: int
    created_at: str
    updated_at: str


class UserController(CRUDController):
    """用户控制器"""
    
    def __init__(
        self,
        request: Request,
        db: AsyncSession = Depends(get_db),
        current_user: Optional[User] = Depends(get_current_user)
    ):
        super().__init__(request, db, current_user)
        self.user_service = self.service_factory.get_user_service()
    
    async def create_item(self, item_data: UserCreateRequest) -> dict:
        """创建用户"""
        # 权限检查
        self.require_permission("user:create")
        
        # 创建用户
        user = await self.user_service.create_user(
            username=item_data.username,
            password=item_data.password,
            nickname=item_data.nickname,
            remark=item_data.remark
        )
        
        # 记录操作日志
        await self.log_operation(
            operation_type="用户管理",
            operation_content=f"创建用户: {item_data.username}"
        )
        
        return self.success_response(
            data=self._format_user_response(user),
            msg="用户创建成功"
        )
    
    async def get_item(self, item_id: int) -> dict:
        """获取用户详情"""
        # 权限检查
        self.require_permission("user:read")
        
        # 获取用户
        user = await self.user_service.get_user_by_id(item_id)
        
        return self.success_response(
            data=self._format_user_response(user),
            msg="获取用户成功"
        )
    
    async def update_item(self, item_id: int, item_data: UserUpdateRequest) -> dict:
        """更新用户"""
        # 权限检查
        self.require_permission("user:update")
        
        # 更新用户
        user = await self.user_service.update_user(
            user_id=item_id,
            nickname=item_data.nickname,
            remark=item_data.remark,
            status=item_data.status
        )
        
        # 记录操作日志
        await self.log_operation(
            operation_type="用户管理",
            operation_content=f"更新用户: {user.username}"
        )
        
        return self.success_response(
            data=self._format_user_response(user),
            msg="用户更新成功"
        )
    
    async def delete_item(self, item_id: int) -> dict:
        """删除用户"""
        # 权限检查
        self.require_permission("user:delete")
        
        # 获取用户信息用于日志
        user = await self.user_service.get_user_by_id(item_id)
        username = user.username
        
        # 删除用户
        await self.user_service.delete_user(item_id)
        
        # 记录操作日志
        await self.log_operation(
            operation_type="用户管理",
            operation_content=f"删除用户: {username}"
        )
        
        return self.success_response(msg="用户删除成功")
    
    async def list_items(
        self,
        pagination: PaginationParams,
        search: Optional[SearchParams] = None
    ) -> dict:
        """获取用户列表"""
        # 权限检查
        self.require_permission("user:list")
        
        # 验证参数
        pagination = self.validate_pagination_params(pagination)
        if search:
            search = self.validate_search_params(search)
        
        # 获取用户列表
        if search and search.keyword:
            users = await self.user_service.search_users(
                keyword=search.keyword,
                skip=(pagination.page - 1) * pagination.page_size,
                limit=pagination.page_size
            )
            total = await self.user_service.count_search_users(search.keyword)
        else:
            users = await self.user_service.get_user_list(
                skip=(pagination.page - 1) * pagination.page_size,
                limit=pagination.page_size
            )
            total = await self.user_service.count_users()
        
        # 格式化响应数据
        user_list = [self._format_user_response(user) for user in users]
        
        return self.paginated_response(
            items=user_list,
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            msg="获取用户列表成功"
        )
    
    async def get_user_statistics(self) -> dict:
        """获取用户统计信息"""
        # 权限检查
        self.require_permission("user:statistics")
        
        # 获取统计信息
        stats = await self.user_service.get_user_statistics()
        
        return self.success_response(
            data=stats,
            msg="获取用户统计成功"
        )
    
    async def batch_update_status(
        self,
        user_ids: List[int],
        status: int
    ) -> dict:
        """批量更新用户状态"""
        # 权限检查
        self.require_permission("user:batch_update")
        
        # 验证参数
        if not user_ids:
            raise ValidationException("用户ID列表不能为空")
        
        if status not in [0, 1]:
            raise ValidationException("状态值必须是0或1")
        
        # 批量更新
        updated_count = await self.user_service.batch_update_status(user_ids, status)
        
        # 记录操作日志
        await self.log_operation(
            operation_type="用户管理",
            operation_content=f"批量更新用户状态: {len(user_ids)}个用户，状态={status}"
        )
        
        return self.success_response(
            data={"updated_count": updated_count},
            msg=f"成功更新{updated_count}个用户状态"
        )
    
    def _format_user_response(self, user: User) -> dict:
        """格式化用户响应数据"""
        return {
            "id": user.id,
            "username": user.username,
            "nickname": user.nickname,
            "remark": user.remark,
            "status": user.status,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None
        }


def get_user_controller(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user)
) -> UserController:
    """获取用户控制器实例"""
    return UserController(request, db, current_user)
