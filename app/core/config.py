"""
应用配置管理
统一管理所有配置项，支持环境变量和配置文件
"""

import os
from typing import Optional, List, Dict, Any
from pydantic import BaseSettings, Field, validator
from functools import lru_cache
from pathlib import Path


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    
    # Oracle数据库配置
    oracle_host: str = Field(default="localhost", env="ORACLE_HOST")
    oracle_port: int = Field(default=1521, env="ORACLE_PORT")
    oracle_service_name: str = Field(default="XE", env="ORACLE_SERVICE_NAME")
    oracle_username: str = Field(default="system", env="ORACLE_USERNAME")
    oracle_password: str = Field(default="password", env="ORACLE_PASSWORD")
    oracle_pool_size: int = Field(default=10, env="ORACLE_POOL_SIZE")
    oracle_max_overflow: int = Field(default=20, env="ORACLE_MAX_OVERFLOW")
    oracle_pool_timeout: int = Field(default=30, env="ORACLE_POOL_TIMEOUT")
    oracle_pool_recycle: int = Field(default=3600, env="ORACLE_POOL_RECYCLE")
    
    # PostgreSQL配置（如果需要）
    postgres_host: str = Field(default="localhost", env="POSTGRES_HOST")
    postgres_port: int = Field(default=5432, env="POSTGRES_PORT")
    postgres_db: str = Field(default="sta_keeper", env="POSTGRES_DB")
    postgres_username: str = Field(default="postgres", env="POSTGRES_USERNAME")
    postgres_password: str = Field(default="password", env="POSTGRES_PASSWORD")
    
    # 数据库连接配置
    db_echo: bool = Field(default=False, env="DB_ECHO")
    db_pool_pre_ping: bool = Field(default=True, env="DB_POOL_PRE_PING")
    
    @property
    def oracle_url(self) -> str:
        """Oracle数据库连接URL"""
        return f"oracle+oracledb://{self.oracle_username}:{self.oracle_password}@{self.oracle_host}:{self.oracle_port}/?service_name={self.oracle_service_name}"
    
    @property
    def postgres_url(self) -> str:
        """PostgreSQL数据库连接URL"""
        return f"postgresql+asyncpg://{self.postgres_username}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    class Config:
        env_prefix = "DB_"
        case_sensitive = False


class RedisConfig(BaseSettings):
    """Redis配置"""
    
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    db: int = Field(default=0, env="REDIS_DB")
    max_connections: int = Field(default=10, env="REDIS_MAX_CONNECTIONS")
    socket_timeout: int = Field(default=5, env="REDIS_SOCKET_TIMEOUT")
    socket_connect_timeout: int = Field(default=5, env="REDIS_SOCKET_CONNECT_TIMEOUT")
    
    @property
    def url(self) -> str:
        """Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"
    
    class Config:
        env_prefix = "REDIS_"
        case_sensitive = False


class SecurityConfig(BaseSettings):
    """安全配置"""
    
    secret_key: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    
    # 密码配置
    password_min_length: int = Field(default=6, env="PASSWORD_MIN_LENGTH")
    password_max_length: int = Field(default=128, env="PASSWORD_MAX_LENGTH")
    password_require_uppercase: bool = Field(default=False, env="PASSWORD_REQUIRE_UPPERCASE")
    password_require_lowercase: bool = Field(default=False, env="PASSWORD_REQUIRE_LOWERCASE")
    password_require_numbers: bool = Field(default=False, env="PASSWORD_REQUIRE_NUMBERS")
    password_require_special: bool = Field(default=False, env="PASSWORD_REQUIRE_SPECIAL")
    
    # CORS配置
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    cors_methods: List[str] = Field(default=["*"], env="CORS_METHODS")
    cors_headers: List[str] = Field(default=["*"], env="CORS_HEADERS")
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v
    
    @validator('cors_methods', pre=True)
    def parse_cors_methods(cls, v):
        if isinstance(v, str):
            return [method.strip() for method in v.split(',')]
        return v
    
    @validator('cors_headers', pre=True)
    def parse_cors_headers(cls, v):
        if isinstance(v, str):
            return [header.strip() for header in v.split(',')]
        return v
    
    class Config:
        env_prefix = "SECURITY_"
        case_sensitive = False


class LoggingConfig(BaseSettings):
    """日志配置"""
    
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    file_path: Optional[str] = Field(default=None, env="LOG_FILE_PATH")
    max_file_size: int = Field(default=10 * 1024 * 1024, env="LOG_MAX_FILE_SIZE")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    
    # 结构化日志配置
    enable_json_logs: bool = Field(default=False, env="LOG_ENABLE_JSON")
    enable_request_logging: bool = Field(default=True, env="LOG_ENABLE_REQUEST")
    enable_sql_logging: bool = Field(default=False, env="LOG_ENABLE_SQL")
    
    class Config:
        env_prefix = "LOG_"
        case_sensitive = False


class AppConfig(BaseSettings):
    """应用配置"""
    
    # 基本信息
    name: str = Field(default="STA Keeper Backend", env="APP_NAME")
    version: str = Field(default="1.0.0", env="APP_VERSION")
    description: str = Field(default="钢铁生产数据管理系统后端", env="APP_DESCRIPTION")
    
    # 运行配置
    host: str = Field(default="0.0.0.0", env="APP_HOST")
    port: int = Field(default=8000, env="APP_PORT")
    debug: bool = Field(default=False, env="APP_DEBUG")
    reload: bool = Field(default=False, env="APP_RELOAD")
    
    # 环境配置
    environment: str = Field(default="development", env="APP_ENVIRONMENT")
    
    # API配置
    api_prefix: str = Field(default="/api", env="API_PREFIX")
    docs_url: Optional[str] = Field(default="/docs", env="DOCS_URL")
    redoc_url: Optional[str] = Field(default="/redoc", env="REDOC_URL")
    openapi_url: Optional[str] = Field(default="/openapi.json", env="OPENAPI_URL")
    
    # 业务配置
    max_upload_size: int = Field(default=10 * 1024 * 1024, env="MAX_UPLOAD_SIZE")  # 10MB
    allowed_file_types: List[str] = Field(default=[".xlsx", ".xls", ".csv"], env="ALLOWED_FILE_TYPES")
    
    # 任务调度配置
    enable_scheduler: bool = Field(default=True, env="ENABLE_SCHEDULER")
    scheduler_timezone: str = Field(default="Asia/Shanghai", env="SCHEDULER_TIMEZONE")
    
    @validator('allowed_file_types', pre=True)
    def parse_allowed_file_types(cls, v):
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(',')]
        return v
    
    @property
    def is_development(self) -> bool:
        return self.environment.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        return self.environment.lower() == "production"
    
    @property
    def is_testing(self) -> bool:
        return self.environment.lower() == "testing"
    
    class Config:
        env_prefix = "APP_"
        case_sensitive = False


class Settings(BaseSettings):
    """主配置类"""
    
    # 子配置
    app: AppConfig = AppConfig()
    database: DatabaseConfig = DatabaseConfig()
    redis: RedisConfig = RedisConfig()
    security: SecurityConfig = SecurityConfig()
    logging: LoggingConfig = LoggingConfig()
    
    # 配置文件路径
    config_file: Optional[str] = Field(default=None, env="CONFIG_FILE")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 如果指定了配置文件，则加载配置文件
        if self.config_file and Path(self.config_file).exists():
            self._load_config_file()
    
    def _load_config_file(self):
        """加载配置文件"""
        try:
            import json
            import yaml
            
            config_path = Path(self.config_file)
            
            if config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            elif config_path.suffix.lower() in ['.yml', '.yaml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            else:
                return
            
            # 更新配置
            for section, values in config_data.items():
                if hasattr(self, section) and isinstance(values, dict):
                    section_config = getattr(self, section)
                    for key, value in values.items():
                        if hasattr(section_config, key):
                            setattr(section_config, key, value)
                            
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def get_database_url(self, db_type: str = "oracle") -> str:
        """获取数据库连接URL"""
        if db_type.lower() == "oracle":
            return self.database.oracle_url
        elif db_type.lower() == "postgres":
            return self.database.postgres_url
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "app": self.app.dict(),
            "database": self.database.dict(),
            "redis": self.redis.dict(),
            "security": self.security.dict(),
            "logging": self.logging.dict()
        }
    
    class Config:
        case_sensitive = False
        env_file = ".env"
        env_file_encoding = "utf-8"


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()


# 全局配置实例
settings = get_settings()
