from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, DeclarativeBase
from sqlalchemy.pool import QueuePool
from urllib.parse import quote_plus

from app.core.config import get_settings
from app.utils.logger import logger

# 获取配置
settings = get_settings()

# 根据配置选择数据库类型
if hasattr(settings, 'database_type') and settings.database_type == 'oracle':
    # Oracle数据库配置
    DATABASE_URL = settings.get_database_url("oracle")
    pool_config = {
        "poolclass": QueuePool,
        "pool_size": settings.database.oracle_pool_size,
        "max_overflow": settings.database.oracle_max_overflow,
        "pool_timeout": settings.database.oracle_pool_timeout,
        "pool_recycle": settings.database.oracle_pool_recycle,
    }
else:
    # MySQL数据库配置（保持向后兼容）
    encoded_password = quote_plus(settings.DB_PASSWORD)
    DATABASE_URL = f"mysql+aiomysql://{settings.DB_USER}:{encoded_password}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}?charset=utf8mb4"
    pool_config = {
        "pool_pre_ping": True,
        "pool_recycle": 3600,
    }

logger.info(f"数据库连接配置: {DATABASE_URL.split('@')[0]}@***")

engine = create_async_engine(
    DATABASE_URL,
    echo=settings.database.db_echo if hasattr(settings, 'database') else True,
    **pool_config
)

SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

class Base(DeclarativeBase):
    pass

async def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        await db.close()