"""
统一异常处理系统
提供完整的异常类层次结构和错误处理机制
"""

from fastapi import HTTPException, status
from typing import Optional, Dict, Any
import traceback
from enum import Enum


class ErrorCode(Enum):
    """错误代码枚举"""
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = 1000
    VALIDATION_ERROR = 1001
    PERMISSION_DENIED = 1002
    RESOURCE_NOT_FOUND = 1003
    RESOURCE_ALREADY_EXISTS = 1004

    # 用户相关错误 (2000-2099)
    USER_NOT_FOUND = 2001
    USER_ALREADY_EXISTS = 2002
    INVALID_CREDENTIALS = 2003
    USER_DISABLED = 2004

    # 数据库相关错误 (3000-3099)
    DATABASE_CONNECTION_ERROR = 3001
    DATABASE_QUERY_ERROR = 3002
    DATABASE_TRANSACTION_ERROR = 3003
    DATABASE_CONSTRAINT_ERROR = 3004

    # 业务逻辑错误 (4000-4999)
    BUSINESS_LOGIC_ERROR = 4001
    DATA_COMPARISON_ERROR = 4002
    MESSAGE_CHECK_ERROR = 4003
    STEEL_COST_VERIFICATION_ERROR = 4004

    # 外部服务错误 (5000-5099)
    ORACLE_CONNECTION_ERROR = 5001
    ERP_SERVICE_ERROR = 5002
    MES_SERVICE_ERROR = 5003


class BaseAPIException(HTTPException):
    """基础API异常类"""

    def __init__(
        self,
        error_code: ErrorCode,
        detail: str,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        headers: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code
        self.context = context or {}
        self.traceback_info = traceback.format_exc() if context and context.get('include_traceback') else None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "error_code": self.error_code.value,
            "error_name": self.error_code.name,
            "detail": self.detail,
            "status_code": self.status_code
        }

        if self.context:
            result["context"] = self.context

        if self.traceback_info:
            result["traceback"] = self.traceback_info

        return result


# 通用异常类
class ValidationException(BaseAPIException):
    """数据验证异常"""
    def __init__(self, detail: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            error_code=ErrorCode.VALIDATION_ERROR,
            detail=detail,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            context=context
        )


class PermissionDeniedException(BaseAPIException):
    """权限拒绝异常"""
    def __init__(self, detail: str = "权限不足", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            error_code=ErrorCode.PERMISSION_DENIED,
            detail=detail,
            status_code=status.HTTP_403_FORBIDDEN,
            context=context
        )


class ResourceNotFoundException(BaseAPIException):
    """资源未找到异常"""
    def __init__(self, detail: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            detail=detail,
            status_code=status.HTTP_404_NOT_FOUND,
            context=context
        )


class ResourceAlreadyExistsException(BaseAPIException):
    """资源已存在异常"""
    def __init__(self, detail: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(
            error_code=ErrorCode.RESOURCE_ALREADY_EXISTS,
            detail=detail,
            status_code=status.HTTP_409_CONFLICT,
            context=context
        )


# 用户相关异常
class UserNotFoundException(ResourceNotFoundException):
    """用户不存在异常"""
    def __init__(self, detail: str = "用户不存在", context: Optional[Dict[str, Any]] = None):
        super().__init__(detail, context)
        self.error_code = ErrorCode.USER_NOT_FOUND


class UserAlreadyExistsException(ResourceAlreadyExistsException):
    """用户已存在异常"""
    def __init__(self, detail: str = "用户已存在", context: Optional[Dict[str, Any]] = None):
        super().__init__(detail, context)
        self.error_code = ErrorCode.USER_ALREADY_EXISTS


class InvalidCredentialsException(BaseAPIException):
    """无效凭证异常"""
    def __init__(self, detail: str = "用户名或密码错误", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            error_code=ErrorCode.INVALID_CREDENTIALS,
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED,
            context=context
        )


class UserDisabledException(BaseAPIException):
    """用户被禁用异常"""
    def __init__(self, detail: str = "用户账户已被禁用", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            error_code=ErrorCode.USER_DISABLED,
            detail=detail,
            status_code=status.HTTP_403_FORBIDDEN,
            context=context
        )


# 数据库相关异常
class DatabaseException(BaseAPIException):
    """数据库异常基类"""
    def __init__(
        self,
        error_code: ErrorCode,
        detail: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_code=error_code,
            detail=detail,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            context=context
        )


class DatabaseConnectionException(DatabaseException):
    """数据库连接异常"""
    def __init__(self, detail: str = "数据库连接失败", context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.DATABASE_CONNECTION_ERROR, detail, context)


class DatabaseQueryException(DatabaseException):
    """数据库查询异常"""
    def __init__(self, detail: str = "数据库查询失败", context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.DATABASE_QUERY_ERROR, detail, context)


class DatabaseTransactionException(DatabaseException):
    """数据库事务异常"""
    def __init__(self, detail: str = "数据库事务失败", context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.DATABASE_TRANSACTION_ERROR, detail, context)


# 业务逻辑异常
class BusinessLogicException(BaseAPIException):
    """业务逻辑异常基类"""
    def __init__(
        self,
        error_code: ErrorCode,
        detail: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_code=error_code,
            detail=detail,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            context=context
        )


class DataComparisonException(BusinessLogicException):
    """数据比较异常"""
    def __init__(self, detail: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.DATA_COMPARISON_ERROR, detail, context)


class MessageCheckException(BusinessLogicException):
    """消息检查异常"""
    def __init__(self, detail: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.MESSAGE_CHECK_ERROR, detail, context)


class SteelCostVerificationException(BusinessLogicException):
    """钢铁成本验证异常"""
    def __init__(self, detail: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.STEEL_COST_VERIFICATION_ERROR, detail, context)


# 外部服务异常
class ExternalServiceException(BaseAPIException):
    """外部服务异常基类"""
    def __init__(
        self,
        error_code: ErrorCode,
        detail: str,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            error_code=error_code,
            detail=detail,
            status_code=status.HTTP_502_BAD_GATEWAY,
            context=context
        )


class OracleConnectionException(ExternalServiceException):
    """Oracle连接异常"""
    def __init__(self, detail: str = "Oracle数据库连接失败", context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.ORACLE_CONNECTION_ERROR, detail, context)


class ERPServiceException(ExternalServiceException):
    """ERP服务异常"""
    def __init__(self, detail: str = "ERP服务调用失败", context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.ERP_SERVICE_ERROR, detail, context)


class MESServiceException(ExternalServiceException):
    """MES服务异常"""
    def __init__(self, detail: str = "MES服务调用失败", context: Optional[Dict[str, Any]] = None):
        super().__init__(ErrorCode.MES_SERVICE_ERROR, detail, context)


# 保持向后兼容性的别名
APIException = BaseAPIException
UserAlreadyExists = UserAlreadyExistsException
UserNotFound = UserNotFoundException
InvalidCredentials = InvalidCredentialsException