from fastapi import HTTPException, status

class APIException(HTTPException):
    """基础API异常类"""
    def __init__(
        self,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        detail: str = None,
        code: int = None,
    ):
        super().__init__(status_code=status_code, detail=detail)
        self.code = code if code is not None else status_code

class UserAlreadyExists(APIException):
    """用户已存在异常"""
    def __init__(self, detail: str = "用户已存在"):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=detail,
            code=1001
        )

class UserNotFound(APIException):
    """用户不存在异常"""
    def __init__(self, detail: str = "用户不存在"):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            code=1002
        )

class InvalidCredentials(APIException):
    """无效凭证异常"""
    def __init__(self, detail: str = "用户名或密码错误"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            code=1003
        )