from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.utils.logger import logger
from app.database.session import get_db, engine, Base
from app.core.config import get_settings
from app.middleware.exception_handler import setup_exception_handlers
from app.routers.system import user, user_role, role, auth, operation_logs
from app.models.system import role as role_model, user_role as user_role_model, user as user_model
from app.routers.steel_making.mes import manual_warehouse,mes_maintenance,mes_message_check
from app.routers.steel_making.erp import auto_schedule, data_comparison, heat_no_switch, initial_casting, ix_receiva, \
    ix_batch_check, maintenance_history, message_check, steel_cost_data_verification
from app.routers.steel_making.common import task_result_details
from app.services.common.scheduler import maintenance_scheduler

settings = get_settings()

app = FastAPI(
    title=settings.app.name,
    description=settings.app.description,
    version=settings.app.version,
    debug=settings.app.debug,
    docs_url=settings.app.docs_url,
    redoc_url=settings.app.redoc_url,
    openapi_url=settings.app.openapi_url
)

# 设置全局异常处理器
setup_exception_handlers(app)


app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.security.cors_origins,
    allow_credentials=True,
    allow_methods=settings.security.cors_methods,
    allow_headers=settings.security.cors_headers,
)


app.include_router(auth.router)
app.include_router(user.router)
app.include_router(role.router)
app.include_router(user_role.router)
app.include_router(operation_logs.router, tags=["operation-logs"])
app.include_router(data_comparison.router, tags=["data-comparison"])
app.include_router(message_check.router, tags=["message-check"])
app.include_router(auto_schedule.router, tags=["auto-schedule"])
app.include_router(initial_casting.router, tags=["initial-casting"])
app.include_router(mes_message_check.router, tags=["mes-message-check"])
app.include_router(mes_maintenance.router, tags=["mes-maintenance"])
app.include_router(maintenance_history.router, tags=["maintenance-history"])
app.include_router(ix_receiva.router, tags=["ix-receiva"])
app.include_router(manual_warehouse.router, tags=["manual-warehouse"])
app.include_router(steel_cost_data_verification.router, tags=["steel-cost-data-verification"])
app.include_router(task_result_details.router, tags=["task-result-details"])
app.include_router(heat_no_switch.router, tags=["heat-no-switch"])
app.include_router(ix_batch_check.router, tags=["ix-batch-check"])

# 健康检查端点

async def init_default_data():
    """初始化默认数据"""
    from app.services.system.auth import get_password_hash
    from sqlalchemy import select

    async with AsyncSession(engine) as db:
        # 检查是否已有admin用户
        stmt = select(user_model.User).where(user_model.User.username == "admin")
        result = await db.execute(stmt)
        admin_user = result.scalar_one_or_none()

        if not admin_user:
            # 创建默认admin用户
            admin_user = user_model.User(
                username="admin",
                password=get_password_hash("123456"),
                nickname="系统管理员",
                remark="默认管理员账户",
                status=1
            )
            db.add(admin_user)
            await db.commit()
            await db.refresh(admin_user)
            logger.info("创建默认admin用户成功")

        # 检查是否已有admin角色
        stmt = select(role_model.Role).where(role_model.Role.name == "admin")
        result = await db.execute(stmt)
        admin_role = result.scalar_one_or_none()

        if not admin_role:
            # 创建默认admin角色
            admin_role = role_model.Role(
                name="admin",
                description="系统管理员角色",
                permissions=["*"]  # 所有权限
            )
            db.add(admin_role)
            await db.commit()
            await db.refresh(admin_role)
            logger.info("创建默认admin角色成功")

        # 获取用户和角色的ID
        admin_user_id = admin_user.id if admin_user else None
        admin_role_id = admin_role.id if admin_role else None

        if admin_user_id and admin_role_id:
            # 检查用户角色关联
            stmt = select(user_role_model.UserRole).where(
                user_role_model.UserRole.user_id == admin_user_id,
                user_role_model.UserRole.role_id == admin_role_id
            )
            result = await db.execute(stmt)
            user_role = result.scalar_one_or_none()

            if not user_role:
                # 创建用户角色关联
                user_role = user_role_model.UserRole(
                    user_id=admin_user_id,
                    role_id=admin_role_id
                )
                db.add(user_role)
                await db.commit()
                logger.info("创建admin用户角色关联成功")

@app.on_event("startup")
async def startup_event():
    logger.info("应用启动")

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    logger.info("数据库表初始化完成")

    await init_default_data()
    logger.info("默认数据初始化完成")

    try:
        await maintenance_scheduler.start()
        logger.info("定时任务调度器启动成功")
    except Exception as e:
        logger.error(f"定时任务调度器启动失败: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("应用关闭")


    try:
        await maintenance_scheduler.stop()
        logger.info("定时任务调度器已停止")
    except Exception as e:
        logger.error(f"停止定时任务调度器失败: {e}")


    await engine.dispose()
    logger.info("数据库连接池已释放")

@app.get("/health")
async def health_check(db: AsyncSession = Depends(get_db)):
    # 获取当前连接的实际配置
    config = {
        "DB_HOST": settings.DB_HOST,
        "DB_PORT": settings.DB_PORT,
        "DB_USER": settings.DB_USER,
        "DB_NAME": settings.DB_NAME
    }
    
    # 详细的健康检查，验证数据库连接
    try:
        await db.execute(text("SELECT 1"))
        await db.commit()  # 确保连接有效
        return {
            "status": "ok",
            "message": "服务运行正常",
            "database": "connected",
            "config": config
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}", exc_info=True)
        return {
            "status": "error",
            "message": "数据库连接失败",
            "error": str(e),
            "config": config,
            "solution": f"请检查MySQL服务是否运行，以及配置是否正确: {config}"
        }, 500

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)