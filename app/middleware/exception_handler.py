"""
全局异常处理中间件
统一处理应用中的所有异常，提供一致的错误响应格式
"""

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
import oracledb
from typing import Union
import traceback
import logging

from app.exceptions import BaseAPIException, ErrorCode, DatabaseException
from app.utils.logger import logger


class GlobalExceptionHandler:
    """全局异常处理器"""
    
    @staticmethod
    async def handle_base_api_exception(request: Request, exc: BaseAPIException) -> JSONResponse:
        """处理自定义API异常"""
        logger.error(
            f"API异常: {exc.error_code.name} - {exc.detail}",
            extra={
                "error_code": exc.error_code.value,
                "status_code": exc.status_code,
                "path": request.url.path,
                "method": request.method,
                "context": exc.context
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "code": exc.error_code.value,
                "msg": exc.detail,
                "data": None,
                "error_details": exc.to_dict()
            }
        )
    
    @staticmethod
    async def handle_http_exception(request: Request, exc: HTTPException) -> JSONResponse:
        """处理HTTP异常"""
        logger.error(
            f"HTTP异常: {exc.status_code} - {exc.detail}",
            extra={
                "status_code": exc.status_code,
                "path": request.url.path,
                "method": request.method
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "code": exc.status_code,
                "msg": exc.detail,
                "data": None
            }
        )
    
    @staticmethod
    async def handle_validation_error(request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理请求验证错误"""
        error_details = []
        for error in exc.errors():
            error_details.append({
                "field": " -> ".join(str(loc) for loc in error["loc"]),
                "message": error["msg"],
                "type": error["type"]
            })
        
        logger.warning(
            f"请求验证失败: {len(error_details)}个错误",
            extra={
                "path": request.url.path,
                "method": request.method,
                "errors": error_details
            }
        )
        
        return JSONResponse(
            status_code=422,
            content={
                "code": ErrorCode.VALIDATION_ERROR.value,
                "msg": "请求数据验证失败",
                "data": None,
                "validation_errors": error_details
            }
        )
    
    @staticmethod
    async def handle_sqlalchemy_error(request: Request, exc: SQLAlchemyError) -> JSONResponse:
        """处理SQLAlchemy数据库错误"""
        error_msg = "数据库操作失败"
        error_code = ErrorCode.DATABASE_QUERY_ERROR
        
        if isinstance(exc, OperationalError):
            error_msg = "数据库连接或操作失败"
            error_code = ErrorCode.DATABASE_CONNECTION_ERROR
        elif isinstance(exc, IntegrityError):
            error_msg = "数据完整性约束违反"
            error_code = ErrorCode.DATABASE_CONSTRAINT_ERROR
        
        logger.error(
            f"数据库错误: {error_msg} - {str(exc)}",
            extra={
                "error_type": type(exc).__name__,
                "path": request.url.path,
                "method": request.method,
                "original_error": str(exc.orig) if hasattr(exc, 'orig') else str(exc)
            }
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "code": error_code.value,
                "msg": error_msg,
                "data": None,
                "error_details": {
                    "error_type": type(exc).__name__,
                    "original_error": str(exc.orig) if hasattr(exc, 'orig') else str(exc)
                }
            }
        )
    
    @staticmethod
    async def handle_oracle_error(request: Request, exc: oracledb.Error) -> JSONResponse:
        """处理Oracle数据库错误"""
        error_msg = "Oracle数据库操作失败"
        
        # 解析Oracle错误代码
        oracle_error = str(exc)
        if "ORA-00001" in oracle_error:
            error_msg = "数据重复，违反唯一性约束"
        elif "ORA-01400" in oracle_error:
            error_msg = "必填字段不能为空"
        elif "ORA-12541" in oracle_error or "ORA-12514" in oracle_error:
            error_msg = "Oracle数据库连接失败"
        
        logger.error(
            f"Oracle错误: {error_msg} - {oracle_error}",
            extra={
                "path": request.url.path,
                "method": request.method,
                "oracle_error": oracle_error
            }
        )
        
        return JSONResponse(
            status_code=502,
            content={
                "code": ErrorCode.ORACLE_CONNECTION_ERROR.value,
                "msg": error_msg,
                "data": None,
                "error_details": {
                    "oracle_error": oracle_error
                }
            }
        )
    
    @staticmethod
    async def handle_generic_exception(request: Request, exc: Exception) -> JSONResponse:
        """处理通用异常"""
        error_msg = "服务器内部错误"
        
        # 记录完整的错误信息
        logger.error(
            f"未处理的异常: {type(exc).__name__} - {str(exc)}",
            extra={
                "path": request.url.path,
                "method": request.method,
                "exception_type": type(exc).__name__,
                "traceback": traceback.format_exc()
            }
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "code": ErrorCode.UNKNOWN_ERROR.value,
                "msg": error_msg,
                "data": None,
                "error_details": {
                    "exception_type": type(exc).__name__,
                    "message": str(exc)
                }
            }
        )


def setup_exception_handlers(app):
    """设置全局异常处理器"""
    
    # 自定义API异常
    @app.exception_handler(BaseAPIException)
    async def base_api_exception_handler(request: Request, exc: BaseAPIException):
        return await GlobalExceptionHandler.handle_base_api_exception(request, exc)
    
    # HTTP异常
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return await GlobalExceptionHandler.handle_http_exception(request, exc)
    
    @app.exception_handler(StarletteHTTPException)
    async def starlette_http_exception_handler(request: Request, exc: StarletteHTTPException):
        return await GlobalExceptionHandler.handle_http_exception(request, exc)
    
    # 请求验证错误
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        return await GlobalExceptionHandler.handle_validation_error(request, exc)
    
    # 数据库错误
    @app.exception_handler(SQLAlchemyError)
    async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError):
        return await GlobalExceptionHandler.handle_sqlalchemy_error(request, exc)
    
    # Oracle错误
    @app.exception_handler(oracledb.Error)
    async def oracle_exception_handler(request: Request, exc: oracledb.Error):
        return await GlobalExceptionHandler.handle_oracle_error(request, exc)
    
    # 通用异常
    @app.exception_handler(Exception)
    async def generic_exception_handler(request: Request, exc: Exception):
        return await GlobalExceptionHandler.handle_generic_exception(request, exc)
