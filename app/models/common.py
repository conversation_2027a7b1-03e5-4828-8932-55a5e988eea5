"""
通用模型定义
"""
from datetime import datetime
import enum


def get_current_time():
    """获取当前时间"""
    return datetime.now()


class TaskStatus(enum.Enum):
    """通用任务状态枚举"""
    PENDING = "待执行"
    RUNNING = "执行中"
    COMPLETED = "已完成"
    FAILED = "执行失败"


class MessageType(enum.Enum):
    """电文类型枚举"""
    RECEIVE = "接收"
    SEND = "发送"
    PRODRES_SUMMARY = "钢坯产量调拨汇总"


class Factory(enum.Enum):
    """厂别枚举"""
    FACTORY_1 = "一厂"
    FACTORY_2 = "二厂"


class StepStatus(enum.Enum):
    """步骤状态枚举"""
    PENDING = "待执行"
    RUNNING = "执行中"
    COMPLETED = "已完成"
    FAILED = "执行失败"


class MESMaintenanceTaskType(enum.Enum):
    """MES维护任务类型枚举"""
    PRODRES_SUMMARY = "钢坯产量调拨汇总报表"
    SCRAP_STEEL_FIX = "转炉废钢号修复"
    BOF_SCRAP_STEEL_CLEAR = "转炉无法选择废钢号"
    OTHER = "其他维护任务"