"""
任务结果明细表模型
用于存储各种任务的详细结果数据，支持分页查询
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import Integer, String, Float, Text, DateTime, ForeignKey, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.database.session import Base
from app.models.common import get_current_time, TaskStatus, Factory


class SteelCostVerificationDetail(Base):
    """炼钢成本数据核对差异明细表"""
    __tablename__ = "steel_cost_verification_details"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_id: Mapped[int] = mapped_column(Integer, ForeignKey("steel_cost_data_verification_tasks.id"), nullable=False, comment="任务ID")
    
    # 基本信息
    heat_no: Mapped[str] = mapped_column(String(50), nullable=False, comment="炉号")
    material_code: Mapped[str] = mapped_column(String(50), nullable=False, comment="物料编码")
    
    # 各系统数据
    oj_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="OJ重量(kg)")
    mr_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="MR重量(kg)")
    ip_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="IP重量(kg)")
    
    # 差异信息
    status: Mapped[str] = mapped_column(String(50), nullable=False, comment="状态")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="描述")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")
    
    # 关联关系
    task: Mapped["SteelCostDataVerificationTask"] = relationship(back_populates="details")
    
    # 索引
    __table_args__ = (
        Index('idx_steel_cost_task_id', 'task_id'),
        Index('idx_steel_cost_heat_material', 'heat_no', 'material_code'),
        Index('idx_steel_cost_status', 'status'),
        {"comment": "炼钢成本数据核对差异明细表"}
    )


class DataComparisonDetail(Base):
    """数据对比差异明细表"""
    __tablename__ = "data_comparison_details"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_id: Mapped[int] = mapped_column(Integer, ForeignKey("data_comparison_tasks.id"), nullable=False, comment="任务ID")
    
    # 基本信息
    heat_no: Mapped[str] = mapped_column(String(50), nullable=False, comment="炉号")
    material_code: Mapped[str] = mapped_column(String(50), nullable=False, comment="物料编码")
    
    # 对比数据
    mes_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="MES重量(kg)")
    erp_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="ERP重量(kg)")
    difference: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="差异(kg)")
    difference_rate: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="差异率(%)")
    
    # 状态信息
    status: Mapped[str] = mapped_column(String(50), nullable=False, comment="状态")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="描述")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")
    
    # 关联关系
    task: Mapped["DataComparisonTask"] = relationship(back_populates="details")
    
    # 索引
    __table_args__ = (
        Index('idx_data_comparison_task_id', 'task_id'),
        Index('idx_data_comparison_heat_material', 'heat_no', 'material_code'),
        Index('idx_data_comparison_status', 'status'),
        Index('idx_data_comparison_difference', 'difference'),
        {"comment": "物料消耗对比差异明细表"}
    )


class MessageCheckDetail(Base):
    """电文检查错误明细表"""
    __tablename__ = "message_check_details"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_id: Mapped[int] = mapped_column(Integer, ForeignKey("message_check_tasks.id"), nullable=False, comment="任务ID")
    
    # 错误信息
    error_type: Mapped[str] = mapped_column(String(100), nullable=False, comment="错误类型")
    error_message: Mapped[str] = mapped_column(Text, nullable=False, comment="错误消息")
    error_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="错误代码")
    
    # 相关数据
    heat_no: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="炉号")
    material_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="物料编码")
    batch_no: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="批次号")
    
    # 建议解决方案
    suggested_solution: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="建议解决方案")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")
    
    # 关联关系
    task: Mapped["MessageCheckTask"] = relationship(back_populates="details")
    
    # 索引
    __table_args__ = (
        Index('idx_message_check_task_id', 'task_id'),
        Index('idx_message_check_error_type', 'error_type'),
        Index('idx_message_check_heat_material', 'heat_no', 'material_code'),
        {"comment":"电文检查错误明细表"}
    )


class IxReceivaDetail(Base):
    """IX收账检查详细记录表"""
    __tablename__ = "ix_receiva_details"


    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_id: Mapped[int] = mapped_column(Integer, ForeignKey("ix_receiva_tasks.id"), nullable=False, comment="任务ID")

    # 基本信息（原results表字段）
    batch_no: Mapped[str] = mapped_column(String(100), nullable=False, comment="批次号")
    sys_id: Mapped[str] = mapped_column(String(10), nullable=False, comment="系统ID (IB/IS)")

    # 时间信息（原results表字段）
    start_date: Mapped[Optional[str]] = mapped_column(String(8), nullable=True, comment="开始日期")
    start_time: Mapped[Optional[str]] = mapped_column(String(6), nullable=True, comment="开始时间")
    end_date: Mapped[Optional[str]] = mapped_column(String(8), nullable=True, comment="结束日期")
    end_time: Mapped[Optional[str]] = mapped_column(String(6), nullable=True, comment="结束时间")

    # 执行信息（原results表字段）
    exec_emp_no: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="执行员工号")
    exec_result: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="执行结果")
    exec_msg: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="执行消息")

    # 状态统计（原results表字段）
    succeed: Mapped[int] = mapped_column(Integer, default=0, comment="成功数量")
    error: Mapped[int] = mapped_column(Integer, default=0, comment="错误数量")
    processing: Mapped[int] = mapped_column(Integer, default=0, comment="处理中数量")
    ip_succeed: Mapped[int] = mapped_column(Integer, default=0, comment="IP成功数量")
    ip_error: Mapped[int] = mapped_column(Integer, default=0, comment="IP错误数量")
    ip_unprocess: Mapped[int] = mapped_column(Integer, default=0, comment="IP未处理数量")

    # 计算字段（原results表字段）
    duration_minutes: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="耗时分钟数")
    status_description: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="状态描述")

    # 错误分析字段（原details表字段）
    error_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="错误类型")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误消息")
    suggested_sql: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="建议SQL解决方案")

    # 状态字段（统一）
    status: Mapped[str] = mapped_column(String(50), nullable=False, comment="记录状态")

    # 时间戳
    check_time: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="检查时间")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")

    # 关联关系
    task: Mapped["IxReceivaTask"] = relationship(back_populates="details")

    # 索引
    __table_args__ = (
        Index('idx_ix_receiva_task_id', 'task_id'),
        Index('idx_ix_receiva_system_status', 'sys_id', 'status'),
        Index('idx_ix_receiva_batch_no', 'batch_no'),
        Index('idx_ix_receiva_error_type', 'error_type'),
        Index('idx_ix_receiva_exec_result', 'exec_result'),
        {"comment": "IX收账检查详细记录表"}
    )
