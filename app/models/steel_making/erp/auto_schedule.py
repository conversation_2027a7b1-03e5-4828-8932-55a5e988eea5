from datetime import datetime
from typing import Optional
from sqlalchemy import Integer, String, DateTime, Text, Enum, JSON, Float
from sqlalchemy.orm import Mapped, mapped_column
from app.database.session import Base
from app.models.common import get_current_time, TaskStatus, StepStatus


class AutoScheduleTask(Base):
    """自动排程任务表"""
    __tablename__ = "auto_schedule_tasks"
    __table_args__ = {"comment": "自动排程任务表"}

    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="任务名称")
    task_type: Mapped[str] = mapped_column(String(50), nullable=False, default="POSTING", comment="任务类型：POSTING(抛账), IB_IX_IP(IB=>IX=>IP), IS_IX_IP(IS=>IX=>IP)")
    status: Mapped[TaskStatus] = mapped_column(Enum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    progress: Mapped[int] = mapped_column(Integer, default=0, comment="执行进度(0-100)")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    
    # 执行时间记录
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="完成时间")
    created_by: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="创建人ID")
    
    # 步骤执行时间记录（JSON格式）
    step_timings: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="各步骤耗时记录")
    
    # 执行结果
    execution_result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="执行结果JSON")


class AutoScheduleStep(Base):
    """自动排程步骤表"""
    __tablename__ = "auto_schedule_steps"
    __table_args__ = {"comment": "自动排程任务明细表"}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_id: Mapped[int] = mapped_column(Integer, nullable=False, comment="任务ID")
    step_name: Mapped[str] = mapped_column(String(100), nullable=False, comment="步骤名称")  # OJRELOADMTRL, OJ32To33, OJ33ToMR
    step_order: Mapped[int] = mapped_column(Integer, nullable=False, comment="步骤顺序")
    status: Mapped[StepStatus] = mapped_column(Enum(StepStatus), default=StepStatus.PENDING, comment="步骤状态")
    
    # 时间记录
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="完成时间")
    duration_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="耗时(秒)")
    
    # 执行详情
    queue_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="队列ID")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    execution_details: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="执行详情JSON")
