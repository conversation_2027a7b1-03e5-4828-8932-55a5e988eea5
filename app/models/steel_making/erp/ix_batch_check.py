"""
IX批次检查数据模型
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Integer, String, DateTime, Text, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import Mapped, mapped_column

from app.database.session import Base
from app.models.common import TaskStatus

class IxBatchCheckTask(Base):
    """IX批次检查任务表"""
    __tablename__ = "ix_batch_check_tasks"
    __table_args__ = {"comment": "IX批次检查任务表"}


    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="任务名称")
    status: Mapped[TaskStatus] = mapped_column(Enum(TaskStatus), nullable=False, default=TaskStatus.PENDING, comment="任务状态")
    progress: Mapped[int] = mapped_column(Integer, default=0, comment="进度百分比")
    error_count: Mapped[int] = mapped_column(Integer, default=0, comment="错误记录数量")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    check_result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="检查结果JSON")
    created_by: Mapped[int] = mapped_column(Integer, nullable=False, comment="创建用户ID")
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment="创建时间")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="完成时间")

    def __repr__(self):
        return f"<IxBatchCheckTask(id={self.id}, task_name='{self.task_name}', status='{self.status}')>"
