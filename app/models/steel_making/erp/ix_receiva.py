"""
IX收账作业检查数据模型
"""

from datetime import datetime
from typing import Optional, List
from sqlalchemy import Integer, String, DateTime, Text, Float, Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from app.database.session import Base
# from app.models.steel_making.common.task_result_details import IxReceivaDetail


class IxReceivaTask(Base):
    """IX收账作业检查任务表"""
    __tablename__ = "ix_receiva_tasks"
    __table_args__ = {"comment": "IX收账作业检查任务表"}


    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="任务名称")
    system_id: Mapped[str] = mapped_column(String(10), nullable=False, comment="系统ID (IB/IS)")
    start_date: Mapped[str] = mapped_column(String(8), nullable=False, comment="开始日期 YYYYMMDD")
    end_date: Mapped[str] = mapped_column(String(8), nullable=False, comment="结束日期 YYYYMMDD")
    status_filter: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="状态筛选条件 JSON")
    
    # 任务状态
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="pending", comment="任务状态")
    progress: Mapped[int] = mapped_column(Integer, default=0, comment="进度百分比")
    
    # 检查结果统计
    total_count: Mapped[int] = mapped_column(Integer, default=0, comment="总检查数量")
    error_count: Mapped[int] = mapped_column(Integer, default=0, comment="收账错误数量")
    processing_count: Mapped[int] = mapped_column(Integer, default=0, comment="收账处理中数量")
    ip_error_count: Mapped[int] = mapped_column(Integer, default=0, comment="抛IP错误数量")
    long_duration_count: Mapped[int] = mapped_column(Integer, default=0, comment="耗时过长数量")
    
    # 执行信息
    execution_time: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="执行耗时(秒)")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    result_data: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="检查结果数据 JSON")
    
    # 用户和时间信息
    user_id: Mapped[int] = mapped_column(Integer, nullable=False, comment="创建用户ID")
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    # 是否为自动任务
    is_auto_task: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否为自动任务")
    min_duration_minutes: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="最小耗时分钟数(自动任务)")

    # 关联关系
    details: Mapped[List["IxReceivaDetail"]] = relationship(back_populates="task", cascade="all, delete-orphan")

