from datetime import datetime
from typing import Optional, List
from sqlalchemy import Integer, String, DateTime, Text, Enum, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.database.session import Base
from app.models.common import get_current_time, TaskStatus, Factory
# from app.models.steel_making.common.task_result_details import SteelCostVerificationDetail


class SteelCostDataVerificationTask(Base):
    """炼钢成本数据核对任务表"""
    __tablename__ = "steel_cost_data_verification_tasks"
    __table_args__ = {"comment": "炼钢成本数据核对任务表"}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="任务名称")
    start_date: Mapped[str] = mapped_column(String(10), nullable=False, comment="开始日期")
    end_date: Mapped[str] = mapped_column(String(10), nullable=False, comment="结束日期")
    factory: Mapped[Optional[Factory]] = mapped_column(Enum(Factory), nullable=True, comment="厂别")
    heat_no: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="炉号")
    material_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="物料编码")
    status: Mapped[TaskStatus] = mapped_column(Enum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    progress: Mapped[int] = mapped_column(Integer, default=0, comment="执行进度(0-100)")
    total_records: Mapped[int] = mapped_column(Integer, default=0, comment="总记录数")
    processed_records: Mapped[int] = mapped_column(Integer, default=0, comment="已处理记录数")
    difference_count: Mapped[int] = mapped_column(Integer, default=0, comment="差异记录数")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    result_summary: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="结果摘要")
    verification_result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="核对结果JSON")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="完成时间")
    created_by: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="创建人ID")

    # 关联关系
    details: Mapped[List["SteelCostVerificationDetail"]] = relationship(back_populates="task", cascade="all, delete-orphan")
