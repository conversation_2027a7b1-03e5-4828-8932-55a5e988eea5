from datetime import datetime
from typing import Optional
from sqlalchemy import Integer, String, Float, DateTime, Text, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import Mapped, mapped_column
from app.database.session import Base


class ManualWarehouseRecord(Base):
    """人工入库记录表"""
    __tablename__ = "manual_warehouse_records"
    __table_args__ = {"comment": "人工入库物料记录表"}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    les_no: Mapped[str] = mapped_column(String(20), unique=True, index=True, comment="LES编号")
    material_code: Mapped[str] = mapped_column(String(50), index=True, comment="物料编码")
    material_name: Mapped[str] = mapped_column(String(200), comment="物料名称")
    weight: Mapped[Optional[float]] = mapped_column(Float(precision=3), nullable=True, comment="重量(吨)")
    factory: Mapped[str] = mapped_column(String(10), comment="工厂(L1/L2)")
    warehouse_date: Mapped[str] = mapped_column(String(10), comment="入库日期(YYYY-MM-DD)")
    warehouse_datetime: Mapped[datetime] = mapped_column(DateTime, comment="入库时间(11点)")
    insert_sql: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="执行的INSERT语句")
    status: Mapped[str] = mapped_column(String(20), default="pending", comment="状态: pending/success/failed")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    user_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="创建用户ID")
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
