from datetime import datetime
from typing import Optional
from sqlalchemy import Integer, String, DateTime, Text, Enum, JSON
from sqlalchemy.orm import Mapped, mapped_column
from app.database.session import Base
from app.models.common import get_current_time, TaskStatus, MESMaintenanceTaskType

# 为了保持向后兼容，创建别名
MESMaintenanceTaskStatus = TaskStatus


class MESMaintenanceTask(Base):
    """MES维护任务表"""
    __tablename__ = "mes_maintenance_tasks"
    __table_args__ = {"comment": "MES维护任务表"}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="任务名称")
    task_type: Mapped[MESMaintenanceTaskType] = mapped_column(Enum(MESMaintenanceTaskType), nullable=False, comment="任务类型")
    target_date: Mapped[Optional[str]] = mapped_column(String(10), nullable=True, comment="目标日期")
    status: Mapped[MESMaintenanceTaskStatus] = mapped_column(Enum(MESMaintenanceTaskStatus), default=MESMaintenanceTaskStatus.PENDING, comment="任务状态")
    progress: Mapped[int] = mapped_column(Integer, default=0, comment="执行进度(0-100)")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    task_result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="任务结果JSON")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="完成时间")
    created_by: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="创建人ID")
