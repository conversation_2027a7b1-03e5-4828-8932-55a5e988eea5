from datetime import datetime
from typing import Optional, List
from sqlalchemy import Integer, String, DateTime, Text, Enum, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.database.session import Base
from app.models.common import get_current_time, TaskStatus, MessageType


class MessageCheckTask(Base):
    """电文检查任务表"""
    __tablename__ = "message_check_tasks"
    __table_args__ = {"comment": "电文检查任务表"}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="任务名称")
    check_date: Mapped[str] = mapped_column(String(10), nullable=False, comment="检查日期")
    message_type: Mapped[MessageType] = mapped_column(Enum(MessageType), nullable=False, comment="电文类型")
    status: Mapped[TaskStatus] = mapped_column(Enum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    progress: Mapped[int] = mapped_column(Integer, default=0, comment="执行进度(0-100)")
    total_records: Mapped[int] = mapped_column(Integer, default=0, comment="总记录数")
    error_count: Mapped[int] = mapped_column(Integer, default=0, comment="错误数量")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
    check_result: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="检查结果JSON")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="创建时间")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="开始时间")
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="完成时间")
    created_by: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="创建人ID")

    # 关联关系
    details: Mapped[List["MessageCheckDetail"]] = relationship(back_populates="task", cascade="all, delete-orphan")
