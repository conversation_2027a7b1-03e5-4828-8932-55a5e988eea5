from datetime import datetime
from typing import Optional
from sqlalchemy import Integer, String, Text, DateTime, Float
from sqlalchemy.orm import Mapped, mapped_column
from app.database.session import Base
from app.models.common import get_current_time


class OperationLog(Base):
    """操作日志模型"""
    __tablename__ = "operation_logs"
    __table_args__ = {"comment": "操作日志"}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    operation_time: Mapped[datetime] = mapped_column(DateTime, default=get_current_time, comment="操作时间")
    operation_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="操作类型")
    operation_content: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="操作内容")
    operator_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="操作人ID")
    operator_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="操作人姓名")
    duration: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="操作耗时(秒)")
    ip_address: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="IP地址")
    user_agent: Mapped[Optional[str]] = mapped_column(String(255), nullable=True, comment="用户代理")
    status: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, comment="操作状态")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="错误信息")
