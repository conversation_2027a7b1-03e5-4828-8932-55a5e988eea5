from datetime import datetime
from typing import List, Optional
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, String, Integer, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from app.database.session import Base
import bcrypt

class User(Base):
    """用户模型"""
    __tablename__ = "sys_user"
    __table_args__ = {"comment": "系统用户表"}

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True, comment="主键ID")
    username: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, comment="用户名")
    password: Mapped[str] = mapped_column(String(255), nullable=False, comment="密码")
    nickname: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="昵称")
    remark: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="备注")
    status: Mapped[int] = mapped_column(Integer, default=1, comment="状态(0:禁用,1:启用)")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    def set_password(self, password: str):
        """设置密码(加密)"""
        salt = bcrypt.gensalt()
        self.password = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

    def check_password(self, password: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password.encode('utf-8'))

    def __repr__(self):
        return f"<User(id={self.id}, username={self.username})>"