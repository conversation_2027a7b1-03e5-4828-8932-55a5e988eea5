from sqlalchemy import Integer
from sqlalchemy.orm import Mapped, mapped_column
from app.database.session import Base

class UserRole(Base):
    """用户-角色关联表"""
    __tablename__ = "sys_user_mid_role"
    __table_args__ = {"comment": "系统用户和角色中间表"}

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, nullable=False, comment="用户ID")
    role_id: Mapped[int] = mapped_column(Integer, nullable=False, comment="角色ID")

    def __repr__(self):
        return f"<UserRole(id={self.id}, user_id={self.user_id}, role_id={self.role_id})>"