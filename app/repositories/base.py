"""
基础Repository类
提供通用的数据访问操作
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Optional, List, Dict, Any, Type
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import func, and_, or_
from pydantic import BaseModel

from app.exceptions import ResourceNotFoundException, ValidationException
from app.utils.exception_decorators import handle_database_errors
from app.utils.logger import logger

# 泛型类型变量
ModelType = TypeVar("ModelType", bound=DeclarativeBase)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseRepository(Generic[ModelType, CreateSchemaType, UpdateSchemaType], ABC):
    """基础Repository类"""
    
    def __init__(self, model: Type[ModelType], db: AsyncSession):
        self.model = model
        self.db = db
    
    @handle_database_errors("创建记录")
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """创建记录"""
        obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in
        db_obj = self.model(**obj_data)
        self.db.add(db_obj)
        await self.db.commit()
        await self.db.refresh(db_obj)
        logger.info(f"创建{self.model.__name__}记录成功: ID={getattr(db_obj, 'id', 'N/A')}")
        return db_obj
    
    @handle_database_errors("根据ID获取记录")
    async def get(self, id: Any) -> Optional[ModelType]:
        """根据ID获取记录"""
        if not id:
            raise ValidationException("ID不能为空")
        
        result = await self.db.execute(select(self.model).where(self.model.id == id))
        return result.scalars().first()
    
    @handle_database_errors("根据ID获取记录（必须存在）")
    async def get_or_404(self, id: Any) -> ModelType:
        """根据ID获取记录，如果不存在则抛出异常"""
        obj = await self.get(id)
        if not obj:
            raise ResourceNotFoundException(f"{self.model.__name__} ID {id} 不存在")
        return obj
    
    @handle_database_errors("获取多条记录")
    async def get_multi(
        self, 
        skip: int = 0, 
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None
    ) -> List[ModelType]:
        """获取多条记录"""
        query = select(self.model)
        
        # 应用过滤条件
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key) and value is not None:
                    query = query.where(getattr(self.model, key) == value)
        
        # 应用排序
        if order_by and hasattr(self.model, order_by):
            query = query.order_by(getattr(self.model, order_by))
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    @handle_database_errors("统计记录数量")
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """统计记录数量"""
        query = select(func.count(self.model.id))
        
        # 应用过滤条件
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key) and value is not None:
                    query = query.where(getattr(self.model, key) == value)
        
        result = await self.db.execute(query)
        return result.scalar()
    
    @handle_database_errors("更新记录")
    async def update(
        self, 
        db_obj: ModelType, 
        obj_in: UpdateSchemaType
    ) -> ModelType:
        """更新记录"""
        obj_data = obj_in.dict(exclude_unset=True) if hasattr(obj_in, 'dict') else obj_in
        
        for field, value in obj_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        await self.db.commit()
        await self.db.refresh(db_obj)
        logger.info(f"更新{self.model.__name__}记录成功: ID={getattr(db_obj, 'id', 'N/A')}")
        return db_obj
    
    @handle_database_errors("删除记录")
    async def remove(self, id: Any) -> ModelType:
        """删除记录"""
        obj = await self.get_or_404(id)
        await self.db.delete(obj)
        await self.db.commit()
        logger.info(f"删除{self.model.__name__}记录成功: ID={id}")
        return obj
    
    @handle_database_errors("批量创建记录")
    async def bulk_create(self, objs_in: List[CreateSchemaType]) -> List[ModelType]:
        """批量创建记录"""
        if not objs_in:
            return []
        
        db_objs = []
        for obj_in in objs_in:
            obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in
            db_obj = self.model(**obj_data)
            db_objs.append(db_obj)
        
        self.db.add_all(db_objs)
        await self.db.commit()
        
        for db_obj in db_objs:
            await self.db.refresh(db_obj)
        
        logger.info(f"批量创建{self.model.__name__}记录成功: {len(db_objs)}条")
        return db_objs
    
    @handle_database_errors("根据条件查找记录")
    async def find_by(self, **kwargs) -> Optional[ModelType]:
        """根据条件查找单条记录"""
        query = select(self.model)
        
        for key, value in kwargs.items():
            if hasattr(self.model, key) and value is not None:
                query = query.where(getattr(self.model, key) == value)
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    @handle_database_errors("根据条件查找多条记录")
    async def find_all_by(self, **kwargs) -> List[ModelType]:
        """根据条件查找多条记录"""
        query = select(self.model)
        
        for key, value in kwargs.items():
            if hasattr(self.model, key) and value is not None:
                query = query.where(getattr(self.model, key) == value)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    @handle_database_errors("检查记录是否存在")
    async def exists(self, id: Any) -> bool:
        """检查记录是否存在"""
        obj = await self.get(id)
        return obj is not None
    
    @handle_database_errors("检查条件记录是否存在")
    async def exists_by(self, **kwargs) -> bool:
        """根据条件检查记录是否存在"""
        obj = await self.find_by(**kwargs)
        return obj is not None


class PaginationResult(BaseModel):
    """分页结果模型"""
    items: List[Any]
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool


class PaginatedRepository(BaseRepository[ModelType, CreateSchemaType, UpdateSchemaType]):
    """支持分页的Repository"""
    
    @handle_database_errors("分页查询")
    async def paginate(
        self,
        page: int = 1,
        page_size: int = 20,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None
    ) -> PaginationResult:
        """分页查询"""
        if page < 1:
            page = 1
        if page_size < 1:
            page_size = 20
        if page_size > 100:
            page_size = 100
        
        skip = (page - 1) * page_size
        
        # 获取总数
        total = await self.count(filters)
        
        # 获取数据
        items = await self.get_multi(skip=skip, limit=page_size, filters=filters, order_by=order_by)
        
        total_pages = (total + page_size - 1) // page_size
        
        return PaginationResult(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
