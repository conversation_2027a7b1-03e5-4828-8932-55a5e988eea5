"""
用户Repository
处理用户相关的数据访问操作
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel

from app.models.system.user import User
from app.repositories.base import PaginatedRepository
from app.utils.exception_decorators import handle_database_errors
from app.exceptions import ValidationException


class UserCreateSchema(BaseModel):
    """用户创建Schema"""
    username: str
    password: str
    nickname: Optional[str] = None
    remark: Optional[str] = None


class UserUpdateSchema(BaseModel):
    """用户更新Schema"""
    username: Optional[str] = None
    nickname: Optional[str] = None
    remark: Optional[str] = None
    status: Optional[int] = None


class UserRepository(PaginatedRepository[User, UserCreateSchema, UserUpdateSchema]):
    """用户Repository"""
    
    def __init__(self, db: AsyncSession):
        super().__init__(User, db)
    
    @handle_database_errors("根据用户名查找用户")
    async def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        if not username or not username.strip():
            raise ValidationException("用户名不能为空")
        
        result = await self.db.execute(
            select(User).where(User.username == username.strip())
        )
        return result.scalars().first()
    
    @handle_database_errors("检查用户名是否存在")
    async def username_exists(self, username: str, exclude_id: Optional[int] = None) -> bool:
        """检查用户名是否已存在"""
        if not username or not username.strip():
            return False
        
        query = select(User).where(User.username == username.strip())
        
        if exclude_id:
            query = query.where(User.id != exclude_id)
        
        result = await self.db.execute(query)
        user = result.scalars().first()
        return user is not None
    
    @handle_database_errors("获取活跃用户列表")
    async def get_active_users(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[User]:
        """获取活跃用户列表"""
        result = await self.db.execute(
            select(User)
            .where(User.status == 1)
            .offset(skip)
            .limit(limit)
            .order_by(User.created_at.desc())
        )
        return result.scalars().all()
    
    @handle_database_errors("统计活跃用户数量")
    async def count_active_users(self) -> int:
        """统计活跃用户数量"""
        from sqlalchemy import func
        result = await self.db.execute(
            select(func.count(User.id)).where(User.status == 1)
        )
        return result.scalar()
    
    @handle_database_errors("搜索用户")
    async def search_users(
        self,
        keyword: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """根据关键词搜索用户"""
        if not keyword or not keyword.strip():
            return await self.get_multi(skip=skip, limit=limit)
        
        keyword = f"%{keyword.strip()}%"
        result = await self.db.execute(
            select(User)
            .where(
                (User.username.like(keyword)) |
                (User.nickname.like(keyword))
            )
            .offset(skip)
            .limit(limit)
            .order_by(User.created_at.desc())
        )
        return result.scalars().all()
    
    @handle_database_errors("批量更新用户状态")
    async def bulk_update_status(self, user_ids: List[int], status: int) -> int:
        """批量更新用户状态"""
        if not user_ids:
            return 0
        
        from sqlalchemy import update
        result = await self.db.execute(
            update(User)
            .where(User.id.in_(user_ids))
            .values(status=status)
        )
        await self.db.commit()
        
        updated_count = result.rowcount
        return updated_count
    
    @handle_database_errors("获取用户统计信息")
    async def get_user_statistics(self) -> dict:
        """获取用户统计信息"""
        from sqlalchemy import func, case
        
        result = await self.db.execute(
            select(
                func.count(User.id).label('total_users'),
                func.sum(case((User.status == 1, 1), else_=0)).label('active_users'),
                func.sum(case((User.status == 0, 1), else_=0)).label('inactive_users')
            )
        )
        
        stats = result.first()
        return {
            'total_users': stats.total_users or 0,
            'active_users': stats.active_users or 0,
            'inactive_users': stats.inactive_users or 0
        }


def get_user_repository(db: AsyncSession) -> UserRepository:
    """获取用户Repository实例"""
    return UserRepository(db)
