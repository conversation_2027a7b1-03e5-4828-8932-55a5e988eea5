"""
任务结果明细API路由
支持分页查询各种任务的详细结果数据
"""

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from typing import Optional, List, Dict, Any
from pydantic import BaseModel

from app.database.session import get_db
from app.models.steel_making.common.task_result_details import (
    SteelCostVerificationDetail,
    DataComparisonDetail,
    MessageCheckDetail,
    IxReceivaDetail
)
from app.utils.response import success

router = APIRouter(prefix="/api/task-result-details", tags=["任务结果明细"])


class PaginationResponse(BaseModel):
    """分页响应模型"""
    total: int
    page: int
    page_size: int
    total_pages: int
    data: List[Dict[str, Any]]


@router.get("/steel-cost-verification/{task_id}")
async def get_steel_cost_verification_details(
    task_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    heat_no: Optional[str] = Query(None, description="炉号筛选"),
    material_code: Optional[str] = Query(None, description="物料编码筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取炼钢成本数据核对差异明细（分页）"""
    
    # 构建查询条件
    conditions = [SteelCostVerificationDetail.task_id == task_id]
    
    if heat_no:
        conditions.append(SteelCostVerificationDetail.heat_no.ilike(f"%{heat_no}%"))
    if material_code:
        conditions.append(SteelCostVerificationDetail.material_code.ilike(f"%{material_code}%"))
    if status:
        conditions.append(SteelCostVerificationDetail.status.ilike(f"%{status}%"))
    
    # 查询总数
    count_stmt = select(func.count(SteelCostVerificationDetail.id)).where(and_(*conditions))
    total_result = await db.execute(count_stmt)
    total = total_result.scalar()
    
    # 分页查询
    offset = (page - 1) * page_size
    stmt = select(SteelCostVerificationDetail).where(
        and_(*conditions)
    ).order_by(SteelCostVerificationDetail.id.desc()).offset(offset).limit(page_size)
    
    result = await db.execute(stmt)
    details = result.scalars().all()
    
    # 转换为字典
    data = []
    for detail in details:
        data.append({
            "id": detail.id,
            "heat_no": detail.heat_no,
            "material_code": detail.material_code,
            "oj_value": detail.oj_value,
            "mr_value": detail.mr_value,
            "ip_value": detail.ip_value,
            "status": detail.status,
            "description": detail.description,
            "created_at": detail.created_at.isoformat() if detail.created_at else None
        })
    
    total_pages = (total + page_size - 1) // page_size

    return success(PaginationResponse(
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages,
        data=data
    ))


@router.get("/data-comparison/{task_id}")
async def get_data_comparison_details(
    task_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    heat_no: Optional[str] = Query(None, description="炉号筛选"),
    material_code: Optional[str] = Query(None, description="物料编码筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    min_difference: Optional[float] = Query(None, description="最小差异值"),
    max_difference: Optional[float] = Query(None, description="最大差异值"),
    db: AsyncSession = Depends(get_db)
):
    """获取数据对比差异明细（分页）"""
    
    # 构建查询条件
    conditions = [DataComparisonDetail.task_id == task_id]
    
    if heat_no:
        conditions.append(DataComparisonDetail.heat_no.ilike(f"%{heat_no}%"))
    if material_code:
        conditions.append(DataComparisonDetail.material_code.ilike(f"%{material_code}%"))
    if status:
        conditions.append(DataComparisonDetail.status.ilike(f"%{status}%"))
    if min_difference is not None:
        conditions.append(DataComparisonDetail.difference >= min_difference)
    if max_difference is not None:
        conditions.append(DataComparisonDetail.difference <= max_difference)
    
    # 查询总数
    count_stmt = select(func.count(DataComparisonDetail.id)).where(and_(*conditions))
    total_result = await db.execute(count_stmt)
    total = total_result.scalar()
    
    # 分页查询
    offset = (page - 1) * page_size
    stmt = select(DataComparisonDetail).where(
        and_(*conditions)
    ).order_by(DataComparisonDetail.id.desc()).offset(offset).limit(page_size)
    
    result = await db.execute(stmt)
    details = result.scalars().all()
    
    # 转换为字典
    data = []
    for detail in details:
        data.append({
            "id": detail.id,
            "heat_no": detail.heat_no,
            "material_code": detail.material_code,
            "mes_value": detail.mes_value,
            "erp_value": detail.erp_value,
            "difference": detail.difference,
            "difference_rate": detail.difference_rate,
            "status": detail.status,
            "description": detail.description,
            "created_at": detail.created_at.isoformat() if detail.created_at else None
        })
    
    total_pages = (total + page_size - 1) // page_size

    return success(PaginationResponse(
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages,
        data=data
    ))


@router.get("/message-check/{task_id}")
async def get_message_check_details(
    task_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    error_type: Optional[str] = Query(None, description="错误类型筛选"),
    heat_no: Optional[str] = Query(None, description="炉号筛选"),
    material_code: Optional[str] = Query(None, description="物料编码筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取电文检查错误明细（分页）"""
    
    # 构建查询条件
    conditions = [MessageCheckDetail.task_id == task_id]
    
    if error_type:
        conditions.append(MessageCheckDetail.error_type.ilike(f"%{error_type}%"))
    if heat_no:
        conditions.append(MessageCheckDetail.heat_no.ilike(f"%{heat_no}%"))
    if material_code:
        conditions.append(MessageCheckDetail.material_code.ilike(f"%{material_code}%"))
    
    # 查询总数
    count_stmt = select(func.count(MessageCheckDetail.id)).where(and_(*conditions))
    total_result = await db.execute(count_stmt)
    total = total_result.scalar()
    
    # 分页查询
    offset = (page - 1) * page_size
    stmt = select(MessageCheckDetail).where(
        and_(*conditions)
    ).order_by(MessageCheckDetail.id.desc()).offset(offset).limit(page_size)
    
    result = await db.execute(stmt)
    details = result.scalars().all()
    
    # 转换为字典
    data = []
    for detail in details:
        data.append({
            "id": detail.id,
            "error_type": detail.error_type,
            "error_message": detail.error_message,
            "error_code": detail.error_code,
            "heat_no": detail.heat_no,
            "material_code": detail.material_code,
            "batch_no": detail.batch_no,
            "suggested_solution": detail.suggested_solution,
            "created_at": detail.created_at.isoformat() if detail.created_at else None
        })
    
    total_pages = (total + page_size - 1) // page_size

    return success(PaginationResponse(
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages,
        data=data
    ))


@router.get("/ix-receiva/{task_id}")
async def get_ix_receiva_details(
    task_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    system_id: Optional[str] = Query(None, description="系统ID筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    error_type: Optional[str] = Query(None, description="错误类型筛选"),
    batch_no: Optional[str] = Query(None, description="批次号筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取IX收账检查错误明细（分页）"""
    
    # 构建查询条件
    conditions = [IxReceivaDetail.task_id == task_id]
    
    if system_id:
        conditions.append(IxReceivaDetail.system_id == system_id)
    if status:
        conditions.append(IxReceivaDetail.status.ilike(f"%{status}%"))
    if error_type:
        conditions.append(IxReceivaDetail.error_type.ilike(f"%{error_type}%"))
    if batch_no:
        conditions.append(IxReceivaDetail.batch_no.ilike(f"%{batch_no}%"))
    
    # 查询总数
    count_stmt = select(func.count(IxReceivaDetail.id)).where(and_(*conditions))
    total_result = await db.execute(count_stmt)
    total = total_result.scalar()
    
    # 分页查询
    offset = (page - 1) * page_size
    stmt = select(IxReceivaDetail).where(
        and_(*conditions)
    ).order_by(IxReceivaDetail.id.desc()).offset(offset).limit(page_size)
    
    result = await db.execute(stmt)
    details = result.scalars().all()
    
    # 转换为字典
    data = []
    for detail in details:
        data.append({
            "id": detail.id,
            "system_id": detail.system_id,
            "batch_no": detail.batch_no,
            "status": detail.status,
            "error_type": detail.error_type,
            "error_message": detail.error_message,
            "duration_minutes": detail.duration_minutes,
            "suggested_sql": detail.suggested_sql,
            "check_time": detail.check_time.isoformat() if detail.check_time else None,
            "created_at": detail.created_at.isoformat() if detail.created_at else None
        })
    
    total_pages = (total + page_size - 1) // page_size

    return success(PaginationResponse(
        total=total,
        page=page,
        page_size=page_size,
        total_pages=total_pages,
        data=data
    ))
