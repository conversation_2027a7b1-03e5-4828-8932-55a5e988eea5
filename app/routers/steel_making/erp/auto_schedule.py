import asyncio
from fastapi import APIRouter, Depends, WebSocket
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.database.session import get_db
from app.services.steel_making.erp.auto_schedule import AutoScheduleService
from app.services.common.websocket_manager import websocket_manager
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.utils.response import success, fail
from app.utils.logger import logger
from app.utils.websocket_utils import generic_websocket_endpoint

router = APIRouter(prefix="/api/auto-schedule")


class AutoScheduleRequest(BaseModel):
    """自动排程请求"""
    task_type: str = "POSTING"


@router.post("/start")
async def start_auto_schedule(
    request: AutoScheduleRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动自动排程任务"""
    try:
        # 验证任务类型
        valid_types = ["POSTING", "IB_IX_IP", "IS_IX_IP"]
        if request.task_type not in valid_types:
            return fail(f"不支持的任务类型: {request.task_type}")

        service = AutoScheduleService(db)

        # 创建任务
        task_id = await service.create_schedule_task(user_id=current_user.id, task_type=request.task_type)
        
        # 定义进度回调函数
        async def progress_callback(task_id: int, progress: int, message: str, data=None):
            await websocket_manager.send_task_progress(
                task_id=task_id,
                progress=progress,
                message=message,
                data=data,  # 传递额外数据
                client_id=str(current_user.id)
            )
        
        # 在后台执行任务
        asyncio.create_task(
            execute_auto_schedule_task(
                task_id,
                progress_callback,
                current_user.id
            )
        )
        
        return success({
            "task_id": task_id,
            "task_type": request.task_type,
            "message": f"自动排程任务已启动 (类型: {request.task_type})"
        })
        
    except Exception as e:
        logger.error(f"启动自动排程任务失败: {e}")
        return fail(f"启动任务失败: {str(e)}")


async def execute_auto_schedule_task(
    task_id: int,
    progress_callback,
    user_id: int
):
    """后台执行自动排程任务"""
    try:
        # 创建新的数据库会话
        async for db in get_db():
            service = AutoScheduleService(db)
            await service.start_schedule_task(task_id, progress_callback)

            # 发送完成通知
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=True,
                result_summary="自动排程任务执行成功",
                client_id=str(user_id)
            )
            break

    except Exception as e:
        logger.error(f"后台任务执行失败: {e}")

        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"任务执行失败: {str(e)}",
            client_id=str(user_id)
        )


@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        service = AutoScheduleService(db)
        status = await service.get_task_status(task_id)
        
        if not status:
            return fail("任务不存在")
        
        return success(status)
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """自动排程WebSocket端点，用于实时通信"""
    await generic_websocket_endpoint(websocket, client_id, "自动排程")
