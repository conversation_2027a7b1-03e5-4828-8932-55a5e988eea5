"""
炉订号置换异常相关路由
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

from app.database.session import get_db
from app.services.steel_making.erp.heat_no_switch import HeatNoSwitchService
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.utils.response import success, fail
from app.utils.logger import logger

router = APIRouter(prefix="/api/heat-no-switch")

class HeatPlanMappingRequest(BaseModel):
    heat_ids: list[str]

class HeatStatusRequest(BaseModel):
    heat_nos: list[str]

class MessageAssistRequest(BaseModel):
    heat_no: Optional[str] = None
    check_date: Optional[str] = None  # 默认当天，格式：YYYY-MM-DD

@router.post("/heat-plan-mapping")
async def get_heat_plan_mapping(
    request: HeatPlanMappingRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """查询炉号和计划炉号对照关系"""
    try:
        logger.info(f"用户 {current_user.username} 查询炉号对照关系，炉号: {request.heat_ids}")

        service = HeatNoSwitchService(db)
        result = await service.get_heat_plan_mapping(request.heat_ids)
        
        return success(result)
        
    except Exception as e:
        logger.error(f"查询炉号对照关系失败: {e}")
        return fail(f"查询失败: {str(e)}")

@router.post("/heat-status")
async def get_heat_status(
    request: HeatStatusRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """查询炉号状态"""
    try:
        logger.info(f"用户 {current_user.username} 查询炉号状态，炉号: {request.heat_nos}")

        service = HeatNoSwitchService(db)
        result = await service.get_heat_status(request.heat_nos)
        
        return success(result)
        
    except Exception as e:
        logger.error(f"查询炉号状态失败: {e}")
        return fail(f"查询失败: {str(e)}")

@router.post("/message-assist")
async def get_message_assist(
    request: MessageAssistRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """电文辅助查询"""
    try:
        # 如果没有提供日期，使用当天
        check_date = request.check_date
        if not check_date:
            check_date = datetime.now().strftime("%Y-%m-%d")
        
        logger.info(f"用户 {current_user.username} 电文辅助查询，炉号: {request.heat_no}, 日期: {check_date}")
        
        service = HeatNoSwitchService(db)
        result = await service.get_message_assist(request.heat_no, check_date)
        
        return success(result)
        
    except Exception as e:
        logger.error(f"电文辅助查询失败: {e}")
        return fail(f"查询失败: {str(e)}")
