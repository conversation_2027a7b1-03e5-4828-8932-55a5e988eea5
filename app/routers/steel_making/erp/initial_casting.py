from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import List

from app.database.session import get_db
from app.models.system.user import User
from app.services.system.auth import get_current_user
from app.utils.logger import logger
from app.utils.response import success, fail
from app.constants.sql_templates import INITIAL_CASTING_SQL_TEMPLATE

router = APIRouter(prefix="/api/initial-casting")


class InitialCastingRequest(BaseModel):
    """增加期初连铸资料请求"""
    heat_no: str  # 炉号


class InitialCastingResponse(BaseModel):
    """增加期初连铸资料响应"""
    sql_statements: List[str]  # 生成的SQL语句
    heat_no: str  # 炉号
    generated_count: int  # 生成的SQL数量


@router.post("/generate-sql")
async def generate_initial_casting_sql(
    request: InitialCastingRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成期初连铸资料SQL语句"""
    try:
        heat_no = request.heat_no.strip()
        
        if not heat_no:
            return fail("炉号不能为空")
        
        logger.info(f"用户 {current_user.username} 请求生成期初连铸资料SQL，炉号: {heat_no}")

        sql_statements = []

        # 生成第一条SQL（使用原炉号）
        sql1 = INITIAL_CASTING_SQL_TEMPLATE.format(heat_no=heat_no)
        sql_statements.append(sql1)
        
        # 如果炉号长度大于9位，生成第二条SQL（移除最后一位）
        if len(heat_no) > 9:
            shortened_heat_no = heat_no[:-1]  # 移除最后一位
            sql2 = INITIAL_CASTING_SQL_TEMPLATE.format(heat_no=shortened_heat_no)
            sql_statements.append(sql2)
            logger.info(f"炉号长度 > 9位，生成了2条SQL语句，原炉号: {heat_no}, 缩短炉号: {shortened_heat_no}")
        else:
            logger.info(f"炉号长度 <= 9位，生成了1条SQL语句，炉号: {heat_no}")
        
        response_data = {
            "sql_statements": sql_statements,
            "heat_no": heat_no,
            "generated_count": len(sql_statements)
        }
        
        return success(response_data)
        
    except Exception as e:
        logger.error(f"生成期初连铸资料SQL失败: {e}")
        return fail(f"生成SQL失败: {str(e)}")
