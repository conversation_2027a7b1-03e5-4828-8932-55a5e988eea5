"""
IX批次检查相关路由
"""

import asyncio
from fastapi import APIRouter, Depends, WebSocket
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.session import get_db
from app.services.steel_making.erp.ix_batch_check import IxBatchCheckService
from app.services.common.websocket_manager import websocket_manager
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.utils.response import success, fail
from app.utils.logger import logger
from app.utils.websocket_utils import generic_websocket_endpoint

router = APIRouter(prefix="/api/ix-batch-check")

@router.post("/start")
async def start_ix_batch_check(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动IX批次检查任务"""
    try:
        logger.info(f"用户 {current_user.username} 启动IX批次检查")
        
        # 创建检查任务
        service = IxBatchCheckService(db)
        task_id = await service.create_check_task(user_id=current_user.id)
        
        # 定义进度回调函数
        async def progress_callback(task_id: int, progress: int, message: str, data: dict = None):
            await websocket_manager.send_task_progress(
                task_id=task_id,
                progress=progress,
                message=message,
                data=data,
                client_id=str(current_user.id)
            )
        
        # 在后台执行任务
        asyncio.create_task(
            execute_ix_batch_check_task(
                task_id,
                progress_callback,
                current_user.id
            )
        )
        
        return success({
            "task_id": task_id,
            "message": "IX批次检查任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动IX批次检查任务失败: {e}")
        return fail(f"启动任务失败: {str(e)}")


async def execute_ix_batch_check_task(
    task_id: int, 
    progress_callback, 
    user_id: int
):
    """后台执行IX批次检查任务"""
    try:
        # 创建新的数据库会话
        async for db in get_db():
            service = IxBatchCheckService(db)
            await service.start_check_task(task_id, progress_callback)
            
            # 发送完成通知
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=True,
                result_summary="IX批次检查任务执行成功",
                client_id=str(user_id)
            )
            break
        
    except Exception as e:
        logger.error(f"IX批次检查后台任务执行失败: {e}")
        
        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"IX批次检查任务执行失败: {str(e)}",
            client_id=str(user_id)
        )


@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        service = IxBatchCheckService(db)
        task_status = await service.get_task_status(task_id)
        
        if not task_status:
            return fail("任务不存在")
        
        return success(task_status)
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket端点"""
    await generic_websocket_endpoint(websocket, client_id, "ix_batch_check")
