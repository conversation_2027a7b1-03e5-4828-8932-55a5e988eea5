"""
IX收账作业检查路由
"""

import asyncio
from fastapi import APIRouter, Depends, WebSocket, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime, date

from app.database.session import get_db
from app.services.steel_making.erp.ix_receiva import IxReceivaService
from app.services.common.websocket_manager import websocket_manager
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.utils.response import success, fail
from app.utils.logger import logger
from app.utils.websocket_utils import generic_websocket_endpoint

router = APIRouter(prefix="/api/ix-receiva")


class IxReceivaCheckRequest(BaseModel):
    """IX收账作业检查请求"""
    system_id: str  # 'IB' 或 'IS'
    start_date: str  # 开始日期 YYYYMMDD
    end_date: str    # 结束日期 YYYYMMDD
    status_filter: Optional[List[str]] = None  # 状态筛选 ['error', 'processing', 'ip_error']


class IxReceivaAutoCheckRequest(BaseModel):
    """IX收账作业自动检查请求"""
    min_duration_minutes: int = 3  # 最小耗时分钟数，默认3分钟


@router.post("/check")
async def start_ix_receiva_check(
    request: IxReceivaCheckRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动IX收账作业检查"""
    try:
        # 验证系统ID
        if request.system_id not in ['IB', 'IS']:
            return fail("系统ID必须是IB或IS")
        
        # 验证日期格式
        try:
            datetime.strptime(request.start_date, "%Y%m%d")
            datetime.strptime(request.end_date, "%Y%m%d")
        except ValueError:
            return fail("日期格式错误，请使用YYYYMMDD格式")
        
        # 验证日期范围
        if request.start_date > request.end_date:
            return fail("开始日期不能大于结束日期")
        
        logger.info(f"用户 {current_user.username} 启动IX收账作业检查，系统: {request.system_id}, 日期: {request.start_date}-{request.end_date}")
        
        # 创建检查任务
        service = IxReceivaService(db)
        task_id = await service.create_check_task(
            system_id=request.system_id,
            start_date=request.start_date,
            end_date=request.end_date,
            status_filter=request.status_filter,
            user_id=current_user.id
        )
        
        # 启动后台任务
        asyncio.create_task(
            execute_ix_receiva_check_task(task_id, current_user.id)
        )

        # 立即发送任务启动通知
        asyncio.create_task(
            websocket_manager.send_task_progress(
                task_id=task_id,
                progress=0,
                message="IX收账作业检查任务已启动，正在初始化...",
                client_id=str(current_user.id)
            )
        )

        return success({
            "task_id": task_id,
            "message": "IX收账作业检查任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动IX收账作业检查失败: {e}")
        return fail(f"启动IX收账作业检查失败: {str(e)}")


@router.post("/auto-check")
async def start_ix_receiva_auto_check(
    request: IxReceivaAutoCheckRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动IX收账作业自动检查（定时任务）"""
    try:
        logger.info(f"用户 {current_user.username} 启动IX收账作业自动检查，最小耗时: {request.min_duration_minutes}分钟")
        
        # 获取当前日期
        today = datetime.now().strftime("%Y%m%d")
        
        # 为IB和IS系统分别创建检查任务
        task_ids = []
        for system_id in ['IB', 'IS']:
            service = IxReceivaService(db)
            task_id = await service.create_check_task(
                system_id=system_id,
                start_date=today,
                end_date=today,
                status_filter=['error', 'processing', 'ip_error'],
                user_id=current_user.id,
                is_auto_task=True,
                min_duration_minutes=request.min_duration_minutes
            )
            task_ids.append(task_id)
            
            # 启动后台任务
            asyncio.create_task(
                execute_ix_receiva_check_task(task_id, current_user.id)
            )
        
        return success({
            "task_ids": task_ids,
            "message": "IX收账作业自动检查任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动IX收账作业自动检查失败: {e}")
        return fail(f"启动IX收账作业自动检查失败: {str(e)}")


async def execute_ix_receiva_check_task(task_id: int, user_id: int):
    """后台执行IX收账作业检查任务"""
    from app.database.session import SessionLocal

    try:
        logger.info(f"开始执行IX收账作业检查后台任务，任务ID: {task_id}, 用户ID: {user_id}")

        # 创建新的数据库会话
        async with SessionLocal() as db:
            service = IxReceivaService(db)

            # 定义进度回调函数
            async def progress_callback(progress: int, message: str):
                logger.info(f"任务进度更新 - 任务ID: {task_id}, 进度: {progress}%, 消息: {message}")
                await websocket_manager.send_task_progress(
                    task_id=task_id,
                    progress=progress,
                    message=message,
                    client_id=str(user_id)
                )

            # 执行检查任务
            await service.start_check_task(task_id, progress_callback)

            # 获取检查结果
            results = await service.get_task_results(task_id)

            # 发送完成通知
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=True,
                result_summary="IX收账作业检查任务执行成功",
                data=results,
                client_id=str(user_id)
            )
        
    except Exception as e:
        logger.error(f"IX收账作业检查后台任务执行失败: {e}")
        
        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"IX收账作业检查任务执行失败: {str(e)}",
            client_id=str(user_id)
        )


@router.get("/result/{task_id}")
async def get_ix_receiva_check_result(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取IX收账作业检查结果"""
    try:
        service = IxReceivaService(db)
        results = await service.get_task_results(task_id)
        return success(results)
        
    except Exception as e:
        logger.error(f"获取IX收账作业检查结果失败: {e}")
        return fail(f"获取IX收账作业检查结果失败: {str(e)}")


@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        service = IxReceivaService(db)
        task_status = await service.get_task_status(task_id)
        
        if not task_status:
            return fail("任务不存在")
        
        return success(task_status)
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.get("/history")
async def get_ix_receiva_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    system_id: Optional[str] = Query(None, description="系统ID"),
    status: Optional[str] = Query(None, description="任务状态"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取IX收账作业检查历史记录"""
    try:
        service = IxReceivaService(db)

        # 构建查询条件
        filters = {}
        if start_date:
            filters['start_date'] = start_date.strftime('%Y%m%d')
        if end_date:
            filters['end_date'] = end_date.strftime('%Y%m%d')
        if system_id:
            filters['system_id'] = system_id
        if status:
            filters['status'] = status

        # 获取历史记录
        history_data = await service.get_history(
            page=page,
            size=size,
            filters=filters
        )

        return success(history_data)

    except Exception as e:
        logger.error(f"获取IX收账作业检查历史失败: {e}")
        return fail(f"获取IX收账作业检查历史失败: {str(e)}")


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """IX收账作业检查WebSocket端点，用于实时通信"""
    logger.info(f"IX收账作业检查WebSocket连接请求，客户端ID: {client_id}")
    await generic_websocket_endpoint(websocket, client_id, "IX收账作业检查")
