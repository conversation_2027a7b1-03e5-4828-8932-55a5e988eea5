"""
维护任务历史查询路由
"""

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, and_, or_
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime, date, timedelta

from app.database.session import get_db
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.models.steel_making.erp.data_comparison import DataComparisonTask
from app.models.steel_making.mes.message_check import MessageCheckTask
from app.models.steel_making.mes.mes_maintenance import MESMaintenanceTask
from app.models.steel_making.erp.auto_schedule import AutoScheduleTask
from app.models.steel_making.erp.ix_receiva import IxReceivaTask
from app.models.steel_making.erp.steel_cost_data_verification import SteelCostDataVerificationTask
from app.models.steel_making.erp.ix_batch_check import IxBatchCheckTask
from app.utils.response import success, fail
from app.utils.logger import logger

router = APIRouter(prefix="/api/maintenance-history")


class TaskHistoryQuery(BaseModel):
    """任务历史查询参数"""
    task_type: Optional[str] = None  # 任务类型
    status: Optional[str] = None     # 任务状态
    start_date: Optional[date] = None  # 开始日期
    end_date: Optional[date] = None    # 结束日期
    page: int = 1                    # 页码
    page_size: int = 20              # 每页大小


class TaskHistoryItem(BaseModel):
    """任务历史项"""
    id: int
    task_name: str
    task_type: str
    status: str
    result_status: str  # 结果状态：有异常/无异常
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    created_by: Optional[int]
    created_by_name: Optional[str]
    duration: Optional[float]  # 执行时长（秒）

    # 炼钢成本数据核对特有字段
    start_date: Optional[str] = None  # 开始时间
    end_date: Optional[str] = None    # 结束时间
    factory: Optional[str] = None
    heat_no: Optional[str] = None
    material_code: Optional[str] = None


class TaskHistoryResponse(BaseModel):
    """任务历史响应"""
    items: List[TaskHistoryItem]
    total: int
    page: int
    page_size: int
    total_pages: int


@router.get("/data-comparison")
async def get_data_comparison_history(
    task_type: Optional[str] = Query(None, description="任务类型"),
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取数据对比任务历史"""
    try:
        # 构建查询条件
        conditions = []
        
        if status:
            conditions.append(DataComparisonTask.status == status)
        
        if start_date:
            conditions.append(DataComparisonTask.created_at >= start_date)
        
        if end_date:
            conditions.append(DataComparisonTask.created_at < end_date + timedelta(days=1))
        
        # 查询总数
        count_stmt = select(func.count(DataComparisonTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))
        
        count_result = await db.execute(count_stmt)
        total = count_result.scalar()
        
        # 查询数据
        stmt = select(DataComparisonTask).order_by(desc(DataComparisonTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(stmt)
        tasks = result.scalars().all()
        
        # 获取所有创建人ID
        creator_ids = [task.created_by for task in tasks if task.created_by and task.created_by != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 获取创建人名称
            created_by_name = None
            if task.created_by == 0:
                created_by_name = "系统"
            elif task.created_by and task.created_by in user_map:
                created_by_name = user_map[task.created_by]
            elif task.created_by:
                created_by_name = f"用户{task.created_by}"

            # 判断结果状态：有差异记录时为"有异常"
            result_status = "无异常"
            if task.difference_count and task.difference_count > 0:
                result_status = "有异常"
            elif task.status.value == "执行失败" if hasattr(task.status, 'value') else str(task.status) == "执行失败":
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type="数据对比",
                status=task.status.value if hasattr(task.status, 'value') else str(task.status),
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.created_by,
                created_by_name=created_by_name,
                duration=duration,
                # 数据对比特有字段
                start_date=task.start_date,
                end_date=task.end_date,
                factory=task.factory.value if hasattr(task.factory, 'value') else str(task.factory) if task.factory else None,
                heat_no=task.heat_no,
                material_code=task.material_code
            ))
        
        total_pages = (total + page_size - 1) // page_size
        
        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))
        
    except Exception as e:
        logger.error(f"获取数据对比历史失败: {e}")
        return fail(f"获取历史失败: {str(e)}")


@router.get("/erp-message-check")
async def get_erp_message_check_history(
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取ERP电文检查任务历史"""
    try:
        # 构建查询条件
        conditions = [
            MessageCheckTask.task_name.like('%ERP%')  # 只查询ERP相关的任务
        ]

        if status:
            conditions.append(MessageCheckTask.status == status)

        if start_date:
            conditions.append(MessageCheckTask.created_at >= start_date)

        if end_date:
            conditions.append(MessageCheckTask.created_at < end_date + timedelta(days=1))
        
        # 查询总数
        count_stmt = select(func.count(MessageCheckTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))
        
        count_result = await db.execute(count_stmt)
        total = count_result.scalar()
        
        # 查询数据
        stmt = select(MessageCheckTask).order_by(desc(MessageCheckTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(stmt)
        tasks = result.scalars().all()
        
        # 获取所有创建人ID
        creator_ids = [task.created_by for task in tasks if task.created_by and task.created_by != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 获取创建人名称
            created_by_name = None
            if task.created_by == 0:
                created_by_name = "系统"
            elif task.created_by and task.created_by in user_map:
                created_by_name = user_map[task.created_by]
            elif task.created_by:
                created_by_name = f"用户{task.created_by}"

            # 判断结果状态：有错误电文时为"有异常"
            result_status = "无异常"
            if task.error_count and task.error_count > 0:
                result_status = "有异常"
            elif task.status.value == "执行失败" if hasattr(task.status, 'value') else str(task.status) == "执行失败":
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type="ERP电文检查",
                status=task.status.value if hasattr(task.status, 'value') else str(task.status),
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.created_by,
                created_by_name=created_by_name,
                duration=duration
            ))
        
        total_pages = (total + page_size - 1) // page_size
        
        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))
        
    except Exception as e:
        logger.error(f"获取ERP电文检查历史失败: {e}")
        return fail(f"获取历史失败: {str(e)}")


@router.get("/mes-message-check")
async def get_mes_message_check_history(
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取MES电文检查任务历史"""
    try:
        # 构建查询条件
        conditions = [
            MessageCheckTask.task_name.like('%MES%')  # 只查询MES相关的任务
        ]

        if status:
            conditions.append(MessageCheckTask.status == status)

        if start_date:
            conditions.append(MessageCheckTask.created_at >= start_date)

        if end_date:
            conditions.append(MessageCheckTask.created_at < end_date + timedelta(days=1))

        # 查询总数
        count_stmt = select(func.count(MessageCheckTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))

        count_result = await db.execute(count_stmt)
        total = count_result.scalar()

        # 查询数据
        stmt = select(MessageCheckTask).order_by(desc(MessageCheckTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.offset((page - 1) * page_size).limit(page_size)

        result = await db.execute(stmt)
        tasks = result.scalars().all()
        
        # 获取所有创建人ID
        creator_ids = [task.created_by for task in tasks if task.created_by and task.created_by != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 获取创建人名称
            created_by_name = None
            if task.created_by == 0:
                created_by_name = "系统"
            elif task.created_by and task.created_by in user_map:
                created_by_name = user_map[task.created_by]
            elif task.created_by:
                created_by_name = f"用户{task.created_by}"

            # 判断结果状态：有错误电文时为"有异常"
            result_status = "无异常"
            if task.error_count and task.error_count > 0:
                result_status = "有异常"
            elif task.status.value == "执行失败" if hasattr(task.status, 'value') else str(task.status) == "执行失败":
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type="MES电文检查",
                status=task.status.value if hasattr(task.status, 'value') else str(task.status),
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.created_by,
                created_by_name=created_by_name,
                duration=duration
            ))
        
        total_pages = (total + page_size - 1) // page_size
        
        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))
        
    except Exception as e:
        logger.error(f"获取MES电文检查历史失败: {e}")
        return fail(f"获取历史失败: {str(e)}")


@router.get("/mes-maintenance")
async def get_mes_maintenance_history(
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取MES维护任务历史"""
    try:
        # 构建查询条件
        conditions = []
        
        if status:
            conditions.append(MESMaintenanceTask.status == status)
        
        if start_date:
            conditions.append(MESMaintenanceTask.created_at >= start_date)
        
        if end_date:
            conditions.append(MESMaintenanceTask.created_at < end_date + timedelta(days=1))
        
        # 查询总数
        count_stmt = select(func.count(MESMaintenanceTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))
        
        count_result = await db.execute(count_stmt)
        total = count_result.scalar()
        
        # 查询数据
        stmt = select(MESMaintenanceTask).order_by(desc(MESMaintenanceTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(stmt)
        tasks = result.scalars().all()
        
        # 获取所有创建人ID
        creator_ids = [task.created_by for task in tasks if task.created_by and task.created_by != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 获取任务类型显示名称
            task_type_map = {
                "PRODRES_SUMMARY": "钢坯产量调拨汇总报表",
                "BOF_SCRAP_STEEL_CLEAR": "转炉无法选择废钢号",
                "SCRAP_STEEL_FIX": "转炉废钢号修复",
                "OTHER": "其他维护任务"
            }

            # 获取创建人名称
            created_by_name = None
            if task.created_by == 0:
                created_by_name = "系统"
            elif task.created_by and task.created_by in user_map:
                created_by_name = user_map[task.created_by]
            elif task.created_by:
                created_by_name = f"用户{task.created_by}"

            # 判断结果状态：不是已完成状态时为"有异常"
            result_status = "无异常"
            if task.status.value != "已完成":
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type=task_type_map.get(task.task_type.value, task.task_type.value),
                status=task.status.value,
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.created_by,
                created_by_name=created_by_name,
                duration=duration
            ))
        
        total_pages = (total + page_size - 1) // page_size
        
        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))
        
    except Exception as e:
        logger.error(f"获取MES维护历史失败: {e}")
        return fail(f"获取历史失败: {str(e)}")


@router.get("/auto-schedule")
async def get_auto_schedule_history(
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取自动排程任务历史"""
    try:
        # 构建查询条件
        conditions = []

        if status:
            conditions.append(AutoScheduleTask.status == status)

        if start_date:
            conditions.append(AutoScheduleTask.created_at >= start_date)

        if end_date:
            conditions.append(AutoScheduleTask.created_at < end_date + timedelta(days=1))

        # 查询总数
        count_stmt = select(func.count(AutoScheduleTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))

        count_result = await db.execute(count_stmt)
        total = count_result.scalar()

        # 查询数据
        stmt = select(AutoScheduleTask).order_by(desc(AutoScheduleTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.offset((page - 1) * page_size).limit(page_size)

        result = await db.execute(stmt)
        tasks = result.scalars().all()

        # 获取所有创建人ID
        creator_ids = [task.created_by for task in tasks if task.created_by and task.created_by != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 获取创建人名称
            created_by_name = None
            if task.created_by == 0:
                created_by_name = "系统"
            elif task.created_by and task.created_by in user_map:
                created_by_name = user_map[task.created_by]
            elif task.created_by:
                created_by_name = f"用户{task.created_by}"

            # 判断结果状态：不是已完成状态时为"有异常"
            status_value = task.status.value if hasattr(task.status, 'value') else str(task.status)
            result_status = "无异常"
            if status_value != "已完成":
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type="自动排程",
                status=status_value,
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.created_by,
                created_by_name=created_by_name,
                duration=duration
            ))

        total_pages = (total + page_size - 1) // page_size

        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取自动排程历史失败: {e}")
        return fail(f"获取历史失败: {str(e)}")


@router.get("/scheduler/status")
async def get_scheduler_status(
    current_user: User = Depends(get_current_user)
):
    """获取调度器状态"""
    try:
        from app.services.common.scheduler import maintenance_scheduler
        status = maintenance_scheduler.get_job_status()
        return success(status)
    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return fail(f"获取调度器状态失败: {str(e)}")


@router.get("/ix-receiva")
async def get_ix_receiva_history(
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取IX收账作业检查任务历史"""
    try:
        # 构建查询条件
        conditions = []

        if status:
            if status == 'success':
                conditions.append(and_(
                    IxReceivaTask.status == 'completed',
                    IxReceivaTask.error_count == 0,
                    IxReceivaTask.processing_count == 0,
                    IxReceivaTask.ip_error_count == 0
                ))
            elif status == 'warning':
                conditions.append(and_(
                    IxReceivaTask.status == 'completed',
                    or_(
                        IxReceivaTask.error_count > 0,
                        IxReceivaTask.processing_count > 0,
                        IxReceivaTask.ip_error_count > 0
                    )
                ))
            elif status == 'error':
                conditions.append(IxReceivaTask.status == 'failed')
            else:
                conditions.append(IxReceivaTask.status == status)

        if start_date:
            conditions.append(IxReceivaTask.created_at >= start_date)

        if end_date:
            conditions.append(IxReceivaTask.created_at < end_date + timedelta(days=1))

        # 查询总数
        count_stmt = select(func.count(IxReceivaTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))

        count_result = await db.execute(count_stmt)
        total = count_result.scalar()

        # 查询数据
        stmt = select(IxReceivaTask).order_by(desc(IxReceivaTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.offset((page - 1) * page_size).limit(page_size)

        result = await db.execute(stmt)
        tasks = result.scalars().all()

        # 获取所有创建人ID
        creator_ids = [task.user_id for task in tasks if task.user_id and task.user_id != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 确定任务状态
            if task.status == 'failed':
                task_status = 'error'
            elif task.error_count > 0 or task.processing_count > 0 or task.ip_error_count > 0:
                task_status = 'warning'
            else:
                task_status = 'success'

            # 获取创建人名称
            created_by_name = None
            if task.user_id == 0:
                created_by_name = "系统"
            elif task.user_id and task.user_id in user_map:
                created_by_name = user_map[task.user_id]
            elif task.user_id:
                created_by_name = f"用户{task.user_id}"

            # 判断结果状态：有收账错误、收账处理中、抛IP错误、耗时过长时为"有异常"
            result_status = "无异常"
            if (task.error_count > 0 or task.processing_count > 0 or
                task.ip_error_count > 0 or task.status == 'failed'):
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type="IX收账作业检查",
                status=task_status,
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.user_id,
                created_by_name=created_by_name,
                duration=duration
            ))

        total_pages = (total + page_size - 1) // page_size

        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取IX收账作业检查历史失败: {e}")
        return fail(f"获取历史失败: {str(e)}")


@router.get("/steel-cost-data-verification")
async def get_steel_cost_data_verification_history(
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取炼钢成本数据核对任务历史"""
    try:
        # 构建查询条件
        conditions = []

        if status:
            conditions.append(SteelCostDataVerificationTask.status == status)

        if start_date:
            conditions.append(SteelCostDataVerificationTask.created_at >= start_date)

        if end_date:
            conditions.append(SteelCostDataVerificationTask.created_at < end_date + timedelta(days=1))

        # 查询总数
        count_stmt = select(func.count(SteelCostDataVerificationTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))

        count_result = await db.execute(count_stmt)
        total = count_result.scalar()

        # 查询数据
        stmt = select(SteelCostDataVerificationTask).order_by(desc(SteelCostDataVerificationTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.offset((page - 1) * page_size).limit(page_size)

        result = await db.execute(stmt)
        tasks = result.scalars().all()

        # 获取所有创建人ID
        creator_ids = [task.created_by for task in tasks if task.created_by and task.created_by != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 获取创建人名称
            created_by_name = None
            if task.created_by == 0:
                created_by_name = "系统"
            elif task.created_by and task.created_by in user_map:
                created_by_name = user_map[task.created_by]
            elif task.created_by:
                created_by_name = f"用户{task.created_by}"

            # 判断结果状态：有差异记录时为"有异常"
            result_status = "无异常"
            if task.difference_count and task.difference_count > 0:
                result_status = "有异常"
            elif task.status.value == "执行失败" if hasattr(task.status, 'value') else str(task.status) == "执行失败":
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type="炼钢成本数据核对",
                status=task.status.value if hasattr(task.status, 'value') else str(task.status),
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.created_by,
                created_by_name=created_by_name,
                duration=duration,
                # 炼钢成本数据核对特有字段
                start_date=task.start_date,
                end_date=task.end_date,
                factory=task.factory.value if hasattr(task.factory, 'value') else str(task.factory) if task.factory else None,
                heat_no=task.heat_no,
                material_code=task.material_code
            ))

        total_pages = (total + page_size - 1) // page_size

        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取炼钢成本数据核对历史失败: {e}")
        return fail(f"获取历史失败: {str(e)}")


@router.get("/ix-batch-check")
async def get_ix_batch_check_history(
    status: Optional[str] = Query(None, description="任务状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取IX批次检查任务历史"""
    try:
        # 构建查询条件
        conditions = []

        if status:
            conditions.append(IxBatchCheckTask.status == status)

        if start_date:
            conditions.append(IxBatchCheckTask.created_at >= start_date)

        if end_date:
            conditions.append(IxBatchCheckTask.created_at < end_date + timedelta(days=1))

        # 查询总数
        count_stmt = select(func.count(IxBatchCheckTask.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))

        count_result = await db.execute(count_stmt)
        total = count_result.scalar()

        # 查询数据
        stmt = select(IxBatchCheckTask).order_by(desc(IxBatchCheckTask.created_at))
        if conditions:
            stmt = stmt.where(and_(*conditions))

        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        result = await db.execute(stmt)
        tasks = result.scalars().all()

        # 获取所有创建人ID
        creator_ids = [task.created_by for task in tasks if task.created_by and task.created_by != 0]

        # 查询用户信息
        user_map = {}
        if creator_ids:
            user_result = await db.execute(
                select(User.id, User.username).where(User.id.in_(creator_ids))
            )
            users = user_result.all()
            user_map = {user.id: user.username for user in users}

        # 转换为响应格式
        items = []
        for task in tasks:
            duration = None
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()

            # 获取创建人名称
            created_by_name = None
            if task.created_by == 0:
                created_by_name = "系统"
            elif task.created_by and task.created_by in user_map:
                created_by_name = user_map[task.created_by]
            elif task.created_by:
                created_by_name = f"用户{task.created_by}"

            # 判断结果状态：有错误记录时为"有异常"
            result_status = "无异常"
            if task.error_count and task.error_count > 0:
                result_status = "有异常"
            elif task.status.value == "执行失败" if hasattr(task.status, 'value') else str(task.status) == "执行失败":
                result_status = "有异常"

            items.append(TaskHistoryItem(
                id=task.id,
                task_name=task.task_name,
                task_type="IX批次检查",
                status=task.status.value if hasattr(task.status, 'value') else str(task.status),
                result_status=result_status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at,
                error_message=task.error_message,
                created_by=task.created_by,
                created_by_name=created_by_name,
                duration=duration
            ))

        total_pages = (total + page_size - 1) // page_size

        return success(TaskHistoryResponse(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        ))

    except Exception as e:
        logger.error(f"获取IX批次检查任务历史失败: {e}")
        return fail(f"获取数据失败: {str(e)}")


@router.get("/task/{task_id}/details")
async def get_task_details(
    task_id: int,
    task_type: str = Query(..., description="任务类型"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务详情"""
    try:
        task = None
        task_model = None

        # 根据任务类型选择对应的模型
        if task_type == "数据对比":
            task_model = DataComparisonTask
        elif task_type in ["电文检查", "ERP电文检查", "MES电文检查"]:
            task_model = MessageCheckTask
        elif task_type in ["MES维护", "钢坯产量调拨汇总报表", "转炉无法选择废钢号", "转炉废钢号修复", "其他维护任务"]:
            task_model = MESMaintenanceTask
        elif task_type == "自动排程":
            task_model = AutoScheduleTask
        elif task_type == "IX收账作业检查":
            task_model = IxReceivaTask
        elif task_type == "炼钢成本数据核对":
            task_model = SteelCostDataVerificationTask
        elif task_type == "IX批次检查":
            task_model = IxBatchCheckTask
        else:
            return fail(f"不支持的任务类型: {task_type}")

        # 查询任务详情
        stmt = select(task_model).where(task_model.id == task_id)
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            return fail("任务不存在")

        # 获取创建人信息
        created_by_name = None
        user_id = getattr(task, 'created_by', None) or getattr(task, 'user_id', None)

        if user_id == 0:
            created_by_name = "系统"
        elif user_id:
            user_result = await db.execute(
                select(User.username).where(User.id == user_id)
            )
            user = user_result.scalar_one_or_none()
            created_by_name = user if user else f"用户{user_id}"

        # 计算执行时长
        duration = None
        if task.started_at and task.completed_at:
            duration = (task.completed_at - task.started_at).total_seconds()

        # 构建详情响应
        details = {
            "id": task.id,
            "task_name": task.task_name,
            "task_type": task_type,
            "status": task.status.value if hasattr(task.status, 'value') else str(task.status),
            "progress": task.progress,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "error_message": getattr(task, 'error_message', None),
            "created_by": user_id,
            "created_by_name": created_by_name,
            "duration": duration
        }

        # 添加特定任务类型的详细信息
        if task_type == "数据对比":
            comparison_result = getattr(task, 'comparison_result', None)
            logger.info(f"数据对比任务 {task_id} 的comparison_result: {type(comparison_result)}, 数据大小: {len(str(comparison_result)) if comparison_result else 0}")

            details.update({
                "start_date": task.start_date,
                "end_date": task.end_date,
                "factory": task.factory.value if hasattr(task.factory, 'value') else str(task.factory),
                "heat_no": getattr(task, 'heat_no', None),
                "material_code": getattr(task, 'material_code', None),
                "total_records": task.total_records,
                "processed_records": task.processed_records,
                "difference_count": task.difference_count,
                "comparison_result": comparison_result
            })
        elif task_type == "炼钢成本数据核对":
            verification_result = getattr(task, 'verification_result', None)
            logger.info(f"炼钢成本数据核对任务 {task_id} 的verification_result: {type(verification_result)}, 数据大小: {len(str(verification_result)) if verification_result else 0}")

            details.update({
                "check_date": task.check_date,
                "factory": task.factory.value if hasattr(task.factory, 'value') else str(task.factory),
                "heat_no": task.heat_no,
                "material_code": task.material_code,
                "total_records": task.total_records,
                "processed_records": task.processed_records,
                "difference_count": task.difference_count,
                "verification_result": verification_result,
                "result_summary": task.result_summary
            })
        elif task_type in ["电文检查", "ERP电文检查", "MES电文检查"]:
            details.update({
                "check_date": getattr(task, 'check_date', None),
                "message_type": getattr(task, 'message_type', {}).value if hasattr(getattr(task, 'message_type', {}), 'value') else str(getattr(task, 'message_type', '')),
                "total_records": getattr(task, 'total_records', 0),
                "processed_records": getattr(task, 'total_records', 0),  # MessageCheckTask没有processed_records字段
                "error_count": getattr(task, 'error_count', 0),
                "check_result": getattr(task, 'check_result', None)
            })
        elif task_type == "IX收账作业检查":
            details.update({
                "start_date": getattr(task, 'start_date', None),
                "end_date": getattr(task, 'end_date', None),
                "system_id": getattr(task, 'system_id', None),
                "total_records": getattr(task, 'total_count', 0),
                "processed_records": getattr(task, 'total_count', 0),
                "error_count": getattr(task, 'error_count', 0),
                "processing_count": getattr(task, 'processing_count', 0),
                "ip_error_count": getattr(task, 'ip_error_count', 0),
                "check_result": getattr(task, 'result_data', None)
            })
        elif task_type in ["MES维护", "钢坯产量调拨汇总报表", "转炉无法选择废钢号", "转炉废钢号修复", "其他维护任务"]:
            details.update({
                "maintenance_type": getattr(task, 'maintenance_type', None),
                "target_date": getattr(task, 'target_date', None),
                "heat_id": getattr(task, 'heat_id', None),
                "result_data": getattr(task, 'result_data', None)
            })
        elif task_type == "自动排程":
            details.update({
                "schedule_type": getattr(task, 'schedule_type', None),
                "target_date": getattr(task, 'target_date', None),
                "total_records": task.total_records,
                "processed_records": task.processed_records,
                "success_count": getattr(task, 'success_count', 0),
                "schedule_result": getattr(task, 'schedule_result', None)
            })
        elif task_type == "IX批次检查":
            details.update({
                "error_count": task.error_count,
                "check_result": task.check_result
            })

        return success(details)

    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        return fail(f"获取任务详情失败: {str(e)}")
