from fastapi import APIRouter, Depends, WebSocket, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.database.session import get_db
from app.models.common import MessageType
from app.services.steel_making.mes.message_check import MessageCheckService
from app.services.common.websocket_manager import websocket_manager
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.utils.response import success, fail
from app.utils.logger import logger
from app.utils.websocket_utils import generic_websocket_endpoint

router = APIRouter(prefix="/api/message-check")


class MessageCheckRequest(BaseModel):
    """电文检查请求"""
    check_date: str  # 检查日期，格式：YYYY-MM-DD
    message_type: str  # 电文类型：receive/send


@router.post("/start")
async def start_message_check(
    request: MessageCheckRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动电文检查任务"""
    try:
        # 验证电文类型
        if request.message_type.lower() == "receive":
            message_type = MessageType.RECEIVE
        elif request.message_type.lower() == "send":
            message_type = MessageType.SEND
        else:
            return fail("电文类型必须是 'receive' 或 'send'")
        
        service = MessageCheckService(db)
        
        # 创建任务
        task_id = await service.create_message_check_task(
            check_date=request.check_date,
            message_type=message_type,
            user_id=current_user.id
        )
        
        # 定义进度回调函数
        async def progress_callback(task_id: int, progress: int, message: str, data=None):
            await websocket_manager.send_task_progress(
                task_id=task_id,
                progress=progress,
                message=message,
                data=data,  # 传递额外数据
                client_id=str(current_user.id)
            )
        
        # 在后台执行任务
        background_tasks.add_task(
            execute_message_check_task,
            task_id,
            message_type,
            progress_callback,
            current_user.id,
            db
        )
        
        return success({
            "task_id": task_id,
            "message": "电文检查任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动电文检查任务失败: {e}")
        return fail(f"启动任务失败: {str(e)}")


async def execute_message_check_task(
    task_id: int, 
    message_type: MessageType, 
    progress_callback, 
    user_id: int,
    db: AsyncSession
):
    """后台执行电文检查任务"""
    try:
        service = MessageCheckService(db)
        await service.start_message_check_task(task_id, message_type, progress_callback)
        
        # 发送完成通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=True,
            result_summary="电文检查任务执行成功",
            client_id=str(user_id)
        )
        
    except Exception as e:
        logger.error(f"后台任务执行失败: {e}")
        
        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"任务执行失败: {str(e)}",
            client_id=str(user_id)
        )


@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        service = MessageCheckService(db)
        status = await service.get_task_status(task_id)
        
        if not status:
            return fail("任务不存在")
        
        return success(status)
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """电文检查WebSocket端点，用于实时通信"""
    await generic_websocket_endpoint(websocket, client_id, "电文检查")
