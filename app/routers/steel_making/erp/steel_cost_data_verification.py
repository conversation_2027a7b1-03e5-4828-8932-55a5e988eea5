from fastapi import APIRouter, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional

from app.database.session import get_db
from app.models.system.user import User
from app.models.common import Factory
from app.services.steel_making.erp.steel_cost_data_verification import SteelCostDataVerificationService
from app.utils.response import success, fail
from app.services.system.auth import get_current_user
from app.utils.logger import logger
from app.services.common.websocket_manager import websocket_manager

router = APIRouter(prefix="/api/steel-cost-data-verification", tags=["炼钢成本数据核对"])


class SteelCostDataVerificationRequest(BaseModel):
    """炼钢成本数据核对请求"""
    start_date: str  # 开始日期，格式：YYYY-MM-DD
    end_date: str    # 结束日期，格式：YYYY-MM-DD
    factory: str  # 厂别：一厂/二厂
    heat_no: Optional[str] = None  # 可选：炉号
    material_code: Optional[str] = None  # 可选：物料编码


@router.post("/start")
async def start_steel_cost_data_verification(
    request: SteelCostDataVerificationRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动炼钢成本数据核对任务"""
    try:
        # 验证厂别
        if request.factory not in ["一厂", "二厂"]:
            return fail("厂别参数错误，必须是'一厂'或'二厂'")
        
        factory = Factory.FACTORY_1 if request.factory == "一厂" else Factory.FACTORY_2
        
        # 创建炼钢成本数据核对服务
        service = SteelCostDataVerificationService(db)
        
        # 创建任务
        task = await service.create_task(
            start_date=request.start_date,
            end_date=request.end_date,
            factory=factory,
            heat_no=request.heat_no,
            material_code=request.material_code,
            user_id=current_user.id
        )
        
        # 定义进度回调函数
        async def progress_callback(task_id: int, progress: int, message: str, data: Optional[dict] = None):
            logger.info(f"发送进度更新: 任务ID={task_id}, 进度={progress}%, 消息={message}, 客户端ID={current_user.id}")
            await websocket_manager.send_task_progress(
                task_id=task_id,
                progress=progress,
                message=message,
                data=data,
                client_id=str(current_user.id)
            )
        
        # 在后台执行任务
        background_tasks.add_task(
            execute_steel_cost_data_verification_task,
            task.id,
            progress_callback,
            current_user.id
        )
        
        return success({
            "task_id": task.id,
            "message": "炼钢成本数据核对任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动炼钢成本数据核对失败: {e}")
        return fail(f"启动炼钢成本数据核对失败: {str(e)}")


@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        from app.models.steel_making.erp.steel_cost_data_verification import SteelCostDataVerificationTask
        
        task = await db.get(SteelCostDataVerificationTask, task_id)
        if not task:
            return fail("任务不存在")
        
        return success({
            "id": task.id,
            "task_name": task.task_name,
            "status": task.status.value,
            "progress": task.progress,
            "total_records": task.total_records,
            "processed_records": task.processed_records,
            "difference_count": task.difference_count,
            "error_message": task.error_message,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None
        })
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket连接端点"""
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            # 保持连接活跃
            await websocket.receive_text()
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, client_id)


async def execute_steel_cost_data_verification_task(task_id: int, progress_callback, user_id: int):
    """后台执行炼钢成本数据核对任务"""
    from app.database.session import SessionLocal
    
    async with SessionLocal() as db:
        try:
            service = SteelCostDataVerificationService(db)
            await service.start_task(task_id, progress_callback)
            
            # 发送任务完成消息
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=True,
                result_summary="炼钢成本数据核对完成",
                client_id=str(user_id)
            )
            
        except Exception as e:
            logger.error(f"炼钢成本数据核对任务执行失败: {task_id}, 错误: {e}")
            
            # 发送任务失败消息
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=False,
                result_summary=f"炼钢成本数据核对失败: {str(e)}",
                client_id=str(user_id)
            )
