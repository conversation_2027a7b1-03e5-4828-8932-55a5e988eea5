"""
MES维护相关路由
"""

import asyncio
from fastapi import APIRouter, Depends, WebSocket
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

from app.database.session import get_db
from app.services.steel_making.mes.mes_maintenance import MESMaintenanceService
from app.services.common.websocket_manager import websocket_manager
from app.services.system.auth import get_current_user
from app.models.system.user import User
from app.utils.response import success, fail
from app.utils.logger import logger
from app.utils.websocket_utils import generic_websocket_endpoint

router = APIRouter(prefix="/api/mes-maintenance")


class UpdateProdResSummaryRequest(BaseModel):
    """更新钢坯产量调拨汇总报表请求"""
    date: str  # 日期，格式：YYYY-MM-DD


class UpdateProdResSummaryResponse(BaseModel):
    """更新钢坯产量调拨汇总报表响应"""
    task_id: int
    message: str


class BofScrapSteelClearRequest(BaseModel):
    """转炉无法选择废钢号请求"""
    heat_id: str  # 炉次号


class BofScrapSteelClearResponse(BaseModel):
    """转炉无法选择废钢号响应"""
    task_id: int
    message: str
    original_bo_csmtwo: Optional[str] = None


@router.post("/update-prodres-summary")
async def update_prodres_summary(
    request: UpdateProdResSummaryRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新钢坯产量调拨汇总报表"""
    try:
        # 验证日期格式
        try:
            datetime.strptime(request.date, "%Y-%m-%d")
        except ValueError:
            return fail("日期格式错误，请使用 YYYY-MM-DD 格式")
        
        logger.info(f"用户 {current_user.username} 启动更新钢坯产量调拨汇总报表，日期: {request.date}")
        
        # 创建维护任务
        service = MESMaintenanceService(db)
        task_id = await service.create_update_prodres_summary_task(
            date=request.date,
            user_id=current_user.id
        )
        
        # 定义进度回调函数
        async def progress_callback(inner_task_id: int, progress: int, message: str, data: Optional[dict] = None):
            await websocket_manager.send_task_progress(
                task_id=inner_task_id,
                progress=progress,
                message=message,
                data=data,
                client_id=str(current_user.id)
            )
        
        # 在后台执行任务
        asyncio.create_task(
            execute_update_prodres_summary_task(
                task_id,
                request.date,
                progress_callback,
                current_user.id
            )
        )
        
        return success({
            "task_id": task_id,
            "message": "更新钢坯产量调拨汇总报表任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动更新钢坯产量调拨汇总报表任务失败: {e}")
        return fail(f"启动任务失败: {str(e)}")


async def execute_update_prodres_summary_task(
    task_id: int, 
    date: str,
    progress_callback, 
    user_id: int
):
    """后台执行更新钢坯产量调拨汇总报表任务"""
    try:
        # 创建新的数据库会话
        async for db in get_db():
            service = MESMaintenanceService(db)
            await service.start_update_prodres_summary_task(task_id, date, progress_callback)
            
            # 发送完成通知
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=True,
                result_summary="更新钢坯产量调拨汇总报表任务执行成功",
                client_id=str(user_id)
            )
            break
        
    except Exception as e:
        logger.error(f"更新钢坯产量调拨汇总报表后台任务执行失败: {e}")
        
        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"更新钢坯产量调拨汇总报表任务执行失败: {str(e)}",
            client_id=str(user_id)
        )


@router.get("/task/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        service = MESMaintenanceService(db)
        task_status = await service.get_task_status(task_id)
        
        if not task_status:
            return fail("任务不存在")
        
        return success(task_status)
        
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.post("/bof-scrap-steel-clear")
async def bof_scrap_steel_clear(
    request: BofScrapSteelClearRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """转炉无法选择废钢号"""
    try:
        # 验证炉次号格式
        if not request.heat_id or not request.heat_id.strip():
            return fail("炉次号不能为空")

        heat_id = request.heat_id.strip()
        logger.info(f"用户 {current_user.username} 启动转炉无法选择废钢号处理，炉次号: {heat_id}")

        # 创建维护任务
        service = MESMaintenanceService(db)
        task_id = await service.create_bof_scrap_steel_clear_task(
            heat_id=heat_id,
            user_id=current_user.id
        )

        # 定义进度回调函数
        async def progress_callback(task_id: int, progress: int, message: str, data: Optional[dict] = None):
            await websocket_manager.send_task_progress(
                task_id=task_id,
                progress=progress,
                message=message,
                data=data,
                client_id=str(current_user.id)
            )

        # 在后台执行任务
        asyncio.create_task(
            execute_bof_scrap_steel_clear_task(
                task_id,
                heat_id,
                progress_callback,
                current_user.id
            )
        )

        return success({
            "task_id": task_id,
            "message": "转炉无法选择废钢号任务已启动"
        })

    except Exception as e:
        logger.error(f"启动转炉无法选择废钢号任务失败: {e}")
        return fail(f"启动任务失败: {str(e)}")


async def execute_bof_scrap_steel_clear_task(
    task_id: int,
    heat_id: str,
    progress_callback,
    user_id: int
):
    """后台执行转炉无法选择废钢号任务"""
    try:
        # 创建新的数据库会话
        async for db in get_db():
            service = MESMaintenanceService(db)
            original_value = await service.start_bof_scrap_steel_clear_task(task_id, heat_id, progress_callback)

            # 发送完成通知
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=True,
                result_summary=f"转炉无法选择废钢号处理完成，原BO_CSMTWO值: {original_value}",
                client_id=str(user_id),
                data={"original_bo_csmtwo": original_value, "heat_id": heat_id}
            )
            break

    except Exception as e:
        logger.error(f"转炉无法选择废钢号后台任务执行失败: {e}")

        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"转炉无法选择废钢号任务执行失败: {str(e)}",
            client_id=str(user_id)
        )


@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """MES维护WebSocket端点，用于实时通信"""
    await generic_websocket_endpoint(websocket, client_id, "MES维护")
