import asyncio
from fastapi import APIRouter, Depends, WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

from app.database.session import get_db
from app.models.system.user import User
from app.models.common import MessageType
from app.services.system.auth import get_current_user
from app.services.steel_making.mes.mes_message_check import MESMessageCheckService
from app.utils.logger import logger
from app.utils.response import success, fail
from app.services.common.websocket_manager import websocket_manager

router = APIRouter(prefix="/api/mes-message-check")


class MESMessageCheckRequest(BaseModel):
    """MES电文检查请求"""
    check_date: str  # 检查日期，格式：YYYY-MM-DD
    message_type: str  # 电文类型：receive 或 send


class MESMessageCheckResponse(BaseModel):
    """MES电文检查响应"""
    task_id: int
    message: str


@router.post("/start")
async def start_mes_message_check(
    request: MESMessageCheckRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """启动MES电文检查任务"""
    try:
        # 验证电文类型
        if request.message_type not in ["receive", "send"]:
            return fail("不支持的电文类型")
        
        message_type = MessageType.RECEIVE if request.message_type == "receive" else MessageType.SEND
        
        # 验证日期格式
        try:
            datetime.strptime(request.check_date, "%Y-%m-%d")
        except ValueError:
            return fail("日期格式错误，请使用 YYYY-MM-DD 格式")
        
        logger.info(f"用户 {current_user.username} 启动MES电文检查，日期: {request.check_date}, 类型: {request.message_type}")
        
        # 创建检查任务
        service = MESMessageCheckService(db)
        task_id = await service.create_mes_message_check_task(
            check_date=request.check_date,
            message_type=message_type,
            user_id=current_user.id
        )
        
        # 定义进度回调函数
        async def progress_callback(task_id: int, progress: int, message: str, data: Optional[dict] = None):
            await websocket_manager.send_task_progress(
                task_id=task_id,
                progress=progress,
                message=message,
                data=data,
                client_id=str(current_user.id)
            )
        
        # 在后台执行任务
        asyncio.create_task(
            execute_mes_message_check_task(
                task_id,
                message_type,
                progress_callback,
                current_user.id
            )
        )
        
        return success({
            "task_id": task_id,
            "message": "MES电文检查任务已启动"
        })
        
    except Exception as e:
        logger.error(f"启动MES电文检查任务失败: {e}")
        return fail(f"启动任务失败: {str(e)}")


async def execute_mes_message_check_task(
    task_id: int, 
    message_type: MessageType,
    progress_callback, 
    user_id: int
):
    """后台执行MES电文检查任务"""
    try:
        # 创建新的数据库会话
        async for db in get_db():
            service = MESMessageCheckService(db)
            await service.start_mes_message_check_task(task_id, message_type, progress_callback)
            
            # 发送完成通知
            await websocket_manager.send_task_completed(
                task_id=task_id,
                success=True,
                result_summary="MES电文检查任务执行成功",
                client_id=str(user_id)
            )
            break
        
    except Exception as e:
        logger.error(f"MES后台任务执行失败: {e}")
        
        # 发送失败通知
        await websocket_manager.send_task_completed(
            task_id=task_id,
            success=False,
            result_summary=f"MES任务执行失败: {str(e)}",
            client_id=str(user_id)
        )


@router.get("/task/{task_id}/status")
async def get_mes_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取MES电文检查任务状态"""
    try:
        service = MESMessageCheckService(db)
        task_status = await service.get_task_status(task_id)
        
        if not task_status:
            return fail("任务不存在")
        
        return success(task_status)
        
    except Exception as e:
        logger.error(f"获取MES任务状态失败: {e}")
        return fail(f"获取任务状态失败: {str(e)}")


@router.websocket("/ws/{user_id}")
async def mes_message_check_websocket_endpoint(websocket: WebSocket, user_id: str):
    """MES电文检查WebSocket端点"""
    await websocket_manager.connect(websocket, user_id)
    try:
        while True:
            # 保持连接活跃
            await websocket.receive_text()
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, user_id)
