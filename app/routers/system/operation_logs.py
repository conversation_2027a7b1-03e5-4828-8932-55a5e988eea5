from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.session import get_db
from app.routers.system.role import PaginationParams
from app.services.system.operation_log import OperationLogService
from app.utils.response import success

router = APIRouter(prefix="/api/log", tags=["role"])


@router.post("/")
async def get_operation_logs(
        params: PaginationParams,
        db: AsyncSession = Depends(get_db),
):
    """获取日志"""
    skip = (params.page - 1) * params.page_size
    logs = await OperationLogService.list_logs(db, skip=skip, limit=params.page_size)
    return success(data={
        "items": logs,
        "total": await OperationLogService.count_logs(db),
        "page": params.page,
        "page_size": params.page_size
    })
