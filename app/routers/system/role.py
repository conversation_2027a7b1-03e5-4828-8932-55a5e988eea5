from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from pydantic import BaseModel

from app.database import get_db
from app.utils.response import success, fail
from app.utils.logger import logger
from app.services.system.role import RoleService

router = APIRouter(prefix="/api/role", tags=["role"])

# 请求模型
class RoleCreate(BaseModel):
    name: str
    description: Optional[str] = None
    permissions: Optional[list[str]] = None

class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[list[str]] = None

class PaginationParams(BaseModel):
    page: int = 1
    page_size: int = 10

# 响应模型
class RoleResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    permissions: list[str]
    created_at: str
    updated_at: str

@router.post("/")
async def create_role(role: RoleCreate, db: AsyncSession = Depends(get_db)):
    """创建角色"""
    try:
        created_role = await RoleService.create_role(
            db, 
            name=role.name,
            description=role.description,
            permissions=role.permissions
        )
        return success(data=created_role)
    except Exception as e:
        logger.error(f"创建角色失败: {str(e)}")
        return fail(msg="创建角色失败")

@router.post("/detail")
async def get_role(role_id: int, db: AsyncSession = Depends(get_db)):
    """获取角色信息"""
    try:
        role = await RoleService.get_role_by_id(db, role_id)
        return success(data=role)
    except Exception as e:
        logger.error(f"获取角色失败: {str(e)}")
        return fail(msg="获取角色失败")

@router.put("/{role_id}")
async def update_role(
    role_id: int, 
    role: RoleUpdate, 
    db: AsyncSession = Depends(get_db)
):
    """更新角色信息"""
    try:
        update_data = role.model_dump(exclude_unset=True)
        updated_role = await RoleService.update_role(db, role_id, **update_data)
        return success(data=updated_role)
    except Exception as e:
        logger.error(f"更新角色失败: {str(e)}")
        return fail(msg="更新角色失败")

@router.delete("/{role_id}")
async def delete_role(role_id: int, db: AsyncSession = Depends(get_db)):
    """删除角色"""
    try:
        await RoleService.delete_role(db, role_id)
        return success(msg="角色删除成功")
    except Exception as e:
        logger.error(f"删除角色失败: {str(e)}")
        return fail(msg="删除角色失败")

@router.post("/list")
async def list_roles(
    params: PaginationParams,
    db: AsyncSession = Depends(get_db)
):
    """获取角色列表"""
    try:
        skip = (params.page - 1) * params.page_size
        roles = await RoleService.list_roles(db, skip=skip, limit=params.page_size)
        return success(data={
            "items": roles,
            "total": await RoleService.count_roles(db),
            "page": params.page,
            "page_size": params.page_size
        })
    except Exception as e:
        logger.error(f"获取角色列表失败: {str(e)}")
        return fail(msg="获取角色列表失败")