from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from pydantic import BaseModel

from app.database.session import get_db
from app.services.system.user import UserService
from app.exceptions import UserAlreadyExists, UserNotFound
from app.utils.logger import logger
from app.utils.response import success, fail


router = APIRouter(prefix="/api/user", tags=["user"])

# 请求模型
class UserCreate(BaseModel):
    username: str
    password: str
    nickname: Optional[str] = None
    remark: Optional[str] = None

class UserUpdate(BaseModel):
    nickname: Optional[str] = None
    remark: Optional[str] = None
    password: Optional[str] = None

class PaginationParams(BaseModel):
    page: int = 1
    page_size: int = 10

# 响应模型
class UserResponse(BaseModel):
    id: int
    username: str
    nickname: Optional[str]
    remark: Optional[str]
    status: int
    created_at: str
    updated_at: str

@router.post("/")
async def create_user(user: UserCreate, db: AsyncSession = Depends(get_db)):
    """创建用户"""
    try:
        created_user = await UserService.create_user(
            db, 
            username=user.username,
            password=user.password,
            nickname=user.nickname,
            remark=user.remark
        )
        return success(data=created_user)
    except UserAlreadyExists as e:
        return fail(msg=e.detail)
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}")
        return fail(msg="服务器内部错误")

@router.post("/detail")
async def get_user(user_id: int, db: AsyncSession = Depends(get_db)):
    """获取用户信息"""
    try:
        user = await UserService.get_user_by_id(db, user_id)
        return success(data=user)
    except UserNotFound as e:
        return fail(msg=e.detail)
    except Exception as e:
        logger.error(f"获取用户失败: {str(e)}")
        return fail(msg="服务器内部错误")

@router.put("/{user_id}")
async def update_user(
    user_id: int, 
    user: UserUpdate, 
    db: AsyncSession = Depends(get_db)
):
    """更新用户信息"""
    try:
        update_data = user.model_dump(exclude_unset=True)
        updated_user = await UserService.update_user(db, user_id, **update_data)
        return success(data=updated_user)
    except UserNotFound as e:
        return fail(msg=e.detail)
    except Exception as e:
        logger.error(f"更新用户失败: {str(e)}")
        return fail(msg="服务器内部错误")

@router.delete("/{user_id}")
async def delete_user(user_id: int, db: AsyncSession = Depends(get_db)):
    """删除用户"""
    try:
        await UserService.delete_user(db, user_id)
        return success(msg="用户删除成功")
    except UserNotFound as e:
        return fail(msg=e.detail)
    except Exception as e:
        logger.error(f"删除用户失败: {str(e)}")
        return fail(msg="服务器内部错误")

@router.post("/list")
async def list_users(
    params: PaginationParams,
    db: AsyncSession = Depends(get_db)
):
    """获取用户列表"""
    try:
        skip = (params.page - 1) * params.page_size
        users = await UserService.list_users(db, skip=skip, limit=params.page_size)
        return success(data={
            "items": users,
            "total": await UserService.count_users(db),
            "page": params.page,
            "page_size": params.page_size
        })
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return fail(msg="获取用户列表失败")