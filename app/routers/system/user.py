from fastapi import APIRouter, Depends, Query
from typing import List, Optional

from app.controllers.system.user import (
    get_user_controller,
    UserController,
    UserCreateRequest,
    UserUpdateRequest
)
from app.controllers.base import PaginationParams, SearchParams


router = APIRouter(prefix="/api/user", tags=["用户管理"])

@router.post("/", summary="创建用户")
async def create_user(
    user_data: UserCreateRequest,
    controller: UserController = Depends(get_user_controller)
):
    """创建用户"""
    return await controller.create_item(user_data)


@router.get("/{user_id}", summary="获取用户详情")
async def get_user(
    user_id: int,
    controller: UserController = Depends(get_user_controller)
):
    """获取用户详情"""
    return await controller.get_item(user_id)


@router.put("/{user_id}", summary="更新用户")
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    controller: UserController = Depends(get_user_controller)
):
    """更新用户信息"""
    return await controller.update_item(user_id, user_data)


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: int,
    controller: UserController = Depends(get_user_controller)
):
    """删除用户"""
    return await controller.delete_item(user_id)


@router.get("/", summary="获取用户列表")
async def list_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    controller: UserController = Depends(get_user_controller)
):
    """获取用户列表"""
    pagination = PaginationParams(page=page, page_size=page_size)
    search = SearchParams(keyword=keyword) if keyword else None
    return await controller.list_items(pagination, search)


@router.get("/statistics", summary="获取用户统计")
async def get_user_statistics(
    controller: UserController = Depends(get_user_controller)
):
    """获取用户统计信息"""
    return await controller.get_user_statistics()


@router.post("/batch/status", summary="批量更新用户状态")
async def batch_update_status(
    user_ids: List[int],
    status: int,
    controller: UserController = Depends(get_user_controller)
):
    """批量更新用户状态"""
    return await controller.batch_update_status(user_ids, status)