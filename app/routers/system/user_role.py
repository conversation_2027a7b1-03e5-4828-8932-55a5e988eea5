from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.session import get_db
from app.services.system.user import UserService

router = APIRouter(prefix="/api/user-role", tags=["user-role"])

@router.post("/bind")
async def bind_role_to_user(
    user_id: int,
    role_id: int,
    db: AsyncSession = Depends(get_db)
):
    """绑定角色到用户"""
    success = await UserService.assign_role_to_user(db, user_id, role_id)
    if success:
        return {"code": 200, "msg": "角色分配成功"}
    else:
        return {"code": 400, "msg": "角色已存在或分配失败"}

@router.delete("/unbind")
async def unbind_role_from_user(
    user_id: int,
    role_id: int,
    db: AsyncSession = Depends(get_db)
):
    """从用户解绑角色"""
    success = await UserService.remove_role_from_user(db, user_id, role_id)
    if success:
        return {"code": 200, "msg": "角色移除成功"}
    else:
        return {"code": 400, "msg": "角色不存在或移除失败"}

@router.get("/users/{user_id}/roles")
async def get_user_roles(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取用户所有角色"""
    roles = await UserService.get_user_roles(db, user_id)
    return {"code": 200, "msg": "获取成功", "data": roles}

@router.get("/permissions")
async def list_all_permissions():
    """获取所有权限列表"""
    from app.constants.permissions import get_all_permissions
    permissions = get_all_permissions()
    return {"code": 200, "msg": "获取成功", "data": {"permissions": permissions}}