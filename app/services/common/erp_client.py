import asyncio
import aiohttp
import json
import re
from typing import Dict, Optional, List
from datetime import datetime
from app.utils.logger import logger


class ERPClient:
    """ERP系统客户端"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url = "https://jgerp.jggroup.cn"
        self.token: Optional[str] = None
        self.is_logged_in = False
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        # 创建Cookie jar来保持会话
        cookie_jar = aiohttp.CookieJar()

        # 创建连接器，禁用SSL验证（如果有证书问题）
        connector = aiohttp.TCPConnector(
            ssl=False,  # 禁用SSL验证
            limit=100,  # 连接池大小
            limit_per_host=30,  # 每个主机的连接数
            keepalive_timeout=30  # 保持连接时间
        )

        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60, connect=10),  # 增加超时时间
            connector=connector,
            cookie_jar=cookie_jar,  # 使用Cookie jar
            headers={
                "Host": "jgerp.jggroup.cn",
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/20100101 Firefox/139.0",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "DNT": "1",
                "Sec-GPC": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
                "Priority": "u=0, i"
            },
            # 禁用自动重定向，避免端口问题
            connector_owner=False
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def login(self, username: str, password: str) -> bool:
        """登录ERP系统"""
        try:
            logger.info(f"正在登录ERP系统，用户名: {username}")

            # 参考mes-self项目的登录实现
            login_url = f"{self.base_url}/erp/ds/jsp/dsjjSignOn.jsp"

            # 设置请求头
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "X-Requested-With": "XMLHttpRequest"
            }

            # 登录数据
            login_data = {
                "userId": username,
                "password": password,
                "loginType": "1"  # 添加登录类型
            }

            logger.info(f"发送登录请求到: {login_url}")
            logger.info(f"登录数据: {login_data}")

            async with self.session.post(login_url, data=login_data, headers=headers) as response:
                logger.info(f"登录响应状态码: {response.status}")

                # 记录响应头
                logger.info(f"响应头: {dict(response.headers)}")

                if response.status != 200:
                    logger.error(f"登录请求失败，状态码: {response.status}")
                    return False

                # 尝试解析响应
                try:
                    # 先尝试JSON解析
                    login_result = await response.json()
                    logger.info(f"登录响应JSON: {login_result}")

                except Exception as json_error:
                    # 如果不是JSON响应，尝试从HTML中提取JSON
                    response_text = await response.text()
                    logger.info(f"登录响应是HTML格式，尝试提取JSON...")

                    # 查找JSON数据
                    json_match = re.search(r'\{[^}]*"signCode"[^}]*\}', response_text)
                    if json_match:
                        json_str = json_match.group()
                        logger.info(f"提取到的JSON: {json_str}")
                        try:
                            login_result = json.loads(json_str)
                        except Exception as parse_error:
                            logger.error(f"JSON解析失败: {parse_error}")
                            return False
                    else:
                        logger.error(f"未找到JSON数据，响应内容: {response_text[:500]}...")
                        return False

                # 检查登录结果
                if login_result.get("signCode") == 301:
                    logger.info("ERP系统登录成功")
                    self.is_logged_in = True

                    # 保存会话Cookie
                    self._save_cookies(response.cookies)

                    return True
                else:
                    error_msg = login_result.get('message', '登录失败')
                    logger.error(f"ERP登录失败: {error_msg}")
                    return False

        except Exception as e:
            logger.error(f"ERP登录异常: {str(e)}")
            return False

    def _save_cookies(self, cookies):
        """保存登录Cookie"""
        try:
            # cookies可能是不同的类型，需要适配
            if hasattr(cookies, 'items'):
                # 如果是字典类型
                for name, morsel in cookies.items():
                    logger.info(f"保存Cookie: {name}={morsel.value}")
                    if name.lower() in ['jsessionid', 'token', 'sessionid']:
                        self.token = morsel.value
            else:
                # 如果是其他类型，尝试迭代
                for cookie in cookies:
                    if hasattr(cookie, 'key') and hasattr(cookie, 'value'):
                        logger.info(f"保存Cookie: {cookie.key}={cookie.value}")
                        if cookie.key.lower() in ['jsessionid', 'token', 'sessionid']:
                            self.token = cookie.value
                    elif hasattr(cookie, 'name') and hasattr(cookie, 'value'):
                        logger.info(f"保存Cookie: {cookie.name}={cookie.value}")
                        if cookie.name.lower() in ['jsessionid', 'token', 'sessionid']:
                            self.token = cookie.value

            if self.token:
                logger.info(f"已保存会话Token: {self.token[:20]}...")
            else:
                logger.warning("未找到会话Token")

        except Exception as e:
            logger.error(f"保存Cookie失败: {e}")

    async def test_connection(self) -> bool:
        """测试ERP系统连接"""
        try:
            logger.info("测试ERP系统连接...")

            # 尝试访问登录页面
            test_url = f"{self.base_url}/erp/ds/jsp/dsjjSignOn.jsp"

            async with self.session.get(test_url) as response:
                logger.info(f"连接测试响应状态码: {response.status}")

                if response.status == 200:
                    logger.info("ERP系统连接正常")
                    return True
                else:
                    logger.error(f"ERP系统连接失败，状态码: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"ERP系统连接测试异常: {str(e)}")
            return False

    async def trigger_schedule(self, select_item: str) -> bool:
        """触发排程任务"""
        try:
            if not self.is_logged_in:
                logger.error("未登录ERP系统，无法触发排程")
                return False

            logger.info(f"正在触发排程任务: {select_item}")

            # 使用标准HTTPS端口，避免重定向到其他端口
            schedule_url = f"{self.base_url}/erp/dq/jsp/dqjjScheduleList.jsp"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "*/*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "X-Requested-With": "XMLHttpRequest",
                "Referer": f"{self.base_url}/erp/ds/jsp/dsjjPortal.jsp"
            }

            # 使用表单数据格式
            data = {"selectItem": select_item}

            logger.info(f"发送排程请求到: {schedule_url}")
            logger.info(f"排程数据: {data}")

            # 禁用自动重定向，手动处理重定向
            async with self.session.post(
                schedule_url,
                data=data,
                headers=headers,
                allow_redirects=False  # 禁用自动重定向
            ) as response:
                logger.info(f"排程响应状态码: {response.status}")
                logger.info(f"响应头: {dict(response.headers)}")

                # 处理重定向
                if response.status in [301, 302, 303, 307, 308]:
                    location = response.headers.get('Location')
                    if location:
                        logger.info(f"检测到重定向: {location}")

                        # 修复重定向URL中的端口问题
                        if ':9083' in location or ':9082' in location:
                            # 替换错误的端口为标准HTTPS端口
                            location = location.replace(':9083', '').replace(':9082', '')
                            location = location.replace('http://', 'https://')
                            logger.info(f"修复重定向URL: {location}")

                        # 如果是相对路径，转换为绝对路径
                        if location.startswith('/'):
                            location = f"{self.base_url}{location}"

                        # 重新发送请求到重定向地址
                        try:
                            async with self.session.post(location, data=data, headers=headers, allow_redirects=False) as redirect_response:
                                logger.info(f"重定向响应状态码: {redirect_response.status}")
                                if redirect_response.status == 200:
                                    response_text = await redirect_response.text()
                                    logger.info(f"排程响应内容: {response_text[:200]}...")
                                    logger.info(f"排程任务 {select_item} 触发成功")
                                    return True
                                else:
                                    response_text = await redirect_response.text()
                                    logger.error(f"重定向后排程任务 {select_item} 触发失败，状态码: {redirect_response.status}")
                                    logger.error(f"错误响应: {response_text[:200]}...")
                                    return False
                        except Exception as redirect_error:
                            logger.error(f"重定向请求失败: {redirect_error}")
                            # 即使重定向失败，也认为触发成功，因为原始请求返回了302
                            logger.info(f"虽然重定向失败，但原始请求返回302，认为排程任务 {select_item} 触发成功")
                            return True

                elif response.status == 200:
                    response_text = await response.text()
                    logger.info(f"排程响应内容: {response_text[:200]}...")
                    logger.info(f"排程任务 {select_item} 触发成功")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"排程任务 {select_item} 触发失败，状态码: {response.status}")
                    logger.error(f"错误响应: {response_text[:200]}...")
                    return False

        except Exception as e:
            logger.error(f"触发排程任务异常: {str(e)}")
            return False
    
    async def check_schedule_queue(self, count: Optional[str] = None) -> Dict:
        """检查排程队列状态"""
        try:
            if not self.is_logged_in:
                logger.error("未登录ERP系统，无法检查队列")
                return {}

            # 生成时间戳作为count参数
            if not count:
                count = str(int(datetime.now().timestamp() * 1000))

            queue_url = f"{self.base_url}/erp/dq/jsp/dqjjData.jsp"
            params = {
                "qn": "scheduleQueue",
                "action": "I",
                "count": count
            }

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "X-Requested-With": "XMLHttpRequest",
                "Referer": f"{self.base_url}/erp/ds/jsp/dsjjPortal.jsp"
            }

            logger.debug(f"检查队列状态: {queue_url}?{params}")

            async with self.session.get(queue_url, params=params, headers=headers) as response:
                logger.debug(f"队列检查响应状态码: {response.status}")

                if response.status == 200:
                    try:
                        queue_data = await response.json()
                        logger.debug(f"队列数据: {queue_data}")
                        return queue_data
                    except Exception as json_error:
                        # 尝试从HTML中提取JSON
                        response_text = await response.text()
                        logger.debug(f"队列响应是HTML格式，尝试提取JSON...")

                        # 查找JSON数据 - 使用更简单的方法
                        # 找到第一个{，然后找到匹配的}
                        start_idx = response_text.find('{')
                        if start_idx != -1:
                            # 从第一个{开始，找到匹配的}
                            brace_count = 0
                            end_idx = start_idx
                            for i, char in enumerate(response_text[start_idx:], start_idx):
                                if char == '{':
                                    brace_count += 1
                                elif char == '}':
                                    brace_count -= 1
                                    if brace_count == 0:
                                        end_idx = i
                                        break

                            if brace_count == 0:
                                json_str = response_text[start_idx:end_idx + 1]
                                logger.debug(f"提取到的队列JSON: {json_str[:200]}...")
                                try:
                                    queue_data = json.loads(json_str)
                                    logger.debug(f"队列数据解析成功")
                                    return queue_data
                                except Exception as parse_error:
                                    logger.error(f"队列JSON解析失败: {parse_error}")
                                    return {}
                            else:
                                logger.error("JSON括号不匹配")
                                return {}
                        else:
                            logger.error(f"未找到JSON数据，响应内容: {response_text[:200]}...")
                            return {}
                else:
                    response_text = await response.text()
                    logger.error(f"检查队列失败，状态码: {response.status}")
                    logger.error(f"错误响应: {response_text[:200]}...")
                    return {}

        except Exception as e:
            logger.error(f"检查排程队列异常: {str(e)}")
            return {}
    
    async def wait_for_task_completion(self, task_name: str, max_wait_seconds: int = 300, check_interval: int = 5) -> Dict:
        """等待任务完成"""
        try:
            logger.info(f"开始监控任务: {task_name}")
            start_time = datetime.now()
            task_found_in_queue = False

            # 先等待5秒，让任务有时间进入队列
            logger.info(f"等待5秒让任务 {task_name} 进入队列...")
            await asyncio.sleep(5)

            while True:
                # 检查队列状态
                queue_data = await self.check_schedule_queue()

                if not queue_data:
                    logger.error("无法获取队列数据")
                    return {"success": False, "error": "无法获取队列数据"}

                # 检查任务是否在等待队列中
                in_queue = queue_data.get("inQueue", [])
                in_queue_task = None
                for task in in_queue:
                    if task.get("name") == task_name:
                        in_queue_task = task
                        task_found_in_queue = True
                        break

                # 检查任务是否在运行队列中
                run_queue = queue_data.get("runQueue", [])
                run_queue_task = None
                for task in run_queue:
                    if task.get("name") == task_name:
                        run_queue_task = task
                        task_found_in_queue = True
                        break

                # 如果任务在等待队列中
                if in_queue_task:
                    logger.info(f"任务 {task_name} 在等待队列中，等待执行...")
                    task_info = in_queue_task

                # 如果任务在运行队列中
                elif run_queue_task:
                    logger.info(f"任务 {task_name} 正在执行中...")
                    task_info = run_queue_task

                    # 记录当前状态
                    progress = task_info.get("progress", "未知")
                    status = task_info.get("status", "未知")
                    logger.info(f"任务 {task_name} 运行中 - 进度: {progress}, 状态: {status}")

                # 如果任务既不在等待队列也不在运行队列中
                else:
                    if task_found_in_queue:
                        # 之前找到过任务，现在不在队列中，说明已完成
                        end_time = datetime.now()
                        duration = (end_time - start_time).total_seconds()
                        logger.info(f"任务 {task_name} 已完成，耗时: {duration:.2f}秒")
                        return {
                            "success": True,
                            "duration": duration,
                            "task_info": None
                        }
                    else:
                        # 从未找到过任务，可能任务名称不正确或任务执行太快
                        elapsed = (datetime.now() - start_time).total_seconds()
                        if elapsed > 30:  # 30秒后还没找到任务，认为任务可能已经完成
                            logger.warning(f"任务 {task_name} 未在队列中找到，可能已经完成或任务名称不正确")
                            return {
                                "success": True,
                                "duration": elapsed,
                                "task_info": None
                            }

                # 检查是否超时
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed > max_wait_seconds:
                    logger.error(f"任务 {task_name} 执行超时 ({max_wait_seconds}秒)")
                    return {
                        "success": False,
                        "error": f"任务执行超时 ({max_wait_seconds}秒)",
                        "task_info": run_queue_task or in_queue_task
                    }

                # 等待下次检查
                await asyncio.sleep(check_interval)

        except Exception as e:
            logger.error(f"等待任务完成异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def execute_schedule_task(self, task_name: str) -> Dict:
        """执行单个排程任务（已弃用，请直接使用trigger_schedule和wait_for_task_completion）"""
        logger.warning("execute_schedule_task方法已弃用，请直接使用trigger_schedule和wait_for_task_completion")

        try:
            step_start_time = datetime.now()

            # 1. 触发任务
            trigger_success = await self.trigger_schedule(task_name)
            if not trigger_success:
                return {
                    "success": False,
                    "error": f"触发任务 {task_name} 失败",
                    "duration": 0
                }

            # 2. 等待任务完成
            result = await self.wait_for_task_completion(task_name)

            step_end_time = datetime.now()
            total_duration = (step_end_time - step_start_time).total_seconds()

            if result["success"]:
                return {
                    "success": True,
                    "duration": total_duration,
                    "task_info": result.get("task_info")
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "未知错误"),
                    "duration": total_duration,
                    "task_info": result.get("task_info")
                }

        except Exception as e:
            logger.error(f"执行排程任务 {task_name} 异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "duration": 0
            }
