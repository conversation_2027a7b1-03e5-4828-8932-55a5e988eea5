import oracledb
import os
from typing import Optional, Dict, Any, List
from app.config import get_settings
from app.utils.logger import logger

class OracleConnectionManager:
    """Oracle数据库连接管理器"""
    
    def __init__(self):
        self.settings = get_settings()
        self._setup_oracle_client()
    
    def _setup_oracle_client(self):
        """设置Oracle客户端环境"""
        try:
            if self.settings.PRO:
                oracle_client_path = os.path.expanduser("/home/<USER>/Environment/instantclient_19_27")
            else:
                # 设置Oracle客户端路径
                oracle_client_path = os.path.expanduser("~/Environment/instantclient_12_2")
                # oracle_client_path = os.path.expanduser("/Users/<USER>/Environment/instantclient-basic-linux.x64-19.27.0.0.0dbru/instantclient_19_27")
            if os.path.exists(oracle_client_path):
                os.environ["ORACLE_HOME"] = oracle_client_path
                os.environ["DYLD_LIBRARY_PATH"] = oracle_client_path
                os.environ["PATH"] = f"{oracle_client_path}:{os.environ.get('PATH', '')}"
            
            # 设置字符编码
            os.environ['NLS_LANG'] = 'AMERICAN_AMERICA.AL32UTF8'
            logger.info("Oracle客户端环境设置完成")
        except Exception as e:
            logger.warning(f"Oracle客户端环境设置失败: {e}")
    
    def get_erp_connection(self) -> oracledb.Connection:
        """获取ERP Oracle数据库连接"""
        try:
            dsn = f"{self.settings.ERP_ORACLE_HOST}:{self.settings.ERP_ORACLE_PORT}/{self.settings.ERP_ORACLE_SERVICE}"
            connection = oracledb.connect(
                user=self.settings.ERP_ORACLE_USER,
                password=self.settings.ERP_ORACLE_PASSWORD,
                dsn=dsn
            )
            logger.info(f"ERP Oracle数据库连接成功: {dsn}")
            return connection
        except Exception as e:
            logger.error(f"ERP Oracle数据库连接失败: {e}")
            raise

    def get_mes_msg_connection(self) -> oracledb.Connection:
        """获取MES电文的Oracle数据库连接"""
        try:
            dsn = f"{self.settings.MES_MSG_ORACLE_HOST}:{self.settings.MES_MSG_ORACLE_PORT}/{self.settings.MES_MSG_ORACLE_SERVICE}"
            connection = oracledb.connect(
                user=self.settings.MES_MSG_ORACLE_USER,
                password=self.settings.MES_MSG_ORACLE_PASSWORD,
                dsn=dsn
            )
            logger.info(f"MES 电文 Oracle数据库连接成功: {dsn}")
            return connection
        except Exception as e:
            logger.error(f"MES 电文 Oracle数据库连接失败: {e}")
            raise
    
    def get_mes_connection(self) -> oracledb.Connection:
        """获取MES Oracle数据库连接"""
        try:
            dsn = f"{self.settings.MES_ORACLE_HOST}:{self.settings.MES_ORACLE_PORT}/{self.settings.MES_ORACLE_SERVICE}"
            connection = oracledb.connect(
                user=self.settings.MES_ORACLE_USER,
                password=self.settings.MES_ORACLE_PASSWORD,
                dsn=dsn
            )
            logger.info(f"MES Oracle数据库连接成功: {dsn}")
            return connection
        except Exception as e:
            logger.error(f"MES Oracle数据库连接失败: {e}")
            raise
    
    def execute_query(self, connection: oracledb.Connection, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        try:
            cursor = connection.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 获取列名
            columns = [desc[0] for desc in cursor.description]
            
            # 获取数据并转换为字典列表
            rows = cursor.fetchall()
            results = []
            for row in rows:
                result = {}
                for i, value in enumerate(row):
                    result[columns[i]] = value
                results.append(result)
            
            cursor.close()
            logger.info(f"查询执行成功，返回 {len(results)} 条记录")
            return results
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            raise
    
    def close_connection(self, connection: oracledb.Connection):
        """关闭数据库连接"""
        try:
            if connection:
                connection.close()
                logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")

# 全局连接管理器实例
oracle_manager = OracleConnectionManager()
