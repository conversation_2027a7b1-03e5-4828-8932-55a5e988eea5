"""
定时任务调度器
"""

from datetime import datetime, timedelta
from typing import Dict, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from app.database.session import get_db
from app.services.steel_making.erp.data_comparison import DataComparisonService
from app.services.steel_making.mes.daily_counting import DailyCounting
from app.services.steel_making.mes.message_check import MessageCheckService
from app.services.steel_making.mes.mes_message_check import MESMessageCheckService
from app.services.steel_making.erp.ix_receiva import IxReceivaService
from app.models.common import Factory, MessageType
from app.utils.logger import logger


class MaintenanceScheduler:
    """维护任务调度器"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行中")
            return
        
        try:
            # 添加定时任务
            await self._add_scheduled_jobs()
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            logger.info("维护任务调度器已启动")
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return
        
        try:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("维护任务调度器已停止")
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    async def _add_scheduled_jobs(self):
        """添加定时任务"""
        self.scheduler.add_job(
            func=self._execute_data_comparison,
            trigger=IntervalTrigger(hours=2),
            id='data_comparison_job',
            name='物料消耗对比',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=600
        )
        

        self.scheduler.add_job(
            func=self._execute_erp_message_check,
            trigger=IntervalTrigger(hours=24),
            id='erp_message_check_job',
            name='ERP电文检查',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=300
        )
        

        self.scheduler.add_job(
            func=self._execute_mes_message_check,
            trigger=IntervalTrigger(hours=24),
            id='mes_message_check_job',
            name='MES电文检查',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=300
        )

        # IX收账作业检查
        self.scheduler.add_job(
            func=self._execute_ix_receiva_check,
            trigger=IntervalTrigger(hours=2),
            id='ix_receiva_check_job',
            name='IX收账作业检查',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=300
        )

        # 炼钢成本数据核对
        self.scheduler.add_job(
            func=self._execute_steel_cost_data_verification,
            trigger=IntervalTrigger(hours=24),
            id='steel_cost_data_verification_job',
            name='炼钢成本数据核对',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=300
        )

        # IX批次检查 - 每30分钟执行一次
        self.scheduler.add_job(
            func=self._execute_ix_batch_check,
            trigger=IntervalTrigger(hours=4),
            id='ix_batch_check_job',
            name='IX批次检查',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=300
        )

        self.scheduler.add_job(
            func=self._execute_daily_counting,
            trigger=IntervalTrigger(hours=3),
            id='daily_counting',
            name='定时日盘生成',
            max_instances=1,
            coalesce=True,
            misfire_grace_time=300
        )


        logger.info("定时任务已添加")

    async def _execute_daily_counting(self):
        """定时执行日盘生成"""
        logger.info("定时执行日盘任务的生成")
        service = DailyCounting()
        await service.start_invoke_daily_counting()

    async def _execute_data_comparison(self):
        """执行物料消耗数据对比任务"""
        try:
            logger.info("开始执行物料消耗数据对比任务")

            async for db in get_db():
                service = DataComparisonService(db)


                yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')


                task_1 = await service.create_task(
                    start_date=yesterday,
                    end_date=yesterday,
                    factory=Factory.FACTORY_1,
                    user_id=0
                )

                await service.start_task(task_1.id, None)


                task_2 = await service.create_task(
                    start_date=yesterday,
                    end_date=yesterday,
                    factory=Factory.FACTORY_2,
                    user_id=0
                )

                await service.start_task(task_2.id, None)

                logger.info(f"定时数据对比任务执行完成: 一厂任务ID={task_1.id}, 二厂任务ID={task_2.id}")
                break

        except Exception as e:
            logger.error(f"定时数据对比任务执行失败: {e}")
    
    async def _execute_erp_message_check(self):
        """执行ERP电文检查任务"""
        try:
            logger.info("开始执行定时ERP电文检查任务")

            async for db in get_db():
                service = MessageCheckService(db)


                today = datetime.now().strftime('%Y-%m-%d')


                task_id_receive = await service.create_message_check_task(
                    check_date=today,
                    message_type=MessageType.RECEIVE,
                    user_id=0
                )

                await service.start_message_check_task(task_id_receive, MessageType.RECEIVE, None)


                task_id_send = await service.create_message_check_task(
                    check_date=today,
                    message_type=MessageType.SEND,
                    user_id=0
                )

                await service.start_message_check_task(task_id_send, MessageType.SEND, None)

                logger.info(f"定时ERP电文检查任务执行完成: 接收任务ID={task_id_receive}, 发送任务ID={task_id_send}")
                break

        except Exception as e:
            logger.error(f"定时ERP电文检查任务执行失败: {e}")
    
    async def _execute_mes_message_check(self):
        """执行MES电文检查任务"""
        try:
            logger.info("开始执行定时MES电文检查任务")

            async for db in get_db():
                service = MESMessageCheckService(db)


                today = datetime.now().strftime('%Y-%m-%d')


                task_id_receive = await service.create_mes_message_check_task(
                    check_date=today,
                    message_type=MessageType.RECEIVE,
                    user_id=0
                )

                await service.start_mes_message_check_task(task_id_receive, MessageType.RECEIVE, None)


                task_id_send = await service.create_mes_message_check_task(
                    check_date=today,
                    message_type=MessageType.SEND,
                    user_id=0
                )

                await service.start_mes_message_check_task(task_id_send, MessageType.SEND, None)

                logger.info(f"定时MES电文检查任务执行完成: 接收任务ID={task_id_receive}, 发送任务ID={task_id_send}")
                break

        except Exception as e:
            logger.error(f"定时MES电文检查任务执行失败: {e}")

    async def _execute_ix_receiva_check(self):
        """执行IX收账作业检查任务"""
        try:
            logger.info("开始执行定时IX收账作业检查任务")

            async for db in get_db():
                service = IxReceivaService(db)

                # 获取当前日期
                today = datetime.now().strftime('%Y%m%d')

                # 为IB和IS系统分别创建检查任务
                for system_id in ['IB', 'IS']:
                    task_id = await service.create_check_task(
                        system_id=system_id,
                        start_date=today,
                        end_date=today,
                        status_filter=['error', 'processing', 'ip_error'],
                        user_id=0,  # 系统用户
                        is_auto_task=True,
                        min_duration_minutes=3  # 只检查耗时超过3分钟的记录
                    )

                    # 定义进度回调函数（定时任务不需要WebSocket通知）
                    async def progress_callback(progress: int, message: str):
                        logger.info(f"IX收账作业检查进度 - {system_id}: {progress}% - {message}")

                    # 执行检查任务
                    await service.start_check_task(task_id, progress_callback)

                    logger.info(f"定时IX收账作业检查任务执行完成: 系统={system_id}, 任务ID={task_id}")

                break

        except Exception as e:
            logger.error(f"定时IX收账作业检查任务执行失败: {e}")

    async def _execute_steel_cost_data_verification(self):
        """执行炼钢成本数据核对任务"""
        try:
            logger.info("开始执行定时炼钢成本数据核对任务")

            async for db in get_db():
                from app.services.steel_making.erp.steel_cost_data_verification import SteelCostDataVerificationService
                service = SteelCostDataVerificationService(db)

                # 获取昨天的日期
                yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

                # 为一厂和二厂分别创建核对任务
                for factory in [Factory.FACTORY_1, Factory.FACTORY_2]:
                    task = await service.create_task(
                        check_date=yesterday,
                        factory=factory,
                        user_id=0  # 系统用户
                    )

                    # 定义进度回调函数（定时任务不需要WebSocket通知）
                    async def progress_callback(task_id: int, progress: int, message: str, data=None):
                        logger.info(f"炼钢成本数据核对进度 - {factory.value}: {progress}% - {message}")

                    # 执行核对任务
                    await service.start_task(task.id, progress_callback)

                    logger.info(f"定时炼钢成本数据核对任务执行完成: 厂别={factory.value}, 任务ID={task.id}")

                break

        except Exception as e:
            logger.error(f"定时炼钢成本数据核对任务执行失败: {e}")

    async def _execute_ix_batch_check(self):
        """执行IX批次检查任务"""
        try:
            logger.info("开始执行定时IX批次检查任务")

            async for db in get_db():
                from app.services.steel_making.erp.ix_batch_check import IxBatchCheckService
                service = IxBatchCheckService(db)

                # 创建检查任务
                task_id = await service.create_check_task(user_id=0)  # 系统用户

                # 定义进度回调函数（定时任务不需要WebSocket通知）
                async def progress_callback(task_id: int, progress: int, message: str, data=None):
                    logger.info(f"IX批次检查进度: {progress}% - {message}")

                # 执行检查任务
                await service.start_check_task(task_id, progress_callback)

                logger.info(f"定时IX批次检查任务执行完成: 任务ID={task_id}")
                break

        except Exception as e:
            logger.error(f"定时IX批次检查任务执行失败: {e}")

    def get_job_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        if not self.is_running:
            return {"status": "stopped", "jobs": []}
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "running",
            "jobs": jobs
        }



maintenance_scheduler = MaintenanceScheduler()
