import json
import time
from typing import Dict, List
from fastapi import WebSocket
from app.utils.logger import logger

class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        self.active_connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        
        if client_id not in self.active_connections:
            self.active_connections[client_id] = []
        
        self.active_connections[client_id].append(websocket)
        logger.info(f"WebSocket客户端 {client_id} 已连接")
    
    def disconnect(self, websocket: WebSocket, client_id: str):
        """断开WebSocket连接"""
        if client_id in self.active_connections:
            if websocket in self.active_connections[client_id]:
                self.active_connections[client_id].remove(websocket)
            
            # 如果该客户端没有其他连接，删除客户端记录
            if not self.active_connections[client_id]:
                del self.active_connections[client_id]
        
        logger.info(f"WebSocket客户端 {client_id} 已断开连接")
    
    async def send_personal_message(self, message: str, client_id: str):
        """发送个人消息"""
        logger.info(f"尝试发送消息到客户端 {client_id}")
        logger.info(f"当前活跃连接: {list(self.active_connections.keys())}")

        if client_id in self.active_connections:
            # 向该客户端的所有连接发送消息
            disconnected_connections = []

            for connection in self.active_connections[client_id]:
                try:
                    await connection.send_text(message)
                    logger.info(f"消息发送成功到客户端 {client_id}")
                except Exception as e:
                    logger.error(f"发送消息失败: {e}")
                    disconnected_connections.append(connection)

            # 清理断开的连接
            for connection in disconnected_connections:
                self.disconnect(connection, client_id)
        else:
            logger.warning(f"客户端 {client_id} 未连接，无法发送消息")
    
    async def broadcast(self, message: str):
        """广播消息给所有连接"""
        for client_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, client_id)
    
    async def send_task_progress(self, task_id: int, progress: int, message: str, data=None, client_id: str = None):
        """发送任务进度更新"""
        progress_data = {
            "type": "task_progress",
            "task_id": task_id,
            "progress": progress,
            "message": message,
            "timestamp": int(time.time() * 1000)
        }

        # 如果有额外数据，添加到消息中
        if data is not None:
            progress_data["data"] = data

        message_json = json.dumps(progress_data, ensure_ascii=False)

        if client_id:
            await self.send_personal_message(message_json, client_id)
        else:
            await self.broadcast(message_json)
    
    async def send_task_completed(self, task_id: int, success: bool, result_summary: str = None, client_id: str = None, data=None):
        """发送任务完成通知"""
        completion_data = {
            "type": "task_completed",
            "task_id": task_id,
            "success": success,
            "result_summary": result_summary,
            "timestamp": int(time.time() * 1000)
        }

        # 如果有额外数据，添加到消息中
        if data is not None:
            completion_data["data"] = data

        message_json = json.dumps(completion_data, ensure_ascii=False)

        if client_id:
            await self.send_personal_message(message_json, client_id)
        else:
            await self.broadcast(message_json)

# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
