"""
服务工厂
统一管理服务实例的创建和依赖注入
"""

from typing import Dict, Type, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.system.user import UserService
from app.utils.logger import logger


class ServiceFactory:
    """服务工厂类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self._services: Dict[str, Any] = {}
    
    def get_user_service(self) -> UserService:
        """获取用户服务实例"""
        if 'user_service' not in self._services:
            self._services['user_service'] = UserService(self.db)
            logger.debug("创建用户服务实例")
        return self._services['user_service']
    
    def clear_cache(self):
        """清除服务缓存"""
        self._services.clear()
        logger.debug("清除服务缓存")


# 全局服务工厂实例缓存
_service_factories: Dict[int, ServiceFactory] = {}


def get_service_factory(db: AsyncSession) -> ServiceFactory:
    """获取服务工厂实例"""
    db_id = id(db)
    if db_id not in _service_factories:
        _service_factories[db_id] = ServiceFactory(db)
    return _service_factories[db_id]


def clear_service_factory_cache():
    """清除服务工厂缓存"""
    global _service_factories
    _service_factories.clear()
    logger.debug("清除全局服务工厂缓存")
