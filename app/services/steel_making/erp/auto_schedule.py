from typing import List, Dict, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime

from app.models.steel_making.erp.auto_schedule import AutoScheduleTask, AutoScheduleStep
from app.models.common import TaskStatus, StepStatus
from app.services.common.erp_client import ER<PERSON>lient
from app.config.settings import settings
from app.utils.logger import logger


class AutoScheduleService:
    """自动排程服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        # ERP登录凭据 - 从配置中获取
        self.erp_username = settings.ERP_WEB_USERNAME
        self.erp_password = settings.ERP_WEB_PASSWORD
        
        # 排程任务配置
        self.schedule_configs = {
            "POSTING": {
                "name": "自动排程抛账",
                "tasks": [
                    {"name": "OJRELOADMTRL", "order": 1, "description": "合金辅料耗用汇总"},
                    {"name": "OJ32To33", "order": 2, "description": "OJ32To33数据传输"},
                    {"name": "OJ33ToMR", "order": 3, "description": "OJ33ToMR数据传输"}
                ]
            },
            "IB_IX_IP": {
                "name": "自动排程IB=>IX=>IP",
                "tasks": [
                    {"name": "IX-IB-2-IX", "order": 1, "description": "IX-IB-2-IX数据传输"},
                    {"name": "IX-IX-2-IP-J02", "order": 2, "description": "IX-IX-2-IP-J02数据传输"}
                ]
            },
            "IS_IX_IP": {
                "name": "自动排程IS=>IX=>IP",
                "tasks": [
                    {"name": "IX-IS-2-IX", "order": 1, "description": "IX-IS-2-IX数据传输"},
                    {"name": "IX-IX-2-IP-J02", "order": 2, "description": "IX-IX-2-IP-J02数据传输"}
                ]
            }
        }
    
    async def create_schedule_task(self, user_id: int, task_type: str = "POSTING") -> int:
        """创建自动排程任务"""
        if task_type not in self.schedule_configs:
            raise ValueError(f"不支持的任务类型: {task_type}")

        config = self.schedule_configs[task_type]
        task_name = f"{config['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        task = AutoScheduleTask(
            task_name=task_name,
            task_type=task_type,
            status=TaskStatus.PENDING,
            progress=0,
            created_by=user_id
        )

        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)

        # 创建步骤记录
        for schedule_task in config["tasks"]:
            step = AutoScheduleStep(
                task_id=task.id,
                step_name=schedule_task["name"],
                step_order=schedule_task["order"],
                status=StepStatus.PENDING
            )
            self.db.add(step)

        await self.db.commit()

        logger.info(f"创建自动排程任务: {task.id}, 类型: {task_type}")
        return task.id
    
    async def start_schedule_task(self, task_id: int, progress_callback: Optional[Callable] = None):
        """启动自动排程任务"""
        # 更新任务状态为运行中
        await self._update_task_status(
            task_id, 
            TaskStatus.RUNNING, 
            progress=0,
            started_at=datetime.now()
        )
        
        try:
            # 获取任务信息
            task = await self._get_task(task_id)
            if not task:
                raise Exception(f"任务 {task_id} 不存在")
            
            # 执行自动排程
            await self._execute_schedule(task, progress_callback)
            
            # 更新任务状态为完成
            await self._update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                progress=100,
                completed_at=datetime.now()
            )
            
            logger.info(f"自动排程任务 {task_id} 执行完成")
            
        except Exception as e:
            logger.error(f"自动排程任务执行失败: {e}")
            await self._update_task_status(
                task_id,
                TaskStatus.FAILED,
                progress=0,
                error_message=str(e),
                completed_at=datetime.now()
            )
            raise
    
    async def _execute_schedule(self, task: AutoScheduleTask, progress_callback: Optional[Callable] = None):
        """执行自动排程逻辑"""
        try:
            step_timings = {}
            execution_results = []
            
            # 步骤1：ERP连接和登录
            if progress_callback:
                await progress_callback(task.id, 2, "步骤1：正在连接ERP系统...")

            async with ERPClient() as erp_client:
                # 先测试连接
                connection_ok = await erp_client.test_connection()
                if not connection_ok:
                    raise Exception("ERP系统连接失败，请检查网络或服务器状态")

                if progress_callback:
                    await progress_callback(task.id, 5, "步骤1：ERP系统连接成功，正在登录...")

                # 尝试登录
                logger.info(f"开始ERP登录，用户名: {self.erp_username}")
                login_success = await erp_client.login(self.erp_username, self.erp_password)
                if not login_success:
                    raise Exception(f"ERP系统登录失败，用户名: {self.erp_username}，请检查用户名和密码")

                logger.info("ERP登录成功，继续执行排程任务")
                if progress_callback:
                    await progress_callback(task.id, 10, "步骤1：ERP系统登录成功")
                
                # 获取任务配置
                if task.task_type not in self.schedule_configs:
                    raise Exception(f"不支持的任务类型: {task.task_type}")

                config = self.schedule_configs[task.task_type]
                schedule_tasks = config["tasks"]

                # 执行各个排程任务
                total_steps = len(schedule_tasks)
                for i, schedule_task in enumerate(schedule_tasks):
                    step_name = schedule_task["name"]
                    step_description = schedule_task["description"]
                    
                    # 计算进度 (10% 登录 + 90% 执行任务)
                    step_progress_start = 10 + (i * 90 // total_steps)
                    step_progress_end = 10 + ((i + 1) * 90 // total_steps)
                    
                    if progress_callback:
                        await progress_callback(
                            task.id, 
                            step_progress_start, 
                            f"步骤{i+2}：正在执行 {step_description}..."
                        )
                    
                    # 更新步骤状态为运行中
                    await self._update_step_status(task.id, step_name, StepStatus.RUNNING, datetime.now())
                    
                    # 执行排程任务
                    step_start_time = datetime.now()

                    if progress_callback:
                        await progress_callback(
                            task.id,
                            step_progress_start + 10,
                            f"步骤{i+2}：正在触发 {step_description}..."
                        )

                    # 触发排程任务
                    trigger_success = await erp_client.trigger_schedule(step_name)
                    if not trigger_success:
                        raise Exception(f"触发排程任务 {step_name} 失败")

                    if progress_callback:
                        await progress_callback(
                            task.id,
                            step_progress_start + 20,
                            f"步骤{i+2}：{step_description} 已触发，正在等待执行完成..."
                        )

                    # 等待任务完成 (5秒轮询间隔)
                    wait_result = await erp_client.wait_for_task_completion(step_name, max_wait_seconds=300, check_interval=5)

                    step_end_time = datetime.now()
                    duration = (step_end_time - step_start_time).total_seconds()
                    step_timings[step_name] = duration

                    # 构建结果
                    result = {
                        "success": wait_result["success"],
                        "duration": duration,
                        "task_info": wait_result.get("task_info"),
                        "error": wait_result.get("error")
                    }
                    
                    if result["success"]:
                        # 步骤成功
                        await self._update_step_status(
                            task.id, 
                            step_name, 
                            StepStatus.COMPLETED, 
                            step_start_time,
                            step_end_time,
                            duration,
                            result.get("task_info")
                        )
                        
                        execution_results.append({
                            "step": step_name,
                            "description": step_description,
                            "success": True,
                            "duration": duration,
                            "task_info": result.get("task_info")
                        })
                        
                        if progress_callback:
                            await progress_callback(
                                task.id, 
                                step_progress_end, 
                                f"步骤{i+2}：{step_description} 执行完成 (耗时: {duration:.2f}秒)"
                            )
                    else:
                        # 步骤失败
                        error_msg = result.get("error", "未知错误")
                        await self._update_step_status(
                            task.id, 
                            step_name, 
                            StepStatus.FAILED, 
                            step_start_time,
                            step_end_time,
                            duration,
                            result.get("task_info"),
                            error_msg
                        )
                        
                        execution_results.append({
                            "step": step_name,
                            "description": step_description,
                            "success": False,
                            "duration": duration,
                            "error": error_msg,
                            "task_info": result.get("task_info")
                        })
                        
                        raise Exception(f"{step_description} 执行失败: {error_msg}")
            
            # 更新任务结果
            await self._update_task_with_result(task.id, step_timings, execution_results)
            
            if progress_callback:
                total_time = sum(step_timings.values())
                await progress_callback(
                    task.id, 
                    100, 
                    f"所有排程任务执行完成！总耗时: {total_time:.2f}秒",
                    {
                        "step_timings": step_timings,
                        "execution_results": execution_results
                    }
                )
                
        except Exception as e:
            logger.error(f"执行自动排程失败: {e}")
            raise
    
    async def _update_step_status(
        self, 
        task_id: int, 
        step_name: str, 
        status: StepStatus,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None,
        duration: Optional[float] = None,
        execution_details: Optional[Dict] = None,
        error_message: Optional[str] = None
    ):
        """更新步骤状态"""
        update_data = {'status': status}
        
        if started_at is not None:
            update_data['started_at'] = started_at
        if completed_at is not None:
            update_data['completed_at'] = completed_at
        if duration is not None:
            update_data['duration_seconds'] = duration
        if execution_details is not None:
            update_data['execution_details'] = execution_details
        if error_message is not None:
            update_data['error_message'] = error_message
        
        stmt = update(AutoScheduleStep).where(
            AutoScheduleStep.task_id == task_id,
            AutoScheduleStep.step_name == step_name
        ).values(**update_data)
        
        await self.db.execute(stmt)
        await self.db.commit()
    
    async def _update_task_with_result(self, task_id: int, step_timings: Dict, execution_results: List[Dict]):
        """更新任务统计信息和结果JSON"""
        stmt = update(AutoScheduleTask).where(
            AutoScheduleTask.id == task_id
        ).values(
            step_timings=step_timings,
            execution_result=execution_results
        )

        await self.db.execute(stmt)
        await self.db.commit()
        logger.info(f"更新任务执行结果，任务ID: {task_id}")
    
    async def _update_task_status(
        self, 
        task_id: int, 
        status: TaskStatus, 
        progress: Optional[int] = None,
        error_message: Optional[str] = None,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None
    ):
        """更新任务状态"""
        update_data = {'status': status}
        
        if progress is not None:
            update_data['progress'] = progress
        if error_message is not None:
            update_data['error_message'] = error_message
        if started_at is not None:
            update_data['started_at'] = started_at
        if completed_at is not None:
            update_data['completed_at'] = completed_at
        
        stmt = update(AutoScheduleTask).where(
            AutoScheduleTask.id == task_id
        ).values(**update_data)
        
        await self.db.execute(stmt)
        await self.db.commit()
    
    async def _get_task(self, task_id: int) -> Optional[AutoScheduleTask]:
        """获取任务信息"""
        stmt = select(AutoScheduleTask).where(AutoScheduleTask.id == task_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_task_status(self, task_id: int) -> Optional[Dict]:
        """获取任务状态"""
        task = await self._get_task(task_id)
        if not task:
            return None

        # 获取步骤信息
        steps_stmt = select(AutoScheduleStep).where(AutoScheduleStep.task_id == task_id).order_by(AutoScheduleStep.step_order)
        steps_result = await self.db.execute(steps_stmt)
        steps = steps_result.scalars().all()

        return {
            'id': task.id,
            'task_name': task.task_name,
            'status': task.status.value,
            'progress': task.progress,
            'error_message': task.error_message,
            'step_timings': task.step_timings,
            'execution_result': task.execution_result,
            'steps': [
                {
                    'step_name': step.step_name,
                    'step_order': step.step_order,
                    'status': step.status.value,
                    'duration_seconds': step.duration_seconds,
                    'error_message': step.error_message
                } for step in steps
            ],
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None
        }
