"""
炉订号置换异常服务
"""

import asyncio
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger

class HeatNoSwitchService:
    """炉订号置换异常服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_heat_plan_mapping(self, heat_ids: List[str]) -> Dict:
        """查询炉号和计划炉号对照关系"""
        def query_mes():
            logger.info(f"开始查询MES数据库炉号对照关系，炉号: {heat_ids}")
            connection = oracle_manager.get_mes_connection()
            try:
                # 构建IN子句
                heat_id_placeholders = ','.join([f":heat_id_{i}" for i in range(len(heat_ids))])
                sql = f"""
                SELECT HEAT_ID, PLAN_HEAT_ID
                FROM jgpss.PPS_B_CHEAT_PLAN
                WHERE HEAT_ID IN ({heat_id_placeholders})
                """

                # 构建参数字典
                params = {f"heat_id_{i}": heat_id for i, heat_id in enumerate(heat_ids)}

                results = oracle_manager.execute_query(connection, sql, params)

                # 处理结果
                found_heat_ids = {result["HEAT_ID"] for result in results}
                result_list = []

                for heat_id in heat_ids:
                    if heat_id in found_heat_ids:
                        # 找到对应的结果
                        heat_result = next(r for r in results if r["HEAT_ID"] == heat_id)
                        result_list.append({
                            "heat_id": heat_result["HEAT_ID"],
                            "plan_heat_id": heat_result["PLAN_HEAT_ID"],
                            "found": True
                        })
                    else:
                        result_list.append({
                            "heat_id": heat_id,
                            "plan_heat_id": None,
                            "found": False
                        })

                logger.info(f"查询到 {len(results)} 条炉号对照关系")
                return {
                    "results": result_list,
                    "total_count": len(result_list)
                }

            except Exception as e:
                logger.error(f"查询MES数据库失败: {e}")
                raise
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行数据库查询
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_mes)
    
    async def get_heat_status(self, heat_nos: List[str]) -> Dict:
        """查询炉号状态"""
        def query_erp():
            logger.info(f"开始查询ERP数据库炉号状态，炉号: {heat_nos}")
            connection = oracle_manager.get_erp_connection()
            try:
                # 构建IN子句
                heat_no_placeholders = ','.join([f":heat_no_{i}" for i in range(len(heat_nos))])
                sql = f"""
                SELECT heatno, status
                FROM db.tbws101
                WHERE heatno IN ({heat_no_placeholders})
                """

                # 构建参数字典
                params = {f"heat_no_{i}": heat_no for i, heat_no in enumerate(heat_nos)}

                results = oracle_manager.execute_query(connection, sql, params)

                # 处理结果
                found_heat_nos = {result["HEATNO"] for result in results}
                result_list = []

                for heat_no in heat_nos:
                    if heat_no in found_heat_nos:
                        # 找到对应的结果
                        heat_result = next(r for r in results if r["HEATNO"] == heat_no)
                        result_list.append({
                            "heat_no": heat_result["HEATNO"],
                            "status": heat_result["STATUS"],
                            "found": True
                        })
                    else:
                        result_list.append({
                            "heat_no": heat_no,
                            "status": None,
                            "found": False
                        })

                logger.info(f"查询到 {len(results)} 条炉号状态")
                return {
                    "results": result_list,
                    "total_count": len(result_list)
                }

            except Exception as e:
                logger.error(f"查询ERP数据库失败: {e}")
                raise
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行数据库查询
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_erp)
    
    async def get_message_assist(self, heat_no: Optional[str], check_date: str) -> Dict:
        """电文辅助查询"""
        def query_erp():
            logger.info(f"开始电文辅助查询，炉号: {heat_no}, 日期: {check_date}")
            connection = oracle_manager.get_erp_connection()
            try:
                # 转换日期格式为YYYYMMDD
                date_obj = datetime.strptime(check_date, "%Y-%m-%d")
                formatted_date = date_obj.strftime("%Y%m%d")
                
                # 构建SQL查询
                sql = """
                SELECT
                    RECSYSDATE,
                    RECSYSTIME,
                    JSON_VALUE(DATASTR, '$.HEATNO') AS HEATNO,
                    FORMID,
                    DATASTR
                FROM DB.TBDZPQUEUEREC
                WHERE QUEUEID LIKE '%REV'
                  AND RECSYSDATE >= :check_date
                  AND (
                            FORMID IN ('CMOJ10', 'CMOJ05', 'CMOJ05_1')
                        OR
                            (FORMID NOT IN ('CMOJ10', 'CMOJ05', 'CMOJ05_1') AND DATASTUS = 'F')
                    )
                    AND FORMID NOT LIKE '%CMWS%'
                """
                
                params = {"check_date": formatted_date}
                
                # 如果提供了炉号，添加炉号过滤条件
                if heat_no:
                    sql += " AND (DATASTR LIKE :heat_no_pattern)"
                    params["heat_no_pattern"] = f"%{heat_no}%"
                
                sql += """
                ORDER BY
                    JSON_VALUE(DATASTR, '$.HEATNO'),
                    CASE FORMID
                        -- 连铸实绩
                        WHEN 'CMOJ05' THEN 7
                        -- 连铸实绩
                        WHEN 'CMOJ05_1' THEN 8
                        -- 合金辅料实绩
                        WHEN 'CMOJ10' THEN 9
                        -- 回炉钢水实绩
                        WHEN 'CMOJ11' THEN 10
                        -- 方坯产出实绩
                        WHEN 'XMIB01' THEN 11
                        -- 方坯入库及储位变更
                        WHEN 'XMIB03' THEN 14
                        -- 方坯存货移动
                        WHEN 'XMIB04' THEN 15
                        -- 方坯出库
                        WHEN 'XMIB05' THEN 16
                        -- 方坯低倍处置
                        WHEN 'XMIB06' THEN 17
                        -- 方坯并炉同步
                        WHEN 'XMIB07' THEN 17
                        -- 板坯产出
                        WHEN 'CMIS01' THEN 18
                        -- 板坯入库及储位变更
                        WHEN 'CMIS03' THEN 19
                        -- 板坯存货异动
                        WHEN 'CMIS04' THEN 20
                        -- 板坯出库信息
                        WHEN 'CMIS05' THEN 21
                        -- 生产日报栏位同步
                        WHEN 'CMIS07' THEN 22
                        ELSE 999
                        END,
                    RECSYSDATE,
                    RECSYSTIME
                """
                
                results = oracle_manager.execute_query(connection, sql, params)
                
                # 转换结果格式
                records = []
                for result in results:
                    records.append({
                        "recsysdate": result["RECSYSDATE"],
                        "recsystime": result["RECSYSTIME"],
                        "heatno": result["HEATNO"] or "",
                        "formid": result["FORMID"],
                        "datastr": result["DATASTR"]
                    })
                
                logger.info(f"电文辅助查询完成，返回 {len(records)} 条记录")
                return {
                    "records": records,
                    "total_count": len(records),
                    "check_date": check_date
                }
                    
            except Exception as e:
                logger.error(f"电文辅助查询失败: {e}")
                raise
            finally:
                oracle_manager.close_connection(connection)
        
        # 在线程池中执行数据库查询
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_erp)
