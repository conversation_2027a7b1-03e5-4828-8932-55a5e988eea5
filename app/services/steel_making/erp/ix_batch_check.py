"""
IX批次检查服务
"""

import asyncio
from typing import Dict, List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime

from app.models.steel_making.erp.ix_batch_check import IxBatchCheckTask
from app.models.common import TaskStatus
from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger

class IxBatchCheckService:
    """IX批次检查服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_check_task(self, user_id: int) -> int:
        """创建IX批次检查任务"""
        task_name = f"IX批次检查_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        task = IxBatchCheckTask(
            task_name=task_name,
            status=TaskStatus.PENDING,
            progress=0,
            error_count=0,
            created_by=user_id
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(f"创建IX批次检查任务: {task.id}")
        return task.id
    
    async def start_check_task(self, task_id: int, progress_callback: Optional[Callable] = None):
        """启动IX批次检查任务"""
        # 更新任务状态为运行中
        await self._update_task_status(
            task_id, 
            TaskStatus.RUNNING, 
            progress=0,
            started_at=datetime.now()
        )
        
        try:
            # 获取任务信息
            task = await self._get_task(task_id)
            if not task:
                raise Exception(f"任务 {task_id} 不存在")

            # 执行IX批次检查
            await self._execute_ix_batch_check(task, progress_callback)
            
            # 更新任务状态为完成
            await self._update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                progress=100,
                completed_at=datetime.now()
            )
            
            logger.info(f"IX批次检查任务 {task_id} 执行完成")
            
        except Exception as e:
            logger.error(f"IX批次检查任务 {task_id} 执行失败: {e}")
            await self._update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e),
                completed_at=datetime.now()
            )
            raise
    
    async def _execute_ix_batch_check(self, task: IxBatchCheckTask, progress_callback: Optional[Callable] = None):
        """执行IX批次检查逻辑"""
        try:
            if progress_callback:
                await progress_callback(task.id, 10, "步骤1：正在连接ERP数据库...")

            await self._test_erp_connection()
            
            if progress_callback:
                await progress_callback(task.id, 20, "步骤1：ERP数据库连接成功")

            if progress_callback:
                await progress_callback(task.id, 30, "步骤2：正在查询IX批次错误日志...")

            error_records = await self._get_batch_error_records()

            if progress_callback:
                await progress_callback(task.id, 70, f"步骤2：查询完成 - 发现{len(error_records)}条错误记录")

            if progress_callback:
                await progress_callback(task.id, 90, "步骤3：正在生成检查结果...")

            # 生成检查结果JSON
            check_result = {
                "summary": {
                    "check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "error_count": len(error_records),
                    "system": "IX"
                },
                "error_records": error_records
            }

            await self._update_task_with_result(task.id, len(error_records), check_result)

            if progress_callback:
                await progress_callback(task.id, 100, f"步骤3：检查完成 - 共发现{len(error_records)}条错误记录", check_result)

        except Exception as e:
            logger.error(f"执行IX批次检查失败: {e}")
            raise

    async def _test_erp_connection(self):
        """测试ERP数据库连接"""
        def test_connection():
            connection = oracle_manager.get_erp_connection()
            try:
                oracle_manager.execute_query(connection, "SELECT 1 FROM DUAL")
                logger.info("ERP数据库连接测试成功")
            finally:
                oracle_manager.close_connection(connection)
        
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, test_connection)

    async def _get_batch_error_records(self) -> List[Dict]:
        """获取IX批次错误记录"""
        def query_error_records():
            logger.info("开始查询IX批次错误记录")
            connection = oracle_manager.get_erp_connection()
            try:
                sql = """
                select BATCHNO, SYSID, STARTDATE, STARTTIME, EXECEMPNO, EXECRESULT, EXECMSG
                from DB.TBIXBBATLOG
                where EXECRESULT = 'E'
                """
                
                results = oracle_manager.execute_query(connection, sql)
                
                # 转换结果格式
                error_records = []
                for result in results:
                    error_records.append({
                        "batchno": result["BATCHNO"],
                        "sysid": result["SYSID"],
                        "startdate": result["STARTDATE"],
                        "starttime": result["STARTTIME"],
                        "execempno": result["EXECEMPNO"],
                        "execresult": result["EXECRESULT"],
                        "execmsg": result["EXECMSG"]
                    })
                
                logger.info(f"查询到 {len(error_records)} 条IX批次错误记录")
                return error_records
                
            except Exception as e:
                logger.error(f"查询IX批次错误记录失败: {e}")
                raise
            finally:
                oracle_manager.close_connection(connection)
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_error_records)

    async def _get_task(self, task_id: int) -> Optional[IxBatchCheckTask]:
        """获取任务信息"""
        result = await self.db.execute(
            select(IxBatchCheckTask).where(IxBatchCheckTask.id == task_id)
        )
        return result.scalar_one_or_none()

    async def _update_task_status(
        self, 
        task_id: int, 
        status: TaskStatus, 
        progress: int = None,
        error_message: str = None,
        started_at: datetime = None,
        completed_at: datetime = None
    ):
        """更新任务状态"""
        task = await self._get_task(task_id)
        if task:
            task.status = status
            if progress is not None:
                task.progress = progress
            if error_message is not None:
                task.error_message = error_message
            if started_at is not None:
                task.started_at = started_at
            if completed_at is not None:
                task.completed_at = completed_at
            
            await self.db.commit()

    async def _update_task_with_result(self, task_id: int, error_count: int, check_result: dict):
        """更新任务结果"""
        task = await self._get_task(task_id)
        if task:
            task.error_count = error_count
            task.check_result = check_result
            await self.db.commit()

    async def get_task_status(self, task_id: int) -> Optional[Dict]:
        """获取任务状态"""
        task = await self._get_task(task_id)
        if not task:
            return None
        
        return {
            "id": task.id,
            "task_name": task.task_name,
            "status": task.status.value,
            "progress": task.progress,
            "error_count": task.error_count,
            "error_message": task.error_message,
            "check_result": task.check_result,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None
        }
