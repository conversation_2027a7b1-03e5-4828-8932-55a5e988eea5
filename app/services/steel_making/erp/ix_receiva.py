"""
IX收账作业检查服务
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func

from app.models.steel_making.erp.ix_receiva import IxReceivaTask
from app.utils.logger import logger
from app.services.common.oracle_connection import oracle_manager

class IxReceivaService:
    """IX收账作业检查服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_check_task(
        self,
        system_id: str,
        start_date: str,
        end_date: str,
        status_filter: Optional[List[str]],
        user_id: int,
        is_auto_task: bool = False,
        min_duration_minutes: Optional[int] = None
    ) -> int:
        """创建IX收账作业检查任务"""
        try:
            task_name = f"IX收账作业检查-{system_id}-{start_date}到{end_date}"
            if is_auto_task:
                task_name = f"自动{task_name}"
            
            task = IxReceivaTask(
                task_name=task_name,
                system_id=system_id,
                start_date=start_date,
                end_date=end_date,
                status_filter=json.dumps(status_filter) if status_filter else None,
                user_id=user_id,
                is_auto_task=is_auto_task,
                min_duration_minutes=min_duration_minutes,
                status="pending"
            )
            
            self.db.add(task)
            await self.db.commit()
            await self.db.refresh(task)
            
            logger.info(f"创建IX收账作业检查任务成功，任务ID: {task.id}")
            return task.id
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建IX收账作业检查任务失败: {e}")
            raise
    
    async def start_check_task(
        self,
        task_id: int,
        progress_callback
    ):
        """执行IX收账作业检查任务"""
        try:
            # 获取任务信息
            result = await self.db.execute(
                select(IxReceivaTask).where(IxReceivaTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise ValueError(f"任务不存在: {task_id}")
            
            # 更新任务状态
            task.status = "running"
            task.started_at = datetime.utcnow()
            await self.db.commit()
            
            if progress_callback:
                await progress_callback(10, "开始检查IX收账作业...")

            # 执行SQL查询
            check_results = await self._execute_ix_receiva_check(
                task.system_id,
                task.start_date,
                task.end_date,
                json.loads(task.status_filter) if task.status_filter else None,
                task.min_duration_minutes,
                progress_callback
            )

            if progress_callback:
                await progress_callback(80, "处理检查结果...")

            # 保存检查结果
            await self._save_check_results(task_id, check_results)

            # 更新任务统计
            await self._update_task_statistics(task_id, check_results)

            if progress_callback:
                await progress_callback(100, "IX收账作业检查完成")
            
            # 更新任务状态
            task.status = "completed"
            task.completed_at = datetime.utcnow()
            task.execution_time = (task.completed_at - task.started_at).total_seconds()
            await self.db.commit()

            logger.info(f"IX收账作业检查任务完成，任务ID: {task_id}")
            logger.info(f"任务统计: 总数={task.total_count}, 错误={task.error_count}, 处理中={task.processing_count}, IP错误={task.ip_error_count}")
            
        except Exception as e:
            # 更新任务状态为失败
            result = await self.db.execute(
                select(IxReceivaTask).where(IxReceivaTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            if task:
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = datetime.utcnow()
                if task.started_at:
                    task.execution_time = (task.completed_at - task.started_at).total_seconds()
                await self.db.commit()
            
            logger.error(f"IX收账作业检查任务失败，任务ID: {task_id}, 错误: {e}")
            raise
    
    async def _execute_ix_receiva_check(
        self,
        system_id: str,
        start_date: str,
        end_date: str,
        status_filter: Optional[List[str]],
        min_duration_minutes: Optional[int],
        progress_callback
    ) -> List[Dict[str, Any]]:
        """执行IX收账作业检查SQL"""
        try:
            if progress_callback:
                await progress_callback(20, f"查询{system_id}系统收账作业数据...")
            
            # 构建Oracle SQL查询（恢复原始SQL格式）
            sql = """
            select BATCHNO,SYSID,STARTDATE,STARTTIME,ENDDATE,ENDTIME,EXECEMPNO,EXECRESULT,EXECMSG,succeed,error,processing,ipSucceed,ipError,ipUnProcess
            from (select a.*
                       , (select count(1) from db.tbixBSysParam where batchNo = '(O)' || a.batchNo)        as succeed
                       , (select count(1) from db.tbixBSysParam where batchNo = '(E)' || a.batchNo)        as error
                       , (select count(1) from db.tbixBSysParam where batchNo = '(P)' || a.batchNo)        as processing
                       , (select count(1) from db.tbixEAcctLog where batchNo = a.batchNo and ipStus = 'S') as ipSucceed
                       , (select count(1) from db.tbixEAcctLog where batchNo = a.batchNo and ipStus = 'F') as ipError
                       , (select count(1) from db.tbixEAcctLog where batchNo = a.batchNo and ipStus = '')  as ipUnProcess
                  from db.tbixBBatLog a
                  where 1 = 1
                    and STARTDATE >= :start_date
                    and STARTDATE <= :end_date
                    and sysId = :system_id
                  order by batchNo desc)
            where succeed + error + processing + ipSucceed + ipError + ipUnProcess > 0
            """
            
            # 添加状态筛选条件（与您原始SQL保持一致）
            if status_filter:
                conditions = []
                if 'error' in status_filter:
                    conditions.append("error > 0")
                if 'processing' in status_filter:
                    conditions.append("processing > 0")
                if 'ip_error' in status_filter:
                    conditions.append("ipError > 0")

                if conditions:
                    sql += f" and ({' or '.join(conditions)})"
            else:
                # 默认只显示有异常的记录（与您原始SQL保持一致）
                sql += " and (error > 0 or processing > 0 or ipError > 0)"
            
            if progress_callback:
                await progress_callback(40, "执行Oracle数据库查询...")

            # 记录SQL和参数用于调试
            logger.info(f"执行IX收账作业检查SQL: {sql}")
            logger.info(f"SQL参数: start_date={start_date}, end_date={end_date}, system_id={system_id}")

            connection = oracle_manager.get_erp_connection()
            try:
                rows = oracle_manager.execute_query(connection, sql, {
                        "start_date": start_date,
                        "end_date": end_date,
                        "system_id": system_id
                    }
                )
                           
            finally:
                oracle_manager.close_connection(connection)

            logger.info(f"Oracle查询完成，返回 {len(rows)} 条原始记录")
            
            if progress_callback:
                await progress_callback(60, f"处理查询结果，共{len(rows)}条记录...")
            
            # 处理查询结果
            check_results = []
            for row in rows:
                # Oracle查询结果是元组，按列顺序访问
                # BATCHNO,SYSID,STARTDATE,STARTTIME,ENDDATE,ENDTIME,EXECEMPNO,EXECRESULT,EXECMSG,succeed,error,processing,ipSucceed,ipError,ipUnProcess
                batch_no = row[0]
                sys_id = row[1]
                start_date = str(row[2]) if row[2] else ""
                start_time = str(row[3]) if row[3] else ""
                end_date = str(row[4]) if row[4] else ""
                end_time = str(row[5]) if row[5] else ""
                exec_emp_no = row[6]
                exec_result = row[7]
                exec_msg = row[8]
                succeed = int(row[9]) if row[9] else 0
                error = int(row[10]) if row[10] else 0
                processing = int(row[11]) if row[11] else 0
                ip_succeed = int(row[12]) if row[12] else 0
                ip_error = int(row[13]) if row[13] else 0
                ip_unprocess = int(row[14]) if row[14] else 0

                # 计算耗时
                duration_minutes = self._calculate_duration(
                    start_date, start_time, end_date, end_time
                )

                # 生成状态描述
                status_desc = self._generate_status_description(
                    error, processing, ip_error, duration_minutes
                )

                # 如果是自动任务，只返回耗时过长的记录
                if min_duration_minutes and duration_minutes < min_duration_minutes:
                    continue

                result_item = {
                    "batch_no": batch_no,
                    "sys_id": sys_id,
                    "start_date": start_date,
                    "start_time": start_time,
                    "end_date": end_date,
                    "end_time": end_time,
                    "exec_emp_no": exec_emp_no,
                    "exec_result": exec_result,
                    "exec_msg": exec_msg,
                    "succeed": succeed,
                    "error": error,
                    "processing": processing,
                    "ip_succeed": ip_succeed,
                    "ip_error": ip_error,
                    "ip_unprocess": ip_unprocess,
                    "duration_minutes": duration_minutes,
                    "status_description": status_desc
                }

                check_results.append(result_item)
            
            logger.info(f"IX收账作业检查完成，系统: {system_id}, 异常记录数: {len(check_results)}")
            return check_results
            
        except Exception as e:
            logger.error(f"执行IX收账作业检查SQL失败: {e}")
            raise
    
    def _calculate_duration(self, start_date: str, start_time: str, end_date: str, end_time: str) -> float:
        """计算耗时分钟数"""
        try:
            if not all([start_date, start_time, end_date, end_time]):
                return 0.0
            
            # 解析开始时间
            start_datetime = datetime.strptime(f"{start_date}{start_time.zfill(6)}", "%Y%m%d%H%M%S")
            # 解析结束时间
            end_datetime = datetime.strptime(f"{end_date}{end_time.zfill(6)}", "%Y%m%d%H%M%S")
            
            # 计算时间差（分钟）
            duration = (end_datetime - start_datetime).total_seconds() / 60
            return max(0.0, duration)
            
        except Exception as e:
            logger.warning(f"计算耗时失败: {e}")
            return 0.0
    
    def _generate_status_description(self, error: int, processing: int, ip_error: int, duration: float) -> str:
        """生成状态描述"""
        descriptions = []
        
        if error > 0:
            descriptions.append(f"收账错误({error})")
        if processing > 0:
            descriptions.append(f"收账处理中({processing})")
        if ip_error > 0:
            descriptions.append(f"抛IP错误({ip_error})")
        if duration > 3:
            descriptions.append(f"耗时过长({duration:.1f}分钟)")
        
        return "; ".join(descriptions) if descriptions else "正常"

    async def _save_check_results(self, task_id: int, check_results: List[Dict[str, Any]]):
        """保存检查结果到数据库"""
        try:
            from app.models.steel_making.common.task_result_details import IxReceivaDetail

            for result_item in check_results:
                # 分析错误类型和建议解决方案
                error_type, suggested_sql = self._analyze_error_and_suggest_solution(result_item)

                detail_record = IxReceivaDetail(
                    task_id=task_id,
                    # 基本信息
                    batch_no=result_item["batch_no"],
                    sys_id=result_item["sys_id"],
                    # 时间信息
                    start_date=result_item["start_date"],
                    start_time=result_item["start_time"],
                    end_date=result_item["end_date"],
                    end_time=result_item["end_time"],
                    # 执行信息
                    exec_emp_no=result_item["exec_emp_no"],
                    exec_result=result_item["exec_result"],
                    exec_msg=result_item["exec_msg"],
                    # 状态统计
                    succeed=result_item["succeed"],
                    error=result_item["error"],
                    processing=result_item["processing"],
                    ip_succeed=result_item["ip_succeed"],
                    ip_error=result_item["ip_error"],
                    ip_unprocess=result_item["ip_unprocess"],
                    # 计算字段
                    duration_minutes=result_item["duration_minutes"],
                    status_description=result_item["status_description"],
                    # 错误分析
                    error_type=error_type,
                    error_message=result_item["exec_msg"] if result_item["exec_result"] == "E" else None,
                    suggested_sql=suggested_sql,
                    # 状态
                    status=self._determine_record_status(result_item),
                    check_time=datetime.now()
                )
                self.db.add(detail_record)

            await self.db.commit()
            logger.info(f"保存IX收账作业检查结果成功，任务ID: {task_id}, 记录数: {len(check_results)}")

        except Exception as e:
            await self.db.rollback()
            logger.error(f"保存IX收账作业检查结果失败: {e}")
            raise

    def _analyze_error_and_suggest_solution(self, result_item: Dict[str, Any]) -> tuple:
        """分析错误类型并提供解决方案"""
        exec_result = result_item.get("exec_result", "")
        error_count = result_item.get("error", 0)
        processing_count = result_item.get("processing", 0)
        ip_error_count = result_item.get("ip_error", 0)

        error_type = None
        suggested_sql = None

        if error_count > 0:
            error_type = "收账错误"
            suggested_sql = "update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(E)%';"
        elif processing_count > 0:
            error_type = "收账处理中"
            suggested_sql = "update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(P)%';"
        elif ip_error_count > 0:
            error_type = "抛送IP错误"
            suggested_sql = "update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(XE)%';"
        elif exec_result == "E":
            error_type = "执行错误"

        return error_type, suggested_sql

    def _determine_record_status(self, result_item: Dict[str, Any]) -> str:
        """确定记录状态"""
        error_count = result_item.get("error", 0)
        processing_count = result_item.get("processing", 0)
        ip_error_count = result_item.get("ip_error", 0)
        exec_result = result_item.get("exec_result", "")

        if exec_result == "E":
            return "执行错误"
        elif error_count > 0:
            return "收账错误"
        elif processing_count > 0:
            return "收账处理中"
        elif ip_error_count > 0:
            return "抛送IP错误"
        else:
            return "正常"

    async def _update_task_statistics(self, task_id: int, check_results: List[Dict[str, Any]]):
        """更新任务统计信息"""
        try:
            # 计算统计数据
            total_count = len(check_results)
            error_count = sum(1 for r in check_results if r["error"] > 0)
            processing_count = sum(1 for r in check_results if r["processing"] > 0)
            ip_error_count = sum(1 for r in check_results if r["ip_error"] > 0)
            long_duration_count = sum(1 for r in check_results if r["duration_minutes"] > 3)

            # 更新任务统计
            result = await self.db.execute(
                select(IxReceivaTask).where(IxReceivaTask.id == task_id)
            )
            task = result.scalar_one_or_none()

            if task:
                task.total_count = total_count
                task.error_count = error_count
                task.processing_count = processing_count
                task.ip_error_count = ip_error_count
                task.long_duration_count = long_duration_count
                task.result_data = json.dumps(check_results, ensure_ascii=False)

                await self.db.commit()
                logger.info(f"更新任务统计成功，任务ID: {task_id}")

        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新任务统计失败: {e}")
            raise

    async def get_task_status(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            result = await self.db.execute(
                select(IxReceivaTask).where(IxReceivaTask.id == task_id)
            )
            task = result.scalar_one_or_none()

            if not task:
                return None

            return {
                "id": task.id,
                "task_name": task.task_name,
                "system_id": task.system_id,
                "status": task.status,
                "progress": task.progress,
                "total_count": task.total_count,
                "error_count": task.error_count,
                "processing_count": task.processing_count,
                "ip_error_count": task.ip_error_count,
                "long_duration_count": task.long_duration_count,
                "execution_time": task.execution_time,
                "error_message": task.error_message,
                "created_at": task.created_at,
                "started_at": task.started_at,
                "completed_at": task.completed_at
            }

        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            raise

    async def get_task_results(self, task_id: int) -> Dict[str, Any]:
        """获取任务结果"""
        try:
            # 获取任务信息
            task_result = await self.db.execute(
                select(IxReceivaTask).where(IxReceivaTask.id == task_id)
            )
            task = task_result.scalar_one_or_none()

            if not task:
                raise ValueError(f"任务不存在: {task_id}")

            # 获取检查结果（从合并后的details表）
            from app.models.steel_making.common.task_result_details import IxReceivaDetail
            results_query = await self.db.execute(
                select(IxReceivaDetail).where(IxReceivaDetail.task_id == task_id)
            )
            results = results_query.scalars().all()

            # 构建返回数据
            check_results = []
            for result in results:
                check_results.append({
                    "batch_no": result.batch_no,
                    "sys_id": result.sys_id,
                    "start_date": result.start_date,
                    "start_time": result.start_time,
                    "end_date": result.end_date,
                    "end_time": result.end_time,
                    "exec_emp_no": result.exec_emp_no,
                    "exec_result": result.exec_result,
                    "exec_msg": result.exec_msg,
                    "succeed": result.succeed,
                    "error": result.error,
                    "processing": result.processing,
                    "ip_succeed": result.ip_succeed,
                    "ip_error": result.ip_error,
                    "ip_unprocess": result.ip_unprocess,
                    "duration_minutes": result.duration_minutes,
                    "status_description": result.status_description
                })

            return {
                "summary": {
                    "check_date": f"{task.start_date}-{task.end_date}",
                    "system_id": task.system_id,
                    "total_count": task.total_count,
                    "error_count": task.error_count,
                    "processing_count": task.processing_count,
                    "ip_error_count": task.ip_error_count,
                    "long_duration_count": task.long_duration_count
                },
                "results": check_results
            }

        except Exception as e:
            logger.error(f"获取任务结果失败: {e}")
            raise

    async def get_history(self, page: int = 1, size: int = 20, filters: dict = None) -> Dict[str, Any]:
        """获取IX收账作业检查历史记录"""
        try:
            # 构建查询条件
            query = select(IxReceivaTask).where(IxReceivaTask.status.in_(['completed', 'failed']))

            if filters:
                if 'start_date' in filters:
                    query = query.where(IxReceivaTask.start_date >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.where(IxReceivaTask.end_date <= filters['end_date'])
                if 'system_id' in filters:
                    query = query.where(IxReceivaTask.system_id == filters['system_id'])
                if 'status' in filters:
                    # 根据任务结果确定状态
                    if filters['status'] == 'success':
                        query = query.where(
                            and_(
                                IxReceivaTask.status == 'completed',
                                IxReceivaTask.error_count == 0,
                                IxReceivaTask.processing_count == 0,
                                IxReceivaTask.ip_error_count == 0
                            )
                        )
                    elif filters['status'] == 'warning':
                        query = query.where(
                            and_(
                                IxReceivaTask.status == 'completed',
                                or_(
                                    IxReceivaTask.error_count > 0,
                                    IxReceivaTask.processing_count > 0,
                                    IxReceivaTask.ip_error_count > 0
                                )
                            )
                        )
                    elif filters['status'] == 'error':
                        query = query.where(IxReceivaTask.status == 'failed')

            # 添加排序
            query = query.order_by(IxReceivaTask.created_at.desc())

            # 获取总数
            count_query = select(func.count()).select_from(query.subquery())
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()

            # 分页查询
            offset = (page - 1) * size
            query = query.offset(offset).limit(size)

            result = await self.db.execute(query)
            tasks = result.scalars().all()

            # 构建返回数据
            items = []
            for task in tasks:
                # 确定任务状态
                if task.status == 'failed':
                    status = 'error'
                elif task.error_count > 0 or task.processing_count > 0 or task.ip_error_count > 0:
                    status = 'warning'
                else:
                    status = 'success'

                items.append({
                    "id": task.id,
                    "task_id": task.id,
                    "system_id": task.system_id,
                    "check_date": f"{task.start_date}-{task.end_date}",
                    "total_count": task.total_count or 0,
                    "error_count": task.error_count or 0,
                    "processing_count": task.processing_count or 0,
                    "ip_error_count": task.ip_error_count or 0,
                    "long_duration_count": task.long_duration_count or 0,
                    "status": status,
                    "created_time": task.created_at.strftime('%Y-%m-%d %H:%M:%S') if task.created_at else '',
                    "execution_time": task.execution_time or 0
                })

            return {
                "total": total,
                "items": items
            }

        except Exception as e:
            logger.error(f"获取IX收账作业检查历史失败: {e}")
            raise
