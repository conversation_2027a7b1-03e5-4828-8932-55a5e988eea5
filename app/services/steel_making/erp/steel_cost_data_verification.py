import asyncio
from datetime import datetime
from typing import List, Dict, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from app.models.steel_making.erp.steel_cost_data_verification import SteelCostDataVerificationTask
from app.models.steel_making.common.task_result_details import SteelCostVerificationDetail
from app.models.common import TaskStatus, Factory
from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger


class SteelCostDataVerificationService:
    """炼钢成本数据核对服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_task(
        self,
        start_date: str,
        end_date: str,
        factory: Factory,
        heat_no: Optional[str] = None,
        material_code: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> SteelCostDataVerificationTask:
        """创建炼钢成本数据核对任务"""
        task_name = f"炼钢成本数据核对_{factory.value}_{start_date}"
        if start_date != end_date:
            task_name += f"至{end_date}"
        if heat_no:
            task_name += f"_{heat_no}"
        if material_code:
            task_name += f"_{material_code}"

        task = SteelCostDataVerificationTask(
            task_name=task_name,
            start_date=start_date,
            end_date=end_date,
            factory=factory,
            heat_no=heat_no,
            material_code=material_code,
            created_by=user_id
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(f"创建炼钢成本数据核对任务: {task.id}")
        return task
    
    async def start_task(
        self, 
        task_id: int, 
        progress_callback: Optional[Callable] = None
    ):
        """启动炼钢成本数据核对任务"""
        try:
            # 获取任务
            task = await self.db.get(SteelCostDataVerificationTask, task_id)
            if not task:
                raise ValueError(f"任务不存在: {task_id}")
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            await self.db.commit()
            
            if progress_callback:
                await progress_callback(task.id, 10, "任务已启动，正在初始化...")

            # 步骤1：测试数据库连接
            if progress_callback:
                await progress_callback(task.id, 20, "步骤1：正在测试数据库连接...")
            
            await self._test_database_connections()
            
            if progress_callback:
                await progress_callback(task.id, 30, "步骤1：数据库连接测试成功")
            
            # 步骤2：查询OJ数据
            if progress_callback:
                await progress_callback(task.id, 40, "步骤2：正在查询OJ数据...")
            # noinspection PyTypeChecker
            oj_data = await self._get_oj_data(task.start_date, task.factory, task.heat_no, task.material_code)
            
            if progress_callback:
                await progress_callback(task.id, 50, f"步骤2：OJ数据查询完成 - 获取到{len(oj_data)}条记录")
            
            # 步骤3：查询MR数据
            if progress_callback:
                await progress_callback(task.id, 60, "步骤3：正在查询MR数据...")

            # noinspection PyTypeChecker
            mr_data = await self._get_mr_data(task.start_date, task.factory, task.heat_no, task.material_code)

            if progress_callback:
                await progress_callback(task.id, 70, f"步骤3：MR数据查询完成 - 获取到{len(mr_data)}条记录")

            # 步骤4：查询IP数据
            if progress_callback:
                await progress_callback(task.id, 80, "步骤4：正在查询IP数据...")

            # noinspection PyTypeChecker
            ip_data = await self._get_ip_data(task.start_date, task.factory, task.heat_no, task.material_code)
            
            if progress_callback:
                await progress_callback(task.id, 85, f"步骤4：IP数据查询完成 - 获取到{len(ip_data)}条记录")
            
            # 步骤5：执行数据对比
            if progress_callback:
                await progress_callback(task.id, 90, "步骤5：正在执行数据对比分析...")
            
            differences = await self._compare_data(oj_data, mr_data, ip_data)
            
            # 更新任务结果
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.progress = 100
            task.difference_count = len(differences)

            # 构建全局统计数据（JSON字段只存储统计信息）
            verification_result = {
                'summary': {
                    'start_date': task.start_date,
                    'end_date': task.end_date,
                    'factory': task.factory.value,
                    'heat_no': task.heat_no,
                    'material_code': task.material_code,
                    'total_records': len(oj_data) + len(mr_data) + len(ip_data),
                    'difference_count': len(differences)
                },
                'statistics': {
                    'oj_stats': {
                        'count': len(oj_data),
                        'total_weight': sum(float(item.get('TOTAL_WGT', 0)) for item in oj_data),
                        'unique_heats': len(set(item.get('HEATNO', '') for item in oj_data)),
                        'unique_materials': len(set(item.get('MATERIALCODE', '') for item in oj_data))
                    },
                    'mr_stats': {
                        'count': len(mr_data),
                        'total_weight': sum(float(item.get('TOTAL_QTY', 0)) * 1000 for item in mr_data),
                        'unique_heats': len(set(item.get('HEATNO', '') for item in mr_data)),
                        'unique_materials': len(set(item.get('MATNO', '') for item in mr_data))
                    },
                    'ip_stats': {
                        'count': len(ip_data),
                        'total_weight': sum(float(item.get('TOTAL_QTY', 0)) * 1000 for item in ip_data),
                        'unique_heats': len(set(item.get('HEATNO', '') for item in ip_data)),
                        'unique_materials': len(set(item.get('MATRLNO', '') for item in ip_data))
                    }
                },
                'difference_summary': {
                    'by_status': self._count_by_status(differences),
                    'by_heat': self._count_by_heat(differences),
                    'weight_differences': {
                        'total_oj_mr_diff': sum(diff.get('oj_mr_diff', 0) for diff in differences),
                        'total_oj_ip_diff': sum(diff.get('oj_ip_diff', 0) for diff in differences),
                        'total_mr_ip_diff': sum(diff.get('mr_ip_diff', 0) for diff in differences)
                    }
                }
            }

            task.verification_result = verification_result
            task.result_summary = f"核对完成 - OJ:{len(oj_data)}条，MR:{len(mr_data)}条，IP:{len(ip_data)}条，差异:{len(differences)}条"

            # 保存差异明细到专门的表中
            # noinspection PyTypeChecker
            await self._save_verification_details(task.id, differences)
            
            await self.db.commit()
            
            if progress_callback:
                # 构建完整的结果数据
                result_data = {
                    'summary': {
                        'start_date': task.start_date,
                        'end_date': task.end_date,
                        'factory': task.factory.value,
                        'heat_no': task.heat_no,
                        'material_code': task.material_code,
                        'oj_count': len(oj_data),
                        'mr_count': len(mr_data),
                        'ip_count': len(ip_data),
                        'difference_count': len(differences),
                        'total_oj_weight': sum(float(item.get('TOTAL_WGT', 0)) for item in oj_data),
                        'total_mr_weight': sum(float(item.get('TOTAL_QTY', 0)) * 1000 for item in mr_data),
                        'total_ip_weight': sum(float(item.get('TOTAL_QTY', 0)) * 1000 for item in ip_data)
                    },
                    'oj_data': oj_data,
                    'mr_data': mr_data,
                    'ip_data': ip_data,
                    'differences': differences
                }

                logger.info(f"准备发送核对结果，数据大小: {len(str(result_data))}")
                await progress_callback(task.id, 100, f"步骤5：数据核对完成 - 发现{len(differences)}条差异记录", result_data)
            
            logger.info(f"炼钢成本数据核对任务完成: {task_id}")
            
        except Exception as e:
            logger.error(f"炼钢成本数据核对任务失败: {task_id}, 错误: {e}")
            
            # 更新任务状态为失败
            task = await self.db.get(SteelCostDataVerificationTask, task_id)
            if task:
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.now()
                task.error_message = str(e)
                await self.db.commit()
            
            if progress_callback:
                await progress_callback(task.id, 0, f"任务执行失败: {str(e)}")
            
            raise
    
    async def _test_database_connections(self):
        """测试数据库连接"""
        def test_connections():
            # 测试ERP连接
            erp_conn = oracle_manager.get_erp_connection()
            try:
                oracle_manager.execute_query(erp_conn, "SELECT 1 FROM DUAL", {})
                logger.info("ERP数据库连接测试成功")
            finally:
                oracle_manager.close_connection(erp_conn)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, test_connections)
    
    async def _get_oj_data(self, check_date: str, factory: Factory, heat_no: Optional[str] = None, material_code: Optional[str] = None) -> List[Dict]:
        """获取OJ数据"""
        def query_oj():
            logger.info(f"开始查询OJ数据，日期: {check_date}, 厂别: {factory.value}")
            connection = oracle_manager.get_erp_connection()
            try:
                # 根据厂别确定工作车间参数
                if factory == Factory.FACTORY_1:
                    workshop_condition = "WORKSHOP IN ('B', 'C')"
                else:
                    workshop_condition = "WORKSHOP IN ('D', 'E')"

                # 构建SQL
                sql = f"""
                select HEATNO, MATERIALCODE, sum(MATERIALWGT) as TOTAL_WGT
                from DB.TBOJ33
                where HEATNO IN
                      (SELECT HEATNO
                       FROM DB.TBIS101
                       WHERE ACCTDATE = :check_date
                         AND {workshop_condition}
                       UNION
                       SELECT HEATNO
                       FROM DB.TBIB101
                       WHERE ACCTDATE = :check_date
                         AND {workshop_condition})
                  and {workshop_condition}
                """

                params = {
                    'check_date': check_date.replace('-', '')
                }
                
                # 添加可选过滤条件
                if heat_no:
                    sql += " and HEATNO = :heat_no"
                    params['heat_no'] = heat_no
                
                if material_code:
                    sql += " and MATERIALCODE = :material_code"
                    params['material_code'] = material_code
                
                sql += " group by HEATNO, MATERIALCODE"
                
                logger.info(f"执行OJ查询，参数: {params}")
                results = oracle_manager.execute_query(connection, sql, params)
                logger.info(f"OJ数据查询成功，获取到 {len(results)} 条记录")
                return results
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_oj)
    
    async def _get_mr_data(self, check_date: str, factory: Factory, heat_no: Optional[str] = None, material_code: Optional[str] = None) -> List[Dict]:
        """获取MR数据"""
        def query_mr():
            logger.info(f"开始查询MR数据，日期: {check_date}, 厂别: {factory.value}")
            connection = oracle_manager.get_erp_connection()
            try:
                # 根据厂别确定工作车间参数
                if factory == Factory.FACTORY_1:
                    workshop_condition = "WORKSHOP IN ('B', 'C')"
                else:
                    workshop_condition = "WORKSHOP IN ('D', 'E')"

                # 构建SQL
                sql = f"""
                SELECT HEATNO,MATNO,SUM(QTY) as TOTAL_QTY
                FROM DB.TBmfs52
                where APPLYAPP = 'MFJCAPI4OJ'
                  and HEATNO in
                      (SELECT HEATNO
                       FROM DB.TBIS101
                       WHERE ACCTDATE = :check_date
                         and {workshop_condition}
                       UNION
                       SELECT HEATNO
                       FROM DB.TBIB101
                       WHERE ACCTDATE = :check_date
                         and {workshop_condition})
                """

                params = {
                    'check_date': check_date.replace('-', '')
                }
                
                # 添加可选过滤条件
                if heat_no:
                    sql += " and HEATNO = :heat_no"
                    params['heat_no'] = heat_no
                
                if material_code:
                    sql += " and MATNO = :material_code"
                    params['material_code'] = material_code
                
                sql += " group by HEATNO, MATNO"
                
                logger.info(f"执行MR查询，参数: {params}")
                results = oracle_manager.execute_query(connection, sql, params)
                logger.info(f"MR数据查询成功，获取到 {len(results)} 条记录")
                return results
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_mr)
    
    async def _get_ip_data(self, check_date: str, factory: Factory, heat_no: Optional[str] = None, material_code: Optional[str] = None) -> List[Dict]:
        """获取IP数据"""
        def query_ip():
            logger.info(f"开始查询IP数据，日期: {check_date}, 厂别: {factory.value}")
            connection = oracle_manager.get_erp_connection()
            try:
                # 根据厂别确定工作车间参数
                if factory == Factory.FACTORY_1:
                    workshop_condition = "WORKSHOP IN ('B', 'C')"
                else:
                    workshop_condition = "WORKSHOP IN ('D', 'E')"

                # 构建SQL
                sql = f"""
                select HEATNO, MATRLNO, sum(qty) as TOTAL_QTY
                from DB.tbipTxnOJ
                where HEATNO in (SELECT HEATNO
                                 FROM DB.TBIS101
                                 WHERE ACCTDATE = :check_date
                                   and {workshop_condition}
                                 UNION
                                 SELECT HEATNO
                                 FROM DB.TBIB101
                                 WHERE ACCTDATE = :check_date
                                   and {workshop_condition})
                """

                params = {
                    'check_date': check_date.replace('-', '')
                }
                
                # 添加可选过滤条件
                if heat_no:
                    sql += " and HEATNO = :heat_no"
                    params['heat_no'] = heat_no
                
                if material_code:
                    sql += " and MATRLNO = :material_code"
                    params['material_code'] = material_code
                
                sql += " group by HEATNO, MATRLNO"
                
                logger.info(f"执行IP查询，参数: {params}")
                results = oracle_manager.execute_query(connection, sql, params)
                logger.info(f"IP数据查询成功，获取到 {len(results)} 条记录")
                return results
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_ip)
    
    async def _compare_data(self, oj_data: List[Dict], mr_data: List[Dict], ip_data: List[Dict]) -> List[Dict]:
        """对比OJ、MR、IP三部分数据"""
        differences = []
        
        # 将数据转换为字典以便快速查找
        oj_dict = {}
        for item in oj_data:
            key = f"{item['HEATNO']}_{item['MATERIALCODE']}"
            oj_dict[key] = float(item['TOTAL_WGT'] or 0)
        
        mr_dict = {}
        for item in mr_data:
            key = f"{item['HEATNO']}_{item['MATNO']}"
            # MR数据需要*1000转换为公斤
            mr_dict[key] = float(item['TOTAL_QTY'] or 0) * 1000
        
        ip_dict = {}
        for item in ip_data:
            key = f"{item['HEATNO']}_{item['MATRLNO']}"
            # IP数据需要*1000转换为公斤
            ip_dict[key] = float(item['TOTAL_QTY'] or 0) * 1000
        
        # 获取所有唯一的炉号-物料组合
        all_keys = set(oj_dict.keys()) | set(mr_dict.keys()) | set(ip_dict.keys())
        
        for key in all_keys:
            heat_no, material_code = key.split('_', 1)
            if material_code in ['10504022','1','5','10504029','10504021','10504036','10504030','10504021']:
                continue
            
            oj_value = oj_dict.get(key, 0)
            mr_value = mr_dict.get(key, 0)
            ip_value = ip_dict.get(key, 0)
            
            # 检查是否有差异（允许小的浮点数误差）
            tolerance = 0.001
            has_difference = (
                abs(oj_value - mr_value) > tolerance or
                abs(oj_value - ip_value) > tolerance or
                abs(mr_value - ip_value) > tolerance
            )
            oj_mr_diff = 0
            oj_ip_diff = 0
            mr_ip_diff = 0
            status = "一致"
            # 只保存有差异的记录（与数据对比逻辑保持一致）
            if has_difference:
                # 计算差异
                oj_mr_diff = oj_value - mr_value
                oj_ip_diff = oj_value - ip_value
                mr_ip_diff = mr_value - ip_value

                # 确定状态
                if oj_value == 0:
                    status = "OJ无数据"
                elif mr_value == 0:
                    status = "MR无数据"
                elif ip_value == 0:
                    status = "IP无数据"
                else:
                    status = "数据不一致"

                differences.append({
                    'heat_no': heat_no,
                    'material_code': material_code,
                    'oj_value': oj_value,
                    'mr_value': mr_value,
                    'ip_value': ip_value,
                    'oj_mr_diff': oj_mr_diff,
                    'oj_ip_diff': oj_ip_diff,
                    'mr_ip_diff': mr_ip_diff,
                    'status': status,
                    'description': f"OJ: {oj_value:.2f}kg, MR: {mr_value:.2f}kg, IP: {ip_value:.2f}kg"
                })
        
        logger.info(f"数据对比完成，发现 {len(differences)} 条记录")
        return differences

    async def _save_verification_details(self, task_id: int, differences: List[Dict]):
        """保存差异明细到专门的表中"""
        try:
            # 删除旧的明细记录
            from sqlalchemy import delete
            delete_stmt = delete(SteelCostVerificationDetail).where(
                SteelCostVerificationDetail.task_id == task_id
            )
            await self.db.execute(delete_stmt)

            # 批量插入新的明细记录
            if differences:
                detail_records = []
                for diff in differences:
                    detail = SteelCostVerificationDetail(
                        task_id=task_id,
                        heat_no=diff.get('heat_no', ''),
                        material_code=diff.get('material_code', ''),
                        oj_value=diff.get('oj_value'),
                        mr_value=diff.get('mr_value'),
                        ip_value=diff.get('ip_value'),
                        status=diff.get('status', ''),
                        description=diff.get('description', '')
                    )
                    detail_records.append(detail)

                self.db.add_all(detail_records)
                await self.db.commit()
                logger.info(f"保存了 {len(detail_records)} 条差异明细记录")

        except Exception as e:
            logger.error(f"保存差异明细失败: {e}")
            await self.db.rollback()
            raise

    def _count_by_status(self, differences: List[Dict]) -> Dict[str, int]:
        """按状态统计差异数量"""
        status_count = {}
        for diff in differences:
            status = diff.get('status', '未知')
            status_count[status] = status_count.get(status, 0) + 1
        return status_count

    def _count_by_heat(self, differences: List[Dict]) -> Dict[str, int]:
        """按炉号统计差异数量"""
        heat_count = {}
        for diff in differences:
            heat_no = diff.get('heat_no', '未知')
            heat_count[heat_no] = heat_count.get(heat_no, 0) + 1
        return heat_count
