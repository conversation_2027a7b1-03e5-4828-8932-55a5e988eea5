import datetime

import requests

from app.utils import logger


class DailyCounting:

    async def start_invoke_daily_counting(self):
        """开始执行每日盘点"""
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)

        url = "http://smes1.jggroup.cn:30142/SMES_F/ASF2012C/btnAuto_Click"
        headers = {
            "Host": "smes1.jggroup.cn:30142",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:141.0) Gecko/20100101 Firefox/141.0",
            "Accept": "text/plain, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Accept-Encoding": "gzip, deflate",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "X-FineUI-Ajax": "true",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": "http://smes1.jggroup.cn:30142",
            "Connection": "keep-alive",
            "Referer": "http://smes1.jggroup.cn:30142/SMES_F/ASF2012C/ASF2012C?WorkShop=L2",
            "Cookie": "token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1lIjoiNTczNzEiLCJqdGkiOiIzMjQ4IiwiaHR0cDovL3NjaGVtYXMubWljcm9zb2Z0LmNvbS93cy8yMDA4LzA2L2lkZW50aXR5L2NsYWltcy9leHBpcmF0aW9uIjoiMjAyNS83LzI5IOS4iuWNiDM6NTg6MTEiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL3JvbGUiOiLkv6Hmga_oh6rliqjljJblpIQiLCJuYmYiOjE3NTM2ODk0OTIsImV4cCI6MTc1MzczMjY5MiwiaXNzIjoiQ2VyaU9TLkNvcmUiLCJhdWQiOiJzdGV2ZSJ9.09eQz_ym2kXvGHcrOXcj-Kq6ipdgl2wn8Ic5zNd0sgs; shiftClass=%E4%B9%99%E7%8F%AD2; shift=%E7%99%BD%E7%8F%AD; shiftClassId=26; shiftId=2; shiftClassCode=Bban; shiftCode=1; .AspNetCore.Antiforgery.NPLBjrL3iZE=CfDJ8GTMIpbTFrZNvvtPxyoeraq652SbShUndRhyIg6gFtHSOyT7zRCzIBMwNcnlL5nOluKsmT1H3B8WatKHNPKtmX3bGVzYTlunM05dvNu2qK6meZIoKPh_Lj6qhBT3G55e5BI6iGqnsuAttSR2KwJUKbA"
        }

        data = {
            "txb_WORK_SHOP": "L2",
            "txb_INVENTORY_TYPE": "0",
            "dt_DT_TIME":  yesterday.strftime("%Y-%m-%d"),
            "drplWORK_SHOP1": "C1",
            "drplINVENTORY_TYPE1": "1",
            "dpDT_TIME1": "",
            "drplMAT_TYPE1": "W",
            "txtEND_INVENTORY": "",
            "Grid1_pagingToolbar_pageNumberBox": "1",
            "ddlPageSize": "100",
            "txb_WORK_SHOP_text": "二炼钢",
            "txb_INVENTORY_TYPE_text": "日盘",
            "Grid1_fields[]": [
                "WORK_SHOP", "INVENTORY_TYPE", "DT_TIME", "MAT_TYPE", "MAT_CODE", "MAT_NAME",
                "START_INVENTORY", "COLUMN_1", "COLUMN_2", "COLUMN_3", "COLUMN_4", "REDUCE_SLAG_WGT",
                "YARD_OUT_WGT", "COLUMN_5", "AUTO_END_INVENTORY", "END_INVENTORY", "INVENTORY_FLAG",
                "YARD_IN_WGT_SUM", "YARD_OUT_WGT_SUM", "YARD_ZT_WGT_SUM", "YARD_MAT_WGT_SUM",
                "REDUCE_SLAG_WGT_SUM", "MAT_SLAG_WGT_SUM", "INVENTORY_FLAG_SUM", "MAT_LOC"
            ],
            "Grid1_pageIndex": "0",
            "Grid1_pageSize": "100",
            "Grid1_sortField": "",
            "Grid1_sortDirection": "ASC",
            "Grid1_modifiedData": "[]",
            "drplWORK_SHOP1_text": "酸轧产线",
            "drplINVENTORY_TYPE1_text": "月盘",
            "drplMAT_TYPE1_text": "无",
            "ddlPageSize_text": "100",
            "dateTime": yesterday.strftime("%Y/%m/%d"),
            "__RequestVerificationToken": "CfDJ8GTMIpbTFrZNvvtPxyoerap77exXYSgY405FtLN8ICaab4RcLSyRo5BXpzFv6B8Vr5354-gAM1keAIFhgcw0Ew1J3JdtQQXPfh-ijdF9HRQYs51-zC_YgDkXXlNpYaWP7Wb6QwuU1CIe31rxc4Mu_Cw"
        }
        response = requests.post(url, headers=headers, data=data)
        logger.info("日盘执行结果为:\n", response.text)