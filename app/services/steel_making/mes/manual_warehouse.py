from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc

from app.models.steel_making.mes.manual_warehouse import ManualWarehouseRecord
from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger


class ManualWarehouseService:
    """人工入库服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def search_materials(self, query: str) -> List[Dict[str, str]]:
        """搜索物料信息"""
        try:
            def query_materials():
                # 构建SQL查询
                sql = """
                select distinct MATE_CODE, MATE_NAME
                from JGLGMES.SMES_B_MATE_CONFIG
                where (MATE_CODE like :query OR MATE_NAME like :query)
                order by MATE_CODE
                """

                connection = oracle_manager.get_mes_connection()
                try:
                    params = {"query": f"%{query}%"}
                    results = oracle_manager.execute_query(connection, sql, params)
                    return results
                finally:
                    oracle_manager.close_connection(connection)

            # 在线程池中执行同步操作
            import asyncio
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(None, query_materials)

            # 转换结果
            materials = []
            for row in results:
                materials.append({
                    "MATE_CODE": row.get("MATE_CODE", ""),
                    "MATE_NAME": row.get("MATE_NAME", "")
                })

            logger.info(f"搜索物料完成，查询词: {query}, 结果数量: {len(materials)}")
            return materials

        except Exception as e:
            logger.error(f"搜索物料失败: {e}")
            raise
    
    async def get_all_materials(self) -> List[Dict[str, str]]:
        """获取所有物料信息"""
        try:
            def query_all_materials():
                # 构建SQL查询
                sql = """
                select distinct MATE_CODE, MATE_NAME
                from JGLGMES.SMES_B_MATE_CONFIG
                order by MATE_CODE
                """

                connection = oracle_manager.get_mes_connection()
                try:
                    results = oracle_manager.execute_query(connection, sql)
                    return results
                finally:
                    oracle_manager.close_connection(connection)

            # 在线程池中执行同步操作
            import asyncio
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(None, query_all_materials)

            # 转换结果
            materials = []
            for row in results:
                materials.append({
                    "MATE_CODE": row.get("MATE_CODE", ""),
                    "MATE_NAME": row.get("MATE_NAME", "")
                })

            logger.info(f"获取所有物料完成，结果数量: {len(materials)}")
            return materials

        except Exception as e:
            logger.error(f"获取所有物料失败: {e}")
            raise
    
    async def get_reference_data(self, material_code: str, factory: str) -> Optional[Dict[str, Any]]:
        """获取参考数据"""
        try:
            def query_reference_data():
                # 构建SQL查询 - 获取最新的一条记录作为模板
                sql = """
                select * from (
                    select t.*, row_number() over (order by SUTTLE_TIME desc) as rn
                    from JGLGMES.SMES_F_YARD_IN t
                    where WORK_SHOP = :factory and MAT_CODE = :material_code
                    order by SUTTLE_TIME desc
                ) where rn = 1
                """

                connection = oracle_manager.get_mes_connection()
                try:
                    params = {"factory": factory, "material_code": material_code}
                    results = oracle_manager.execute_query(connection, sql, params)
                    return results
                finally:
                    oracle_manager.close_connection(connection)

            # 在线程池中执行同步操作
            import asyncio
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(None, query_reference_data)

            if not results:
                return None

            # 转换结果（返回的是字典格式）
            reference_data = results[0]

            logger.info(f"获取参考数据完成，物料: {material_code}, 工厂: {factory}")
            return reference_data

        except Exception as e:
            logger.error(f"获取参考数据失败: {e}")
            # 不抛出异常，返回None表示没有参考数据
            return None

    async def get_fallback_reference_data(self, factory: str) -> Optional[Dict[str, Any]]:
        """获取备用参考数据（该工厂的任意一条记录）"""
        try:
            def query_fallback_data():
                # 构建SQL查询 - 获取该工厂的任意一条记录作为模板
                sql = """
                select * from (
                    select t.*, row_number() over (order by SUTTLE_TIME desc) as rn
                    from JGLGMES.SMES_F_YARD_IN t
                    where WORK_SHOP = :factory
                    order by SUTTLE_TIME desc
                ) where rn = 1
                """

                connection = oracle_manager.get_mes_connection()
                try:
                    params = {"factory": factory}
                    results = oracle_manager.execute_query(connection, sql, params)
                    return results
                finally:
                    oracle_manager.close_connection(connection)

            # 在线程池中执行同步操作
            import asyncio
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(None, query_fallback_data)

            if not results:
                return None

            # 转换结果
            reference_data = results[0]

            logger.info(f"获取备用参考数据完成，工厂: {factory}")
            return reference_data

        except Exception as e:
            logger.error(f"获取备用参考数据失败: {e}")
            return None
    
    async def generate_les_no(self, warehouse_date: str) -> str:
        """生成LES编号"""
        try:
            # 格式化日期
            date_str = warehouse_date.replace("-", "")
            
            # 查询当天已有的最大序号
            stmt = select(func.max(ManualWarehouseRecord.les_no)).where(
                ManualWarehouseRecord.les_no.like(f"{date_str}%")
            )
            result = await self.db.execute(stmt)
            max_les_no = result.scalar()
            
            if max_les_no:
                # 提取序号部分并加1
                sequence = int(max_les_no[-4:]) + 1
            else:
                # 第一条记录
                sequence = 1
            
            # 生成新的LES编号
            les_no = f"{date_str}{sequence:04d}"
            
            logger.info(f"生成LES编号: {les_no}")
            return les_no

        except Exception as e:
            logger.error(f"生成LES编号失败: {e}")
            raise

    async def generate_insert_sql_from_template(
        self,
        reference_data: Dict[str, Any],
        new_les_no: str,
        new_weight: float,
        new_datetime: datetime,
        material_code: str = None
    ) -> str:
        """基于参考数据生成完整的INSERT语句"""
        try:
            # 获取参考数据的所有列名和值
            columns = []
            values = []

            datetime_str = new_datetime.strftime("%Y-%m-%d %H:%M:%S")

            for column, value in reference_data.items():
                if column.upper() == 'RN':  # 跳过ROW_NUMBER()生成的列
                    continue

                columns.append(column)

                # 根据列名设置新值
                if column.upper() == 'LES_NO':
                    values.append(f"'{new_les_no}'")
                elif column.upper() == 'MAT_CODE' and material_code:
                    # 使用用户选择的物料编码
                    values.append(f"'{material_code}'")
                elif column.upper() in ['SUTTLE', 'DEDUCTION_SUTTLE']:
                    values.append(str(new_weight))
                elif column.upper() in ['GROSS_TIME', 'TARE_TIME', 'SUTTLE_TIME', 'REC_TIME']:
                    values.append(f"TO_DATE('{datetime_str}', 'YYYY-MM-DD HH24:MI:SS')")
                else:
                    # 保持原始值
                    if value is None:
                        values.append('NULL')
                    elif isinstance(value, str):
                        # 转义单引号
                        escaped_value = value.replace("'", "''")
                        values.append(f"'{escaped_value}'")
                    elif isinstance(value, (int, float)):
                        values.append(str(value))
                    elif hasattr(value, 'strftime'):  # datetime对象
                        values.append(f"TO_DATE('{value.strftime('%Y-%m-%d %H:%M:%S')}', 'YYYY-MM-DD HH24:MI:SS')")
                    else:
                        values.append(f"'{str(value)}'")

            # 生成INSERT语句
            columns_str = ', '.join(columns)
            values_str = ', '.join(values)

            insert_sql = f"""
            INSERT INTO JGLGMES.SMES_F_YARD_IN ({columns_str})
            VALUES ({values_str})
            """

            logger.info(f"基于模板生成INSERT语句，LES编号: {new_les_no}")
            return insert_sql

        except Exception as e:
            logger.error(f"生成INSERT语句失败: {e}")
            raise
    
    async def create_warehouse_record(
        self,
        material_code: str,
        material_name: str,
        weight: float,
        factory: str,
        warehouse_date: str,
        user_id: int
    ) -> Dict[str, Any]:
        """创建入库记录"""
        try:
            # 生成LES编号
            les_no = await self.generate_les_no(warehouse_date)

            # 构造入库时间（选择日期的11点）
            warehouse_datetime = datetime.strptime(f"{warehouse_date} 11:00:00", "%Y-%m-%d %H:%M:%S")

            # 获取参考数据作为模板
            reference_data = await self.get_reference_data(material_code, factory)

            if not reference_data:
                raise Exception(f"未找到工厂 {factory} 的任何参考数据，无法生成入库记录")

            # 基于参考数据生成完整的INSERT语句
            insert_sql = await self.generate_insert_sql_from_template(
                reference_data, les_no, weight, warehouse_datetime, material_code
            )
            
            # 创建记录
            record = ManualWarehouseRecord(
                les_no=les_no,
                material_code=material_code,
                material_name=material_name,
                weight=weight,
                factory=factory,
                warehouse_date=warehouse_date,
                warehouse_datetime=warehouse_datetime,
                insert_sql=insert_sql,
                status="pending",
                user_id=user_id
            )
            
            self.db.add(record)
            await self.db.commit()
            await self.db.refresh(record)
            
            # 执行MES数据库插入
            try:
                def execute_insert():
                    connection = oracle_manager.get_mes_connection()
                    try:
                        cursor = connection.cursor()
                        cursor.execute(insert_sql)
                        connection.commit()
                        cursor.close()
                        return cursor.rowcount
                    finally:
                        oracle_manager.close_connection(connection)

                # 在线程池中执行同步操作
                import asyncio
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, execute_insert)

                # 更新状态为成功
                record.status = "success"
                await self.db.commit()

                logger.info(f"人工入库成功，LES编号: {les_no}")

            except Exception as mes_error:
                # 更新状态为失败
                record.status = "failed"
                record.error_message = str(mes_error)
                await self.db.commit()

                logger.error(f"MES数据库插入失败: {mes_error}")
                raise Exception(f"MES数据库插入失败: {str(mes_error)}")
            
            return {
                "lesNo": les_no,
                "insertSql": insert_sql
            }
            
        except Exception as e:
            logger.error(f"创建入库记录失败: {e}")
            await self.db.rollback()
            raise
    
    async def get_warehouse_history(
        self,
        page: int = 1,
        page_size: int = 20,
        material_code: Optional[str] = None,
        factory: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        les_no: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取入库历史记录"""
        try:
            # 构建查询条件
            conditions = []
            
            if material_code:
                conditions.append(ManualWarehouseRecord.material_code.like(f"%{material_code}%"))
            
            if factory:
                conditions.append(ManualWarehouseRecord.factory == factory)
            
            if start_date:
                conditions.append(ManualWarehouseRecord.warehouse_date >= start_date)
            
            if end_date:
                conditions.append(ManualWarehouseRecord.warehouse_date <= end_date)
            
            if les_no:
                conditions.append(ManualWarehouseRecord.les_no.like(f"%{les_no}%"))
            
            # 查询总数
            count_stmt = select(func.count(ManualWarehouseRecord.id))
            if conditions:
                count_stmt = count_stmt.where(and_(*conditions))
            
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # 查询数据
            stmt = select(ManualWarehouseRecord).order_by(desc(ManualWarehouseRecord.created_at))
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            stmt = stmt.offset((page - 1) * page_size).limit(page_size)
            
            result = await self.db.execute(stmt)
            records = result.scalars().all()
            
            # 转换为响应格式
            items = []
            for record in records:
                items.append({
                    "id": record.id,
                    "lesNo": record.les_no,
                    "materialCode": record.material_code,
                    "materialName": record.material_name,
                    "weight": record.weight,
                    "factory": record.factory,
                    "warehouseDate": record.warehouse_date,
                    "createdAt": record.created_at.strftime('%Y-%m-%d %H:%M:%S') if record.created_at else '',
                    "createdBy": f"用户{record.user_id}" if record.user_id else "系统",
                    "status": record.status
                })
            
            total_pages = (total + page_size - 1) // page_size
            
            return {
                "items": items,
                "total": total,
                "page": page,
                "pageSize": page_size,
                "totalPages": total_pages
            }
            
        except Exception as e:
            logger.error(f"获取入库历史失败: {e}")
            raise
    
    async def delete_warehouse_record(self, record_id: int) -> bool:
        """删除入库记录"""
        try:
            stmt = select(ManualWarehouseRecord).where(ManualWarehouseRecord.id == record_id)
            result = await self.db.execute(stmt)
            record = result.scalar_one_or_none()
            
            if not record:
                raise Exception("记录不存在")
            
            if record.status == "success":
                raise Exception("已成功的记录不能删除")
            
            await self.db.delete(record)
            await self.db.commit()
            
            logger.info(f"删除入库记录成功，ID: {record_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除入库记录失败: {e}")
            await self.db.rollback()
            raise
