"""
MES维护服务
"""

import asyncio
import aiohttp
from typing import Optional, Callable, Dict, Any
from urllib.parse import quote
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.steel_making.mes.mes_maintenance import MESMaintenanceTask, MESMaintenanceTaskType, MESMaintenanceTaskStatus
from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger


class MESMaintenanceService:
    """MES维护服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_update_prodres_summary_task(
        self,
        date: str,
        user_id: int
    ) -> int:
        """创建更新钢坯产量调拨汇总报表任务"""
        task_name = f"更新钢坯产量调拨汇总报表_{date}"

        task = MESMaintenanceTask(
            task_name=task_name,
            task_type=MESMaintenanceTaskType.PRODRES_SUMMARY,
            target_date=date,
            status=MESMaintenanceTaskStatus.PENDING,
            progress=0,
            created_by=user_id
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(f"创建更新钢坯产量调拨汇总报表任务: {task.id}")
        return task.id
    
    async def start_update_prodres_summary_task(
        self, 
        task_id: int, 
        date: str, 
        progress_callback: Optional[Callable] = None
    ):
        """启动更新钢坯产量调拨汇总报表任务"""
        # 获取任务
        stmt = select(MESMaintenanceTask).where(MESMaintenanceTask.id == task_id)
        result = await self.db.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            raise Exception(f"任务不存在: {task_id}")
        
        try:
            # 更新任务状态
            task.status = MESMaintenanceTaskStatus.RUNNING
            task.progress = 0
            task.started_at = datetime.now()
            await self.db.commit()
            
            # 执行更新逻辑
            await self._execute_update_prodres_summary(task, date, progress_callback)
            
            # 更新任务状态为完成
            task.status = MESMaintenanceTaskStatus.COMPLETED
            task.progress = 100
            task.completed_at = datetime.now()
            await self.db.commit()
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = MESMaintenanceTaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            await self.db.commit()
            logger.error(f"更新钢坯产量调拨汇总报表任务失败: {e}")
            raise
    
    async def _execute_update_prodres_summary(
        self,
        task: MESMaintenanceTask,
        date: str,
        progress_callback: Optional[Callable] = None
    ):
        """执行更新钢坯产量调拨汇总报表逻辑"""
        try:
            # 步骤1：连接MES数据库并删除数据
            if progress_callback:
                await progress_callback(task.id, 10, "步骤1：正在连接MES数据库...")

            await self._delete_prodres_data(date)
            
            if progress_callback:
                await progress_callback(task.id, 50, "步骤1：MES数据库数据删除完成")

            # 步骤2：调用外部API更新数据
            if progress_callback:
                await progress_callback(task.id, 60, "步骤2：正在调用外部API更新数据...")

            await self._call_update_api(date)

            if progress_callback:
                await progress_callback(task.id, 90, "步骤2：外部API调用完成")

            # 步骤3：完成
            if progress_callback:
                await progress_callback(task.id, 100, "更新钢坯产量调拨汇总报表完成")

        except Exception as e:
            logger.error(f"执行更新钢坯产量调拨汇总报表失败: {e}")
            if progress_callback:
                await progress_callback(task.id, -1, f"执行失败: {str(e)}")
            raise
    
    async def _delete_prodres_data(self, date: str):
        """删除MES数据库中的SMES_G_PRODRES数据"""
        def delete_data():
            logger.info(f"开始删除MES数据库SMES_G_PRODRES数据，日期: {date}")
            connection = oracle_manager.get_mes_connection()
            try:
                # 使用传入的日期格式（YYYY-MM-DD）
                formatted_date = date
                
                sql = """
                DELETE FROM JGPSS.SMES_G_PRODRES t
                WHERE TO_CHAR(T.PRODDATE,'YYYY-MM-DD') = :date_param
                """
                
                cursor = connection.cursor()
                cursor.execute(sql, {'date_param': formatted_date})
                deleted_rows = cursor.rowcount
                connection.commit()
                cursor.close()
                
                logger.info(f"MES数据库删除完成，删除了 {deleted_rows} 条记录")
                return deleted_rows
                
            except Exception as e:
                connection.rollback()
                logger.error(f"删除MES数据库数据失败: {e}")
                raise
            finally:
                oracle_manager.close_connection(connection)
        
        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, delete_data)
    
    async def _call_update_api(self, date: str):
        """调用外部API更新数据"""
        try:
            # 格式化日期为API需要的格式
            formatted_datetime = f"{date} 00:00:00"
            encoded_datetime = quote(formatted_datetime)
            
            # 构建API URL
            api_url = f"http://172.17.11.101:5005/api/SMES_G_PRODRES/GetSumSMES_G_PRODRES_NEW_Manual?Datetime={encoded_datetime}"
            
            logger.info(f"调用外部API: {api_url}")
            
            # 使用aiohttp调用API
            async with aiohttp.ClientSession() as session:
                async with session.get(api_url, timeout=aiohttp.ClientTimeout(total=300)) as response:
                    if response.status == 200:
                        result = await response.text()
                        logger.info(f"外部API调用成功，响应: {result[:200]}...")  # 只记录前200个字符
                        return result
                    else:
                        error_msg = f"外部API调用失败，状态码: {response.status}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                        
        except Exception as e:
            logger.error(f"调用外部API失败: {e}")
            raise

    async def create_bof_scrap_steel_clear_task(
        self,
        heat_id: str,
        user_id: int
    ) -> int:
        """创建转炉无法选择废钢号任务"""
        task_name = f"转炉无法选择废钢号_{heat_id}"

        task = MESMaintenanceTask(
            task_name=task_name,
            task_type=MESMaintenanceTaskType.BOF_SCRAP_STEEL_CLEAR,
            target_date=heat_id,  # 使用heat_id作为目标标识
            status=MESMaintenanceTaskStatus.PENDING,
            progress=0,
            created_by=user_id
        )

        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)

        logger.info(f"创建转炉无法选择废钢号任务: {task.id}")
        return task.id

    async def start_bof_scrap_steel_clear_task(
        self,
        task_id: int,
        heat_id: str,
        progress_callback: Optional[Callable] = None
    ):
        """启动转炉无法选择废钢号任务"""
        # 获取任务
        stmt = select(MESMaintenanceTask).where(MESMaintenanceTask.id == task_id)
        result = await self.db.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            raise Exception(f"任务不存在: {task_id}")

        try:
            # 更新任务状态
            task.status = MESMaintenanceTaskStatus.RUNNING
            task.progress = 0
            task.started_at = datetime.now()
            await self.db.commit()

            # 执行清理逻辑
            original_value = await self._execute_bof_scrap_steel_clear(task, heat_id, progress_callback)

            # 更新任务状态为完成
            task.status = MESMaintenanceTaskStatus.COMPLETED
            task.progress = 100
            task.completed_at = datetime.now()
            task.task_result = {"original_bo_csmtwo": original_value, "heat_id": heat_id}
            await self.db.commit()

            return original_value

        except Exception as e:
            # 更新任务状态为失败
            task.status = MESMaintenanceTaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            await self.db.commit()
            logger.error(f"转炉无法选择废钢号任务失败: {e}")
            raise

    async def _execute_bof_scrap_steel_clear(
        self,
        task: MESMaintenanceTask,
        heat_id: str,
        progress_callback: Optional[Callable] = None
    ) -> str:
        """执行转炉无法选择废钢号清理逻辑"""
        try:
            # 步骤1：查询当前BO_CSMTWO值
            if progress_callback:
                await progress_callback(task.id, 20, "步骤1：正在查询当前BO_CSMTWO值...")

            original_value = await self._query_bo_csmtwo(heat_id)

            if progress_callback:
                await progress_callback(task.id, 50, f"步骤1：查询完成，原值: {original_value}")

            # 步骤2：将BO_CSMTWO设置为null
            if progress_callback:
                await progress_callback(task.id, 60, "步骤2：正在清空BO_CSMTWO值...")

            await self._clear_bo_csmtwo(heat_id)

            if progress_callback:
                await progress_callback(task.id, 90, "步骤2：BO_CSMTWO值清空完成")

            # 步骤3：完成
            if progress_callback:
                await progress_callback(task.id, 100, "转炉无法选择废钢号处理完成")

            return original_value

        except Exception as e:
            logger.error(f"执行转炉无法选择废钢号清理失败: {e}")
            if progress_callback:
                await progress_callback(task.id, -1, f"执行失败: {str(e)}")
            raise

    async def _query_bo_csmtwo(self, heat_id: str) -> str:
        """查询MES数据库中的BO_CSMTWO值"""
        def query_data():
            logger.info(f"开始查询MES数据库BO_CSMTWO值，HEAT_ID: {heat_id}")
            connection = oracle_manager.get_mes_connection()
            try:
                sql = """
                SELECT BO_CSMTWO FROM JGLGMES.SMES_B_BOF_RES
                WHERE HEAT_ID = :heat_id
                """

                cursor = connection.cursor()
                cursor.execute(sql, {'heat_id': heat_id})
                result = cursor.fetchone()
                cursor.close()

                if result:
                    original_value = result[0] if result[0] is not None else ""
                    logger.info(f"MES数据库查询完成，BO_CSMTWO原值: {original_value}")
                    return original_value
                else:
                    raise Exception(f"未找到HEAT_ID为 {heat_id} 的记录")

            except Exception as e:
                logger.error(f"查询MES数据库BO_CSMTWO失败: {e}")
                raise
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_data)

    async def _clear_bo_csmtwo(self, heat_id: str):
        """清空MES数据库中的BO_CSMTWO值"""
        def clear_data():
            logger.info(f"开始清空MES数据库BO_CSMTWO值，HEAT_ID: {heat_id}")
            connection = oracle_manager.get_mes_connection()
            try:
                sql = """
                UPDATE JGLGMES.SMES_B_BOF_RES
                SET BO_CSMTWO = NULL
                WHERE HEAT_ID = :heat_id
                """

                cursor = connection.cursor()
                cursor.execute(sql, {'heat_id': heat_id})
                updated_rows = cursor.rowcount
                connection.commit()
                cursor.close()

                logger.info(f"MES数据库BO_CSMTWO清空完成，更新了 {updated_rows} 条记录")
                return updated_rows

            except Exception as e:
                connection.rollback()
                logger.error(f"清空MES数据库BO_CSMTWO失败: {e}")
                raise
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, clear_data)

    async def get_task_status(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        stmt = select(MESMaintenanceTask).where(MESMaintenanceTask.id == task_id)
        result = await self.db.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            return None

        return {
            "id": task.id,
            "task_name": task.task_name,
            "task_type": task.task_type.value if hasattr(task.task_type, 'value') else str(task.task_type),
            "target_date": task.target_date,
            "status": task.status.value if hasattr(task.status, 'value') else str(task.status),
            "progress": task.progress,
            "error_message": task.error_message,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None
        }
