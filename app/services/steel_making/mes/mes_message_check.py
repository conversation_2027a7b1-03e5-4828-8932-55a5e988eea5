import asyncio
import json
from typing import List, Dict, Optional, Callable, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime

from app.models.steel_making.mes.message_check import MessageCheckTask
from app.models.common import TaskStatus, MessageType
from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger


def serialize_for_json(obj: Any) -> Any:
    """递归处理对象，确保所有datetime对象都被转换为字符串"""
    if hasattr(obj, 'strftime'):  # datetime对象
        return obj.strftime('%Y-%m-%d %H:%M:%S')
    elif isinstance(obj, dict):
        return {key: serialize_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_json(item) for item in obj]
    else:
        return obj


class MESMessageCheckService:
    """MES电文检查服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_mes_message_check_task(
        self,
        check_date: str,
        message_type: MessageType,
        user_id: int
    ) -> int:
        """创建MES电文检查任务"""
        task_name = f"MES电文检查_{message_type.value}_{check_date}"

        task = MessageCheckTask(
            task_name=task_name,
            check_date=check_date,
            message_type=message_type,
            status=TaskStatus.PENDING,
            progress=0,
            total_records=0,
            error_count=0,
            created_by=user_id
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(f"创建MES电文检查任务: {task.id}")
        return task.id
    
    async def start_mes_message_check_task(self, task_id: int, message_type: MessageType, progress_callback: Optional[Callable] = None):
        """启动MES电文检查任务"""
        # 更新任务状态为运行中
        await self._update_task_status(
            task_id, 
            TaskStatus.RUNNING, 
            progress=0,
            started_at=datetime.now()
        )
        
        try:
            # 获取任务信息
            task = await self._get_task(task_id)
            if not task:
                raise Exception(f"任务 {task_id} 不存在")

            # 执行MES电文检查
            await self._execute_mes_message_check(task, progress_callback)
            
            # 更新任务状态为完成
            await self._update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                progress=100,
                completed_at=datetime.now()
            )
            
            logger.info(f"MES电文检查任务 {task_id} 执行完成")
            
        except Exception as e:
            logger.error(f"MES电文检查任务执行失败: {e}")
            await self._update_task_status(
                task_id,
                TaskStatus.FAILED,
                progress=0,
                error_message=str(e),
                completed_at=datetime.now()
            )
            raise
    
    async def _execute_mes_message_check(self, task: MessageCheckTask, progress_callback: Optional[Callable] = None):
        """执行MES电文检查逻辑"""
        try:
            # 步骤1：连接MES数据库
            if progress_callback:
                await progress_callback(task.id, 10, "步骤1：正在连接MES数据库...")

            # 测试数据库连接
            await self._test_mes_connection()
            
            if progress_callback:
                await progress_callback(task.id, 20, "步骤1：MES数据库连接成功")

            # 步骤2：查询错误电文
            if progress_callback:
                await progress_callback(task.id, 30, f"步骤2：正在查询MES {task.message_type.value}错误电文...")

            error_messages = await self._get_mes_error_messages(task.check_date, task.message_type)

            if progress_callback:
                await progress_callback(task.id, 70, f"步骤2：查询完成 - 发现{len(error_messages)}条错误电文")

            # 步骤3：生成检查结果
            if progress_callback:
                await progress_callback(task.id, 90, "步骤3：正在生成检查结果...")

            # 生成检查结果JSON（只存储统计信息）
            check_result = {
                "summary": {
                    "check_date": task.check_date,
                    "message_type": task.message_type.value,
                    "error_count": len(error_messages),
                    "check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "system": "MES",
                    "total_records": len(error_messages)
                },
                "statistics": {
                    "by_interface": self._count_by_interface(error_messages),
                    "by_status": self._count_by_status(error_messages),
                    "by_hour": self._count_by_hour(error_messages),
                    "error_types": self._analyze_error_types(error_messages)
                }
            }

            # 确保所有数据都可以序列化为JSON
            serialized_result = serialize_for_json(check_result)

            # 添加调试日志
            logger.info(f"序列化后的结果类型: {type(serialized_result)}")
            logger.info(f"序列化后的结果前100字符: {str(serialized_result)[:100]}")

            # 更新任务结果和保存明细数据
            await self._update_task_with_result(task.id, len(error_messages), serialized_result)
            await self._save_message_check_details(task.id, error_messages)

            if progress_callback:
                await progress_callback(task.id, 100, f"步骤3：MES检查完成 - 共发现{len(error_messages)}条错误电文", check_result)

        except Exception as e:
            logger.error(f"执行MES电文检查失败: {e}")
            raise
    
    async def _test_mes_connection(self):
        """测试MES数据库连接"""
        def test_connection():
            # 测试MES连接
            mes_conn = oracle_manager.get_mes_connection()
            try:
                oracle_manager.execute_query(mes_conn, "SELECT 1 FROM DUAL", {})
                logger.info("MES数据库连接测试成功")
            finally:
                oracle_manager.close_connection(mes_conn)
        
        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, test_connection)
    
    async def _get_mes_error_messages(self, check_date: str, message_type: MessageType) -> List[Dict]:
        """获取MES错误电文数据"""
        def query_error_messages():
            logger.info(f"开始查询MES {message_type.value}错误电文，日期: {check_date}")
            connection = oracle_manager.get_mes_msg_connection()
            try:
                if message_type == MessageType.RECEIVE:
                    # MES的错误接收电文
                    sql = """
                    SELECT WORKTIME, INTERFACENAME, INTERFACENO, ISTATUS, ERRORINFO, SYSTEM
                    FROM INTERFACE_ERP_2.R_INTERFACE_INFO
                    WHERE WORKTIME BETWEEN TO_DATE(:start_time, 'YYYY-MM-DD HH24:MI:SS')
                                       AND TO_DATE(:end_time, 'YYYY-MM-DD HH24:MI:SS')
                      AND ERRORINFO IS NOT NULL
                    ORDER BY WORKTIME
                    """
                else:  # MessageType.SEND
                    # MES的错误发送电文
                    sql = """
                    SELECT WORKTIME, INTERFACENAME, INTERFACENO, ISTATUS, ERRORINFO
                    FROM INTERFACE_ERP_2.S_INTERFACE_INFO
                    WHERE WORKTIME BETWEEN TO_DATE(:start_time, 'YYYY-MM-DD HH24:MI:SS')
                                       AND TO_DATE(:end_time, 'YYYY-MM-DD HH24:MI:SS')
                      AND ERRORINFO IS NOT NULL
                    ORDER BY WORKTIME
                    """

                params = {
                    'start_time': f"{check_date} 00:00:00",
                    'end_time': f"{check_date} 23:59:59"
                }

                logger.info(f"执行MES {message_type.value}电文查询，参数: {params}")
                raw_results = oracle_manager.execute_query(connection, sql, params)
                logger.info(f"MES {message_type.value}电文查询成功，获取到 {len(raw_results)} 条记录")

                # 处理datetime对象，转换为字符串
                results = []
                for row in raw_results:
                    processed_row = {}
                    for key, value in row.items():
                        if hasattr(value, 'strftime'):  # 检查是否是datetime对象
                            processed_row[key] = value.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            processed_row[key] = value
                    results.append(processed_row)

                return results
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_error_messages)
    
    async def _update_task_with_result(self, task_id: int, error_count: int, check_result: Dict):
        """更新任务统计信息和结果JSON"""
        try:
            # 尝试序列化为JSON字符串来验证
            json_str = json.dumps(check_result, ensure_ascii=False)
            logger.info(f"JSON序列化成功，长度: {len(json_str)}")

            stmt = update(MessageCheckTask).where(
                MessageCheckTask.id == task_id
            ).values(
                total_records=error_count,
                error_count=error_count,
                check_result=check_result  # 存储JSON结果
            )

            await self.db.execute(stmt)
            await self.db.commit()
            logger.info(f"更新MES任务统计信息，错误数: {error_count}")
        except Exception as e:
            logger.error(f"更新任务结果失败: {e}")
            logger.error(f"check_result类型: {type(check_result)}")
            logger.error(f"check_result内容: {check_result}")
            raise
    
    async def _update_task_status(
        self, 
        task_id: int, 
        status: TaskStatus, 
        progress: Optional[int] = None,
        error_message: Optional[str] = None,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None
    ):
        """更新任务状态"""
        update_data = {'status': status}
        
        if progress is not None:
            update_data['progress'] = progress
        if error_message is not None:
            update_data['error_message'] = error_message
        if started_at is not None:
            update_data['started_at'] = started_at
        if completed_at is not None:
            update_data['completed_at'] = completed_at
        
        stmt = update(MessageCheckTask).where(
            MessageCheckTask.id == task_id
        ).values(**update_data)
        
        await self.db.execute(stmt)
        await self.db.commit()
    
    async def _get_task(self, task_id: int) -> Optional[MessageCheckTask]:
        """获取任务信息"""
        stmt = select(MessageCheckTask).where(MessageCheckTask.id == task_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_task_status(self, task_id: int) -> Optional[Dict]:
        """获取任务状态"""
        task = await self._get_task(task_id)
        if not task:
            return None

        return {
            'id': task.id,
            'task_name': task.task_name,
            'check_date': task.check_date,
            'message_type': task.message_type.value,
            'status': task.status.value,
            'progress': task.progress,
            'total_records': task.total_records,
            'error_count': task.error_count,
            'error_message': task.error_message,
            'check_result': task.check_result,  # JSON结果
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None
        }

    def _count_by_interface(self, error_messages: List[Dict]) -> Dict[str, int]:
        """按接口名称统计错误数量"""
        interface_count = {}
        for msg in error_messages:
            interface = msg.get('INTERFACENAME', '未知')
            interface_count[interface] = interface_count.get(interface, 0) + 1
        return interface_count

    def _count_by_status(self, error_messages: List[Dict]) -> Dict[str, int]:
        """按状态统计错误数量"""
        status_count = {}
        for msg in error_messages:
            status = msg.get('ISTATUS', '未知')
            status_count[str(status)] = status_count.get(str(status), 0) + 1
        return status_count

    def _count_by_hour(self, error_messages: List[Dict]) -> Dict[str, int]:
        """按小时统计错误数量"""
        hour_count = {}
        for msg in error_messages:
            worktime = msg.get('WORKTIME', '')
            if worktime:
                try:
                    # 提取小时部分
                    hour = worktime.split(' ')[1].split(':')[0] if ' ' in worktime else '00'
                    hour_count[f"{hour}:00"] = hour_count.get(f"{hour}:00", 0) + 1
                except:
                    hour_count['未知'] = hour_count.get('未知', 0) + 1
        return hour_count

    def _analyze_error_types(self, error_messages: List[Dict]) -> Dict[str, int]:
        """分析错误类型"""
        error_types = {}
        for msg in error_messages:
            error_info = msg.get('ERRORINFO', '')
            if error_info:
                # 简单的错误类型分类
                if 'ORA-' in error_info:
                    error_types['数据库错误'] = error_types.get('数据库错误', 0) + 1
                elif 'timeout' in error_info.lower():
                    error_types['超时错误'] = error_types.get('超时错误', 0) + 1
                elif 'connection' in error_info.lower():
                    error_types['连接错误'] = error_types.get('连接错误', 0) + 1
                else:
                    error_types['其他错误'] = error_types.get('其他错误', 0) + 1
            else:
                error_types['未知错误'] = error_types.get('未知错误', 0) + 1
        return error_types

    async def _save_message_check_details(self, task_id: int, error_messages: List[Dict]):
        """保存电文检查错误明细到专门的表中"""
        try:
            from app.models.steel_making.common.task_result_details import MessageCheckDetail
            from sqlalchemy import delete

            # 删除旧的明细记录
            delete_stmt = delete(MessageCheckDetail).where(
                MessageCheckDetail.task_id == task_id
            )
            await self.db.execute(delete_stmt)

            # 批量插入新的明细记录
            if error_messages:
                detail_records = []
                for msg in error_messages:
                    # 分析错误信息生成建议解决方案
                    error_info = msg.get('ERRORINFO', '')
                    suggested_solution = self._generate_solution_suggestion(error_info)

                    detail = MessageCheckDetail(
                        task_id=task_id,
                        error_type=self._classify_error_type(error_info),
                        error_message=error_info,
                        error_code=msg.get('INTERFACENO', ''),
                        heat_no=self._extract_heat_no(error_info),
                        material_code=self._extract_material_code(error_info),
                        batch_no=self._extract_batch_no(error_info),
                        suggested_solution=suggested_solution
                    )
                    detail_records.append(detail)

                self.db.add_all(detail_records)
                await self.db.commit()
                logger.info(f"保存了 {len(detail_records)} 条电文检查错误明细记录")

        except Exception as e:
            logger.error(f"保存电文检查错误明细失败: {e}")
            await self.db.rollback()
            raise

    def _classify_error_type(self, error_info: str) -> str:
        """分类错误类型"""
        if not error_info:
            return '未知错误'

        error_lower = error_info.lower()
        if 'ora-' in error_lower:
            return '数据库错误'
        elif 'timeout' in error_lower:
            return '超时错误'
        elif 'connection' in error_lower:
            return '连接错误'
        elif 'constraint' in error_lower:
            return '约束错误'
        else:
            return '其他错误'

    def _generate_solution_suggestion(self, error_info: str) -> str:
        """生成解决方案建议"""
        if not error_info:
            return '请检查错误详情'

        error_lower = error_info.lower()
        if 'ora-00001' in error_lower:
            return '主键冲突，请检查数据唯一性'
        elif 'ora-01400' in error_lower:
            return '非空约束违反，请检查必填字段'
        elif 'timeout' in error_lower:
            return '请检查网络连接和系统负载'
        elif 'connection' in error_lower:
            return '请检查数据库连接配置'
        else:
            return '请联系系统管理员进行详细分析'

    def _extract_heat_no(self, error_info: str) -> str:
        """从错误信息中提取炉号"""
        # 简单的正则匹配，可以根据实际情况调整
        import re
        if error_info:
            match = re.search(r'[A-Z]\d{6}', error_info)
            if match:
                return match.group()
        return ''

    def _extract_material_code(self, error_info: str) -> str:
        """从错误信息中提取物料编码"""
        import re
        if error_info:
            match = re.search(r'\d{8}', error_info)
            if match:
                return match.group()
        return ''

    def _extract_batch_no(self, error_info: str) -> str:
        """从错误信息中提取批次号"""
        import re
        if error_info:
            match = re.search(r'BATCH[A-Z0-9]+', error_info)
            if match:
                return match.group()
        return ''
