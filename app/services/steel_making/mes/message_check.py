import asyncio
from typing import List, Dict, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime

from app.models.steel_making.mes.message_check import MessageCheckTask
from app.models.common import TaskStatus, MessageType
from app.services.common.oracle_connection import oracle_manager
from app.utils.logger import logger


class MessageCheckService:
    """电文检查服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_message_check_task(
        self,
        check_date: str,
        message_type: MessageType,
        user_id: int
    ) -> int:
        """创建电文检查任务"""
        task_name = f"ERP电文检查_{message_type.value}_{check_date}"

        task = MessageCheckTask(
            task_name=task_name,
            check_date=check_date,
            message_type=message_type,
            status=TaskStatus.PENDING,
            progress=0,
            total_records=0,
            error_count=0,
            created_by=user_id
        )
        
        self.db.add(task)
        await self.db.commit()
        await self.db.refresh(task)
        
        logger.info(f"创建电文检查任务: {task.id}")
        return task.id
    
    async def start_message_check_task(self, task_id: int, message_type: MessageType, progress_callback: Optional[Callable] = None):
        """启动电文检查任务"""
        # 更新任务状态为运行中
        await self._update_task_status(
            task_id, 
            TaskStatus.RUNNING, 
            progress=0,
            started_at=datetime.now()
        )
        
        try:
            # 获取任务信息
            task = await self._get_task(task_id)
            if not task:
                raise Exception(f"任务 {task_id} 不存在")

            # 执行电文检查
            await self._execute_message_check(task, progress_callback)
            
            # 更新任务状态为完成
            await self._update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                progress=100,
                completed_at=datetime.now()
            )
            
            logger.info(f"电文检查任务 {task_id} 执行完成")
            
        except Exception as e:
            logger.error(f"电文检查任务执行失败: {e}")
            await self._update_task_status(
                task_id,
                TaskStatus.FAILED,
                progress=0,
                error_message=str(e),
                completed_at=datetime.now()
            )
            raise
    
    async def _execute_message_check(self, task: MessageCheckTask, progress_callback: Optional[Callable] = None):
        """执行电文检查逻辑"""
        try:

            if progress_callback:
                await progress_callback(task.id, 10, "步骤1：正在连接ERP数据库...")


            await self._test_erp_connection()
            
            if progress_callback:
                await progress_callback(task.id, 20, "步骤1：ERP数据库连接成功")


            if progress_callback:
                await progress_callback(task.id, 30, f"步骤2：正在查询{task.message_type.value}错误电文...")

            error_messages = await self._get_error_messages(task.check_date, task.message_type)

            if progress_callback:
                await progress_callback(task.id, 70, f"步骤2：查询完成 - 发现{len(error_messages)}条错误电文")


            if progress_callback:
                await progress_callback(task.id, 90, "步骤3：正在生成检查结果...")

            # 转换一下数据结果
            translated_messages = []
            if len(error_messages)>0:
                for message in error_messages:
                    translated_messages.append({
                        "message_id": message["FORMID"],
                        "error_type": message["QUEUEID"],
                        "error_description": message["EXPREASON"],
                        "created_time": message["RECSYSDATE"] + "-" + message["RECSYSTIME"],
                        "message_content": message["DATASTR"],
                    })

            # 生成检查结果JSON
            check_result = {
                "summary": {
                    "check_date": task.check_date,
                    "message_type": task.message_type.value,
                    "error_count": len(error_messages),
                    "check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "system": "ERP"
                },
                "error_messages": translated_messages
            }
            await self._update_task_with_result(task.id, len(error_messages), check_result)

            if progress_callback:
                await progress_callback(task.id, 100, f"步骤3：检查完成 - 共发现{len(error_messages)}条错误电文", check_result)

        except Exception as e:
            logger.error(f"执行电文检查失败: {e}")
            raise
    
    async def _test_erp_connection(self):
        """测试ERP数据库连接"""
        def test_connection():

            erp_conn = oracle_manager.get_erp_connection()
            try:
                oracle_manager.execute_query(erp_conn, "SELECT 1 FROM DUAL", {})
                logger.info("ERP数据库连接测试成功")
            finally:
                oracle_manager.close_connection(erp_conn)
        

        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, test_connection)
    
    async def _get_error_messages(self, check_date: str, message_type: MessageType) -> List[Dict]:
        """获取错误电文数据"""
        def query_error_messages():
            logger.info(f"开始查询{message_type.value}错误电文，日期: {check_date}")
            connection = oracle_manager.get_erp_connection()
            try:
                if message_type == MessageType.RECEIVE:
                    sql = """
                    SELECT RECSYSDATE, RECSYSTIME, QUEUEID, FORMID, DATASTR, EXPREASON
                    FROM DB.TBDZPQUEUEREC
                    WHERE QUEUEID like '%REV'
                      and RECSYSDATE = :check_date
                      and DATASTUS = 'F'
                    order by RECSYSDATE || RECSYSTIME
                    """
                else:
                    sql = """
                    SELECT RECSYSDATE, RECSYSTIME, QUEUEID, FORMID, DATASTR, EXPREASON
                    FROM DB.TBDZPQUEUEREC
                    WHERE QUEUEID like '%SEND%'
                      and RECSYSDATE = :check_date
                      and DATASTUS = 'F'
                    order by RECSYSDATE || RECSYSTIME
                    """

                params = {
                    'check_date': check_date.replace('-', '')
                }

                logger.info(f"执行{message_type.value}电文查询，参数: {params}")
                results = oracle_manager.execute_query(connection, sql, params)
                logger.info(f"{message_type.value}电文查询成功，获取到 {len(results)} 条记录")
                return results
            finally:
                oracle_manager.close_connection(connection)

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, query_error_messages)
    
    async def _update_task_with_result(self, task_id: int, error_count: int, check_result: Dict):
        """更新任务统计信息和结果JSON"""
        stmt = update(MessageCheckTask).where(
            MessageCheckTask.id == task_id
        ).values(
            total_records=error_count,
            error_count=error_count,
            check_result=check_result  # 存储JSON结果
        )

        await self.db.execute(stmt)
        await self.db.commit()
        logger.info(f"更新任务统计信息，错误数: {error_count}")
    
    async def _update_task_status(
        self, 
        task_id: int, 
        status: TaskStatus, 
        progress: Optional[int] = None,
        error_message: Optional[str] = None,
        started_at: Optional[datetime] = None,
        completed_at: Optional[datetime] = None
    ):
        """更新任务状态"""
        update_data = {'status': status}
        
        if progress is not None:
            update_data['progress'] = progress
        if error_message is not None:
            update_data['error_message'] = error_message
        if started_at is not None:
            update_data['started_at'] = started_at
        if completed_at is not None:
            update_data['completed_at'] = completed_at
        
        stmt = update(MessageCheckTask).where(
            MessageCheckTask.id == task_id
        ).values(**update_data)
        
        await self.db.execute(stmt)
        await self.db.commit()
    
    async def _get_task(self, task_id: int) -> Optional[MessageCheckTask]:
        """获取任务信息"""
        stmt = select(MessageCheckTask).where(MessageCheckTask.id == task_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_task_status(self, task_id: int) -> Optional[Dict]:
        """获取任务状态"""
        task = await self._get_task(task_id)
        if not task:
            return None

        return {
            'id': task.id,
            'task_name': task.task_name,
            'check_date': task.check_date,
            'message_type': task.message_type.value,
            'status': task.status.value,
            'progress': task.progress,
            'total_records': task.total_records,
            'error_count': task.error_count,
            'error_message': task.error_message,
            'check_result': task.check_result,  # JSON结果
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None
        }
