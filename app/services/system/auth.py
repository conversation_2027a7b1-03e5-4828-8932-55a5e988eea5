from datetime import datetime, timedelta
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2<PERSON><PERSON>wordBearer
from jose import J<PERSON><PERSON><PERSON>r, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.database.session import get_db
from app.models.system.user import User
from app.models.system.role import Role
from app.models.system.user_role import UserRole
from app.config.settings import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2密码授权方案
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """获取密码哈希值"""
    return pwd_context.hash(password)

async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    """认证用户"""
    stmt = select(User).where(User.username == username)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    if not user:
        return None
    if not verify_password(password, user.password):
        return None
    return user

async def create_access_token(data: dict, expires_delta: Optional[timedelta] = None, db: Optional[AsyncSession] = None) -> str:
    """创建访问令牌(包含权限信息)"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=15)
    to_encode.update({"exp": expire})

    # 添加权限信息
    if "sub" in to_encode and db:
        stmt = select(User).where(User.username == to_encode["sub"])
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        if user:
            permissions = set()
            user_roles_stmt = select(UserRole).where(UserRole.user_id == user.id)
            user_roles_result = await db.execute(user_roles_stmt)
            user_roles = user_roles_result.scalars().all()

            for user_role in user_roles:
                role_stmt = select(Role).where(Role.id == user_role.role_id)
                role_result = await db.execute(role_stmt)
                role = role_result.scalar_one_or_none()
                if role and role.permissions:
                    permissions.update(role.permissions)
            to_encode["permissions"] = list(permissions)

    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db)
) -> User:
    """获取当前用户并加载角色权限信息"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    stmt = select(User).where(User.username == username)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    if user is None:
        raise credentials_exception

    # 加载用户角色和权限信息
    user_roles_stmt = select(UserRole).where(UserRole.user_id == user.id)
    user_roles_result = await db.execute(user_roles_stmt)
    user.roles = user_roles_result.scalars().all()
    user.permissions = payload.get("permissions", [])

    return user