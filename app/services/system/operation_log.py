from datetime import datetime
from contextlib import contextmanager
from typing import Optional
from fastapi import Request, Depends
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.models.system.operation_log import OperationLog
from app.database.session import get_db
from app.services.system.auth import get_current_user
from app.utils import logger


class OperationLogService:
    """操作日志服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.log = None
        self.start_time = None

    @contextmanager
    def record_operation(
        self,
        request: Request,
        operation_type: str,
        operation_content: str,
        user_id: Optional[int] = None,
        user_name: Optional[str] = None
    ):
        """记录操作日志的上下文管理器"""
        self.start_time = datetime.now()
        self.log = OperationLog(
            operation_type=operation_type,
            operation_content=operation_content,
            operator_id=user_id,
            operator_name=user_name,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            status="processing"
        )
        
        try:
            yield self.log
            self.log.status = "success"
        except Exception as e:
            self.log.status = "failed"
            self.log.error_message = str(e)
            raise
        finally:
            self.log.duration = (datetime.now() - self.start_time).total_seconds()
            self.db.add(self.log)
            self.db.commit()

    @staticmethod
    async def list_logs(db: AsyncSession, skip: int = 0, limit: int = 10) -> list[OperationLog]:
        """获取日志列表"""
        try:
            result = await db.execute(select(OperationLog).offset(skip).limit(limit))
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取日志列表失败: {str(e)}")
            raise
    

    async def count_logs(db: AsyncSession) -> int:
        """获取用户总数"""
        try:
            result = await db.execute(select(OperationLog))
            return len(result.scalars().all())
        except Exception as e:
            logger.error(f"获取日志总数失败: {str(e)}")
            raise


async def log_operation(
    request: Request,
    operation_type: str,
    operation_content: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """记录操作的快捷方法"""
    with OperationLogService(db).record_operation(
        request=request,
        operation_type=operation_type,
        operation_content=operation_content,
        user_id=current_user.id,
        user_name=current_user.username
    ):
        pass