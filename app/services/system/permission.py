from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session

from app.database.session import get_db
from app.models.system.user import User
from app.models.system.role import Role
from app.models.system.user_role import UserRole

class PermissionChecker:
    """权限校验器"""
    
    def __init__(self, required_permissions: list[str]):
        self.required_permissions = required_permissions
    
    async def __call__(
        self, 
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        """检查用户是否拥有所需权限"""
        # 获取用户所有角色
        user_roles = db.query(UserRole).filter(
            UserRole.user_id == user.id
        ).all()
        
        if not user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无访问权限"
            )
            
        # 检查角色权限
        for user_role in user_roles:
            role = db.query(Role).filter(
                Role.id == user_role.role_id
            ).first()
            
            if role and any(
                perm in role.permissions 
                for perm in self.required_permissions
            ):
                return True
                
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

def get_current_user_with_permissions(
    user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户并加载权限信息"""
    user.roles = db.query(UserRole).filter(
        UserRole.user_id == user.id
    ).all()
    return user