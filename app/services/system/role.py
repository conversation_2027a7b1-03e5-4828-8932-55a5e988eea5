from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Optional

from app.models.system.role import Role
from app.utils.logger import logger


class RoleService:
    @staticmethod
    async def create_role(
            db: AsyncSession,
            name: str,
            description: Optional[str] = None,
            permissions: Optional[List[str]] = None
    ) -> Role:
        """创建角色"""
        try:
            role = Role(
                name=name,
                description=description,
                permissions=permissions or []
            )
            db.add(role)
            await db.commit()
            await db.refresh(role)
            return role
        except Exception as e:
            logger.error(f"创建角色失败: {str(e)}")
            await db.rollback()
            raise

    @staticmethod
    async def get_role_by_id(db: AsyncSession, role_id: int) -> Optional[Role]:
        """根据ID获取角色"""
        try:
            result = await db.execute(select(Role).where(Role.id == role_id))
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取角色失败: {str(e)}")
            raise

    @staticmethod
    async def update_role(
            db: AsyncSession,
            role_id: int,
            **kwargs
    ) -> Optional[Role]:
        """更新角色信息"""
        try:
            result = await db.execute(select(Role).where(Role.id == role_id))
            role = result.scalar_one_or_none()
            if role:
                for key, value in kwargs.items():
                    setattr(role, key, value)
                await db.commit()
                await db.refresh(role)
            return role
        except Exception as e:
            logger.error(f"更新角色失败: {str(e)}")
            await db.rollback()
            raise

    @staticmethod
    async def delete_role(db: AsyncSession, role_id: int) -> bool:
        """删除角色"""
        try:
            result = await db.execute(select(Role).where(Role.id == role_id))
            role = result.scalar_one_or_none()
            if role:
                await db.delete(role)
                await db.commit()
                return True
            return False
        except Exception as e:
            logger.error(f"删除角色失败: {str(e)}")
            await db.rollback()
            raise

    @staticmethod
    async def list_roles(
            db: AsyncSession,
            skip: int = 0,
            limit: int = 10
    ) -> List[Role]:
        """获取角色列表"""
        try:
            result = await db.execute(select(Role).offset(skip).limit(limit))
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取角色列表失败: {str(e)}")
            raise

    @staticmethod
    async def count_roles(db: AsyncSession) -> int:
        """获取角色总数"""
        try:
            result = await db.execute(select(Role))
            return len(result.scalars().all())
        except Exception as e:
            logger.error(f"获取角色总数失败: {str(e)}")
            raise
