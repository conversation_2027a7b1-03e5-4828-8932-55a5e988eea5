from typing import List
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.models.system.user import User
from app.models.system.role import Role
from app.models.system.user_role import UserRole
from app.constants.permissions import PERMISSIONS

class UserRoleService:
    """用户角色关联服务"""

    @staticmethod
    def bind_user_to_role(db: Session, user_id: int, role_id: int):
        """绑定用户到角色"""
        # 检查关联是否已存在
        existing = db.query(UserRole).filter(
            UserRole.user_id == user_id,
            UserRole.role_id == role_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户已拥有该角色"
            )

        # 创建新关联
        user_role = UserRole(user_id=user_id, role_id=role_id)
        db.add(user_role)
        db.commit()
        return user_role

    @staticmethod
    def unbind_user_from_role(db: Session, user_id: int, role_id: int):
        """从角色解绑用户"""
        user_role = db.query(UserRole).filter(
            UserRole.user_id == user_id,
            UserRole.role_id == role_id
        ).first()
        if not user_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户未拥有该角色"
            )

        db.delete(user_role)
        db.commit()
        return {"message": "解绑成功"}

    @staticmethod
    def get_user_roles(db: Session, user_id: int) -> List[Role]:
        """获取用户所有角色"""
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        return [role.role for role in user.roles]

    @staticmethod
    def get_role_users(db: Session, role_id: int) -> List[User]:
        """获取角色所有用户"""
        role = db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在"
            )

        return [user.user for user in role.users]

    @staticmethod
    def update_role_permissions(db: Session, role_id: int, permissions: List[str]):
        """更新角色权限"""
        role = db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="角色不存在"
            )

        # 验证权限是否有效
        all_perms = [perm for category in PERMISSIONS.values() for perm in category.values()]
        for perm in permissions:
            if perm not in all_perms:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效权限: {perm}"
                )

        role.permissions = permissions
        db.commit()
        return role