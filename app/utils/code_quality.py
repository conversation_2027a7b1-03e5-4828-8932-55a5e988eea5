"""
代码质量工具
提供代码质量检查、重构建议和最佳实践验证
"""

import ast
import inspect
from typing import List, Dict, Any, Optional, Callable, Type
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from app.utils.logger import get_logger

logger = get_logger("code_quality")


class IssueType(str, Enum):
    """问题类型"""
    COMPLEXITY = "complexity"
    DUPLICATION = "duplication"
    NAMING = "naming"
    STRUCTURE = "structure"
    PERFORMANCE = "performance"
    SECURITY = "security"
    MAINTAINABILITY = "maintainability"


class Severity(str, Enum):
    """严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class CodeIssue:
    """代码问题"""
    type: IssueType
    severity: Severity
    message: str
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    function_name: Optional[str] = None
    suggestion: Optional[str] = None


class ComplexityAnalyzer:
    """复杂度分析器"""
    
    def __init__(self, max_complexity: int = 10):
        self.max_complexity = max_complexity
    
    def analyze_function(self, func: Callable) -> List[CodeIssue]:
        """分析函数复杂度"""
        issues = []
        
        try:
            # 获取函数源码
            source = inspect.getsource(func)
            tree = ast.parse(source)
            
            # 计算圈复杂度
            complexity = self._calculate_complexity(tree)
            
            if complexity > self.max_complexity:
                issues.append(CodeIssue(
                    type=IssueType.COMPLEXITY,
                    severity=Severity.HIGH if complexity > 15 else Severity.MEDIUM,
                    message=f"函数复杂度过高: {complexity} (建议 <= {self.max_complexity})",
                    function_name=func.__name__,
                    suggestion="考虑将函数拆分为更小的函数"
                ))
        
        except Exception as e:
            logger.warning(f"分析函数 {func.__name__} 复杂度失败: {e}")
        
        return issues
    
    def _calculate_complexity(self, tree: ast.AST) -> int:
        """计算圈复杂度"""
        complexity = 1  # 基础复杂度
        
        for node in ast.walk(tree):
            # 条件语句增加复杂度
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            # 异常处理增加复杂度
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            # 逻辑运算符增加复杂度
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        
        return complexity


class NamingAnalyzer:
    """命名规范分析器"""
    
    def __init__(self):
        self.snake_case_pattern = r'^[a-z_][a-z0-9_]*$'
        self.pascal_case_pattern = r'^[A-Z][a-zA-Z0-9]*$'
        self.constant_pattern = r'^[A-Z_][A-Z0-9_]*$'
    
    def analyze_function(self, func: Callable) -> List[CodeIssue]:
        """分析函数命名"""
        issues = []
        
        # 检查函数名
        if not self._is_snake_case(func.__name__):
            issues.append(CodeIssue(
                type=IssueType.NAMING,
                severity=Severity.LOW,
                message=f"函数名不符合snake_case规范: {func.__name__}",
                function_name=func.__name__,
                suggestion="使用snake_case命名函数"
            ))
        
        # 检查函数名长度
        if len(func.__name__) < 3:
            issues.append(CodeIssue(
                type=IssueType.NAMING,
                severity=Severity.MEDIUM,
                message=f"函数名过短: {func.__name__}",
                function_name=func.__name__,
                suggestion="使用更具描述性的函数名"
            ))
        
        return issues
    
    def analyze_class(self, cls: Type) -> List[CodeIssue]:
        """分析类命名"""
        issues = []
        
        # 检查类名
        if not self._is_pascal_case(cls.__name__):
            issues.append(CodeIssue(
                type=IssueType.NAMING,
                severity=Severity.LOW,
                message=f"类名不符合PascalCase规范: {cls.__name__}",
                suggestion="使用PascalCase命名类"
            ))
        
        return issues
    
    def _is_snake_case(self, name: str) -> bool:
        """检查是否为snake_case"""
        import re
        return bool(re.match(self.snake_case_pattern, name))
    
    def _is_pascal_case(self, name: str) -> bool:
        """检查是否为PascalCase"""
        import re
        return bool(re.match(self.pascal_case_pattern, name))


class DuplicationAnalyzer:
    """重复代码分析器"""
    
    def __init__(self, min_lines: int = 5):
        self.min_lines = min_lines
    
    def analyze_functions(self, functions: List[Callable]) -> List[CodeIssue]:
        """分析函数重复"""
        issues = []
        
        # 获取所有函数的源码
        function_sources = {}
        for func in functions:
            try:
                source = inspect.getsource(func)
                function_sources[func.__name__] = source
            except Exception:
                continue
        
        # 检查重复代码
        for i, (name1, source1) in enumerate(function_sources.items()):
            for name2, source2 in list(function_sources.items())[i+1:]:
                similarity = self._calculate_similarity(source1, source2)
                
                if similarity > 0.8:  # 80%相似度
                    issues.append(CodeIssue(
                        type=IssueType.DUPLICATION,
                        severity=Severity.MEDIUM,
                        message=f"函数 {name1} 和 {name2} 存在重复代码 (相似度: {similarity:.1%})",
                        suggestion="考虑提取公共函数或使用继承"
                    ))
        
        return issues
    
    def _calculate_similarity(self, source1: str, source2: str) -> float:
        """计算代码相似度"""
        lines1 = [line.strip() for line in source1.split('\n') if line.strip()]
        lines2 = [line.strip() for line in source2.split('\n') if line.strip()]
        
        if not lines1 or not lines2:
            return 0.0
        
        # 简单的行匹配算法
        matches = 0
        for line1 in lines1:
            if line1 in lines2:
                matches += 1
        
        return matches / max(len(lines1), len(lines2))


class StructureAnalyzer:
    """结构分析器"""
    
    def analyze_function(self, func: Callable) -> List[CodeIssue]:
        """分析函数结构"""
        issues = []
        
        try:
            # 获取函数参数数量
            sig = inspect.signature(func)
            param_count = len(sig.parameters)
            
            if param_count > 5:
                issues.append(CodeIssue(
                    type=IssueType.STRUCTURE,
                    severity=Severity.MEDIUM,
                    message=f"函数参数过多: {param_count} (建议 <= 5)",
                    function_name=func.__name__,
                    suggestion="考虑使用数据类或字典封装参数"
                ))
            
            # 检查函数长度
            source = inspect.getsource(func)
            line_count = len([line for line in source.split('\n') if line.strip()])
            
            if line_count > 50:
                issues.append(CodeIssue(
                    type=IssueType.STRUCTURE,
                    severity=Severity.HIGH,
                    message=f"函数过长: {line_count} 行 (建议 <= 50)",
                    function_name=func.__name__,
                    suggestion="将函数拆分为更小的函数"
                ))
        
        except Exception as e:
            logger.warning(f"分析函数 {func.__name__} 结构失败: {e}")
        
        return issues


class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self):
        self.complexity_analyzer = ComplexityAnalyzer()
        self.naming_analyzer = NamingAnalyzer()
        self.duplication_analyzer = DuplicationAnalyzer()
        self.structure_analyzer = StructureAnalyzer()
    
    def check_function(self, func: Callable) -> List[CodeIssue]:
        """检查函数质量"""
        issues = []
        
        # 复杂度检查
        issues.extend(self.complexity_analyzer.analyze_function(func))
        
        # 命名检查
        issues.extend(self.naming_analyzer.analyze_function(func))
        
        # 结构检查
        issues.extend(self.structure_analyzer.analyze_function(func))
        
        return issues
    
    def check_class(self, cls: Type) -> List[CodeIssue]:
        """检查类质量"""
        issues = []
        
        # 命名检查
        issues.extend(self.naming_analyzer.analyze_class(cls))
        
        # 检查类的方法
        for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
            if not name.startswith('_'):  # 跳过私有方法
                method_issues = self.check_function(method)
                for issue in method_issues:
                    issue.function_name = f"{cls.__name__}.{name}"
                issues.extend(method_issues)
        
        return issues
    
    def check_module(self, module) -> List[CodeIssue]:
        """检查模块质量"""
        issues = []
        
        # 检查模块中的函数
        functions = []
        for name, obj in inspect.getmembers(module, predicate=inspect.isfunction):
            if obj.__module__ == module.__name__:  # 只检查本模块定义的函数
                issues.extend(self.check_function(obj))
                functions.append(obj)
        
        # 检查重复代码
        if len(functions) > 1:
            issues.extend(self.duplication_analyzer.analyze_functions(functions))
        
        # 检查模块中的类
        for name, obj in inspect.getmembers(module, predicate=inspect.isclass):
            if obj.__module__ == module.__name__:  # 只检查本模块定义的类
                issues.extend(self.check_class(obj))
        
        return issues
    
    def generate_report(self, issues: List[CodeIssue]) -> Dict[str, Any]:
        """生成质量报告"""
        report = {
            "total_issues": len(issues),
            "by_type": {},
            "by_severity": {},
            "suggestions": []
        }
        
        # 按类型统计
        for issue in issues:
            report["by_type"][issue.type] = report["by_type"].get(issue.type, 0) + 1
        
        # 按严重程度统计
        for issue in issues:
            report["by_severity"][issue.severity] = report["by_severity"].get(issue.severity, 0) + 1
        
        # 收集建议
        suggestions = set()
        for issue in issues:
            if issue.suggestion:
                suggestions.add(issue.suggestion)
        report["suggestions"] = list(suggestions)
        
        return report


# 全局质量检查器
quality_checker = CodeQualityChecker()


def check_code_quality(target) -> List[CodeIssue]:
    """检查代码质量的便捷函数"""
    if inspect.isfunction(target):
        return quality_checker.check_function(target)
    elif inspect.isclass(target):
        return quality_checker.check_class(target)
    elif inspect.ismodule(target):
        return quality_checker.check_module(target)
    else:
        logger.warning(f"不支持的检查目标类型: {type(target)}")
        return []


def log_quality_issues(issues: List[CodeIssue]):
    """记录质量问题到日志"""
    if not issues:
        logger.info("代码质量检查通过，未发现问题")
        return
    
    for issue in issues:
        log_level = {
            Severity.LOW: logger.info,
            Severity.MEDIUM: logger.warning,
            Severity.HIGH: logger.error,
            Severity.CRITICAL: logger.critical
        }.get(issue.severity, logger.info)
        
        log_level(f"代码质量问题: {issue.message}", extra={
            "issue_type": issue.type,
            "severity": issue.severity,
            "function": issue.function_name,
            "suggestion": issue.suggestion
        })
