"""
数据库操作工具
提供事务管理、连接池管理和数据库操作装饰器
"""

import functools
import asyncio
from typing import Callable, Any, Optional, Dict, List, Union
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
from sqlalchemy import text

from app.database.session import SessionLocal, engine
from app.exceptions import DatabaseException, DatabaseConnectionException, DatabaseIntegrityException
from app.utils.logger import get_logger
from app.utils.monitoring import timing_decorator, metrics_collector

logger = get_logger("database")


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connection_pool_stats = {
            "active_connections": 0,
            "total_connections": 0,
            "failed_connections": 0
        }
    
    async def get_connection_info(self) -> Dict[str, Any]:
        """获取连接池信息"""
        try:
            pool = engine.pool
            return {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid()
            }
        except Exception as e:
            logger.error(f"获取连接池信息失败: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """数据库健康检查"""
        try:
            async with SessionLocal() as session:
                # 执行简单查询测试连接
                result = await session.execute(text("SELECT 1 FROM DUAL"))
                result.fetchone()
                
                return {
                    "status": "healthy",
                    "timestamp": "now()",
                    "connection_info": await self.get_connection_info()
                }
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": "now()"
            }


# 全局数据库管理器
db_manager = DatabaseManager()


@asynccontextmanager
async def get_db_session():
    """获取数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception as e:
        await session.rollback()
        logger.error(f"数据库会话异常: {e}")
        raise
    finally:
        await session.close()


@asynccontextmanager
async def transaction_context(session: Optional[AsyncSession] = None):
    """事务上下文管理器"""
    if session is None:
        async with get_db_session() as session:
            async with transaction_context(session) as tx_session:
                yield tx_session
    else:
        try:
            yield session
            await session.commit()
            logger.debug("事务提交成功")
        except Exception as e:
            await session.rollback()
            logger.error(f"事务回滚: {e}")
            raise


def transactional(
    rollback_on_exception: bool = True,
    isolation_level: Optional[str] = None
):
    """
    事务装饰器
    
    Args:
        rollback_on_exception: 异常时是否回滚
        isolation_level: 事务隔离级别
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 查找session参数
            session = None
            for arg in args:
                if isinstance(arg, AsyncSession):
                    session = arg
                    break
            
            if 'db' in kwargs and isinstance(kwargs['db'], AsyncSession):
                session = kwargs['db']
            
            if session is None:
                # 如果没有提供session，创建新的
                async with get_db_session() as new_session:
                    # 替换参数中的session
                    new_args = []
                    for arg in args:
                        if isinstance(arg, AsyncSession):
                            new_args.append(new_session)
                        else:
                            new_args.append(arg)
                    
                    if 'db' in kwargs:
                        kwargs['db'] = new_session
                    
                    async with transaction_context(new_session):
                        return await func(*new_args, **kwargs)
            else:
                # 使用现有session
                async with transaction_context(session):
                    return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator


def handle_database_exceptions(operation_name: str = "数据库操作"):
    """数据库异常处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except IntegrityError as e:
                error_msg = f"{operation_name}失败: 数据完整性错误"
                logger.error(f"{error_msg}: {e}")
                metrics_collector.record_metric(
                    f"database.integrity_error",
                    1,
                    {"operation": operation_name}
                )
                raise DatabaseIntegrityException(error_msg)
            except OperationalError as e:
                error_msg = f"{operation_name}失败: 数据库连接错误"
                logger.error(f"{error_msg}: {e}")
                metrics_collector.record_metric(
                    f"database.connection_error",
                    1,
                    {"operation": operation_name}
                )
                raise DatabaseConnectionException(error_msg)
            except SQLAlchemyError as e:
                error_msg = f"{operation_name}失败: 数据库错误"
                logger.error(f"{error_msg}: {e}")
                metrics_collector.record_metric(
                    f"database.general_error",
                    1,
                    {"operation": operation_name}
                )
                raise DatabaseException(error_msg)
            except Exception as e:
                error_msg = f"{operation_name}失败: 未知错误"
                logger.error(f"{error_msg}: {e}")
                metrics_collector.record_metric(
                    f"database.unknown_error",
                    1,
                    {"operation": operation_name}
                )
                raise DatabaseException(error_msg)
        
        return wrapper
    
    return decorator


@timing_decorator(log_slow_queries=True, slow_threshold=0.5)
@handle_database_exceptions("批量插入")
async def bulk_insert(
    session: AsyncSession,
    model_class: Any,
    data_list: List[Dict[str, Any]],
    batch_size: int = 1000
) -> int:
    """批量插入数据"""
    if not data_list:
        return 0
    
    total_inserted = 0
    
    # 分批处理
    for i in range(0, len(data_list), batch_size):
        batch = data_list[i:i + batch_size]
        
        try:
            # 创建模型实例
            instances = [model_class(**item) for item in batch]
            
            # 批量添加
            session.add_all(instances)
            await session.flush()
            
            total_inserted += len(instances)
            
            logger.debug(f"批量插入 {len(instances)} 条记录到 {model_class.__name__}")
            
        except Exception as e:
            logger.error(f"批量插入第 {i//batch_size + 1} 批数据失败: {e}")
            raise
    
    return total_inserted


@timing_decorator(log_slow_queries=True, slow_threshold=0.5)
@handle_database_exceptions("批量更新")
async def bulk_update(
    session: AsyncSession,
    model_class: Any,
    updates: List[Dict[str, Any]],
    id_field: str = "id"
) -> int:
    """批量更新数据"""
    if not updates:
        return 0
    
    try:
        from sqlalchemy import update
        
        # 构建批量更新语句
        stmt = update(model_class)
        
        # 执行批量更新
        result = await session.execute(stmt, updates)
        updated_count = result.rowcount
        
        logger.debug(f"批量更新 {updated_count} 条 {model_class.__name__} 记录")
        
        return updated_count
        
    except Exception as e:
        logger.error(f"批量更新失败: {e}")
        raise


@timing_decorator(log_slow_queries=True, slow_threshold=0.5)
@handle_database_exceptions("批量删除")
async def bulk_delete(
    session: AsyncSession,
    model_class: Any,
    condition_field: str,
    condition_values: List[Any]
) -> int:
    """批量删除数据"""
    if not condition_values:
        return 0
    
    try:
        from sqlalchemy import delete
        
        # 构建删除语句
        stmt = delete(model_class).where(
            getattr(model_class, condition_field).in_(condition_values)
        )
        
        # 执行删除
        result = await session.execute(stmt)
        deleted_count = result.rowcount
        
        logger.debug(f"批量删除 {deleted_count} 条 {model_class.__name__} 记录")
        
        return deleted_count
        
    except Exception as e:
        logger.error(f"批量删除失败: {e}")
        raise


@timing_decorator(log_slow_queries=True, slow_threshold=1.0)
@handle_database_exceptions("执行原生SQL")
async def execute_raw_sql(
    session: AsyncSession,
    sql: str,
    params: Optional[Dict[str, Any]] = None
) -> Any:
    """执行原生SQL"""
    try:
        result = await session.execute(text(sql), params or {})
        
        logger.debug(f"执行SQL: {sql[:100]}...")
        
        return result
        
    except Exception as e:
        logger.error(f"执行SQL失败: {sql[:100]}... - {e}")
        raise


class DatabaseConnectionPool:
    """数据库连接池管理"""
    
    def __init__(self):
        self.stats = {
            "total_connections": 0,
            "active_connections": 0,
            "failed_connections": 0,
            "avg_connection_time": 0.0
        }
    
    async def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        try:
            pool = engine.pool
            return {
                "size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid(),
                "stats": self.stats
            }
        except Exception as e:
            logger.error(f"获取连接池状态失败: {e}")
            return {"error": str(e)}
    
    async def recreate_pool(self):
        """重新创建连接池"""
        try:
            await engine.dispose()
            logger.info("数据库连接池已重新创建")
        except Exception as e:
            logger.error(f"重新创建连接池失败: {e}")
            raise


# 全局连接池管理器
connection_pool = DatabaseConnectionPool()
