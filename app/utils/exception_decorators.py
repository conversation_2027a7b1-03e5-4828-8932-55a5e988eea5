"""
异常处理装饰器
提供常用的异常处理装饰器，简化服务层的异常处理逻辑
"""

from functools import wraps
from typing import Callable, Type, Optional, Dict, Any, Union, List
import asyncio
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
import oracledb

from app.exceptions import (
    BaseAPIException, 
    DatabaseException, 
    DatabaseConnectionException,
    DatabaseQueryException,
    DatabaseTransactionException,
    OracleConnectionException,
    BusinessLogicException,
    ErrorCode
)
from app.utils.logger import logger


def handle_database_errors(
    operation_name: str = "数据库操作",
    include_context: bool = True
):
    """
    数据库操作异常处理装饰器
    
    Args:
        operation_name: 操作名称，用于错误日志
        include_context: 是否在异常中包含上下文信息
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except BaseAPIException:
                # 重新抛出自定义异常
                raise
            except IntegrityError as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - 数据完整性错误: {str(e)}")
                raise DatabaseTransactionException(
                    detail=f"{operation_name}失败：数据完整性约束违反",
                    context=context
                )
            except OperationalError as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - 数据库连接错误: {str(e)}")
                raise DatabaseConnectionException(
                    detail=f"{operation_name}失败：数据库连接异常",
                    context=context
                )
            except SQLAlchemyError as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - SQLAlchemy错误: {str(e)}")
                raise DatabaseQueryException(
                    detail=f"{operation_name}失败：数据库查询异常",
                    context=context
                )
            except Exception as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - 未知错误: {str(e)}")
                raise DatabaseException(
                    error_code=ErrorCode.DATABASE_QUERY_ERROR,
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BaseAPIException:
                # 重新抛出自定义异常
                raise
            except IntegrityError as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - 数据完整性错误: {str(e)}")
                raise DatabaseTransactionException(
                    detail=f"{operation_name}失败：数据完整性约束违反",
                    context=context
                )
            except OperationalError as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - 数据库连接错误: {str(e)}")
                raise DatabaseConnectionException(
                    detail=f"{operation_name}失败：数据库连接异常",
                    context=context
                )
            except SQLAlchemyError as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - SQLAlchemy错误: {str(e)}")
                raise DatabaseQueryException(
                    detail=f"{operation_name}失败：数据库查询异常",
                    context=context
                )
            except Exception as e:
                context = {"operation": operation_name, "args": str(args), "kwargs": str(kwargs)} if include_context else None
                logger.error(f"{operation_name}失败 - 未知错误: {str(e)}")
                raise DatabaseException(
                    error_code=ErrorCode.DATABASE_QUERY_ERROR,
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def handle_oracle_errors(
    operation_name: str = "Oracle操作",
    include_context: bool = True
):
    """
    Oracle数据库操作异常处理装饰器
    
    Args:
        operation_name: 操作名称，用于错误日志
        include_context: 是否在异常中包含上下文信息
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except BaseAPIException:
                # 重新抛出自定义异常
                raise
            except oracledb.Error as e:
                context = {"operation": operation_name, "oracle_error": str(e)} if include_context else None
                logger.error(f"{operation_name}失败 - Oracle错误: {str(e)}")
                raise OracleConnectionException(
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
            except Exception as e:
                context = {"operation": operation_name, "error": str(e)} if include_context else None
                logger.error(f"{operation_name}失败 - 未知错误: {str(e)}")
                raise OracleConnectionException(
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BaseAPIException:
                # 重新抛出自定义异常
                raise
            except oracledb.Error as e:
                context = {"operation": operation_name, "oracle_error": str(e)} if include_context else None
                logger.error(f"{operation_name}失败 - Oracle错误: {str(e)}")
                raise OracleConnectionException(
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
            except Exception as e:
                context = {"operation": operation_name, "error": str(e)} if include_context else None
                logger.error(f"{operation_name}失败 - 未知错误: {str(e)}")
                raise OracleConnectionException(
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def handle_business_errors(
    operation_name: str = "业务操作",
    error_code: ErrorCode = ErrorCode.BUSINESS_LOGIC_ERROR,
    include_context: bool = True
):
    """
    业务逻辑异常处理装饰器
    
    Args:
        operation_name: 操作名称，用于错误日志
        error_code: 错误代码
        include_context: 是否在异常中包含上下文信息
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except BaseAPIException:
                # 重新抛出自定义异常
                raise
            except Exception as e:
                context = {"operation": operation_name, "error": str(e)} if include_context else None
                logger.error(f"{operation_name}失败: {str(e)}")
                raise BusinessLogicException(
                    error_code=error_code,
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BaseAPIException:
                # 重新抛出自定义异常
                raise
            except Exception as e:
                context = {"operation": operation_name, "error": str(e)} if include_context else None
                logger.error(f"{operation_name}失败: {str(e)}")
                raise BusinessLogicException(
                    error_code=error_code,
                    detail=f"{operation_name}失败：{str(e)}",
                    context=context
                )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def retry_on_failure(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception
):
    """
    失败重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
    """
    if not isinstance(exceptions, (list, tuple)):
        exceptions = [exceptions]
    
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except tuple(exceptions) as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"第{attempt + 1}次尝试失败，{current_delay}秒后重试: {str(e)}")
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        logger.error(f"重试{max_retries}次后仍然失败: {str(e)}")
                        raise
                except Exception as e:
                    # 不在重试列表中的异常直接抛出
                    raise
            
            # 如果所有重试都失败了，抛出最后一个异常
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    last_exception = e
                    if attempt < max_retries:
                        logger.warning(f"第{attempt + 1}次尝试失败，{current_delay}秒后重试: {str(e)}")
                        import time
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        logger.error(f"重试{max_retries}次后仍然失败: {str(e)}")
                        raise
                except Exception as e:
                    # 不在重试列表中的异常直接抛出
                    raise
            
            # 如果所有重试都失败了，抛出最后一个异常
            raise last_exception
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator
