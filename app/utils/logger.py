import logging
import logging.handlers
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from contextvars import ContextVar

from app.core.config import LoggingConfig

# 上下文变量用于存储请求相关信息
request_context: ContextVar[Dict[str, Any]] = ContextVar('request_context', default={})


class StructuredFormatter(logging.Formatter):
    """结构化日志格式器"""

    def format(self, record: logging.LogRecord) -> str:
        # 获取请求上下文
        context = request_context.get({})

        # 构建日志数据
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # 添加请求上下文信息
        if context:
            log_data["context"] = context

        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_data.update(record.extra_fields)

        return json.dumps(log_data, ensure_ascii=False, default=str)


class ContextualFormatter(logging.Formatter):
    """带上下文的普通格式器"""

    def format(self, record: logging.LogRecord) -> str:
        # 获取请求上下文
        context = request_context.get({})

        # 构建基础消息
        base_message = super().format(record)

        # 添加上下文信息
        if context:
            context_str = " | ".join([f"{k}={v}" for k, v in context.items()])
            base_message = f"{base_message} | {context_str}"

        return base_message


def setup_logging(config: LoggingConfig):
    """设置日志系统"""
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.level.upper()))

    # 清除现有处理器
    root_logger.handlers.clear()

    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)

    if config.enable_json_logs:
        formatter = StructuredFormatter()
    else:
        formatter = ContextualFormatter(config.format)

    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 添加文件处理器
    if config.file_path:
        log_path = Path(config.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_path,
            maxBytes=config.max_file_size,
            backupCount=config.backup_count,
            encoding='utf-8'
        )

        if config.enable_json_logs:
            formatter = StructuredFormatter()
        else:
            formatter = ContextualFormatter(config.format)

        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # 配置第三方库日志级别
    if not config.enable_sql_logging:
        logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
        logging.getLogger('sqlalchemy.pool').setLevel(logging.WARNING)

    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)


def get_logger(name: str = "sta_keeper") -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


def set_request_context(**kwargs):
    """设置请求上下文"""
    context = request_context.get({})
    context.update(kwargs)
    request_context.set(context)


def clear_request_context():
    """清除请求上下文"""
    request_context.set({})


# 创建默认日志器
logger = get_logger()