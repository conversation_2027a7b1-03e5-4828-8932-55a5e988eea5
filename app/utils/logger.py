import logging
from logging.handlers import RotatingFileHandler
from app.config import get_settings

settings = get_settings()

def setup_logging():
    logger = logging.getLogger(settings.APP_NAME)
    if settings.DEBUG:
        logger.setLevel(logging.DEBUG)
    else:
        logger.setLevel(logging.INFO)
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    if settings.LOG_TO_FILE:
        # 生产环境：日志输出到文件，按大小轮转
        file_handler = RotatingFileHandler(
            settings.LOG_FILE,
            maxBytes=1024 * 1024 * 5,  # 5MB
            backupCount=3
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    else:
        # 开发环境：日志输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    return logger

logger = setup_logging()