"""
性能监控和指标收集工具
提供装饰器和工具函数用于监控应用性能
"""

import time
import functools
import asyncio
from typing import Callable, Any, Dict, Optional, Union
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
from contextlib import contextmanager

from app.utils.logger import get_logger, set_request_context

logger = get_logger("monitoring")


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_records))
        self._lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录指标"""
        with self._lock:
            record = {
                "timestamp": datetime.now(),
                "value": value,
                "tags": tags or {}
            }
            self._metrics[name].append(record)
    
    def get_metrics(self, name: str, since: Optional[datetime] = None) -> list:
        """获取指标数据"""
        with self._lock:
            metrics = list(self._metrics[name])
            
            if since:
                metrics = [m for m in metrics if m["timestamp"] >= since]
            
            return metrics
    
    def get_average(self, name: str, since: Optional[datetime] = None) -> float:
        """获取平均值"""
        metrics = self.get_metrics(name, since)
        if not metrics:
            return 0.0
        
        return sum(m["value"] for m in metrics) / len(metrics)
    
    def get_percentile(self, name: str, percentile: float, since: Optional[datetime] = None) -> float:
        """获取百分位数"""
        metrics = self.get_metrics(name, since)
        if not metrics:
            return 0.0
        
        values = sorted([m["value"] for m in metrics])
        index = int(len(values) * percentile / 100)
        return values[min(index, len(values) - 1)]
    
    def clear_metrics(self, name: Optional[str] = None):
        """清除指标数据"""
        with self._lock:
            if name:
                self._metrics[name].clear()
            else:
                self._metrics.clear()


# 全局指标收集器
metrics_collector = PerformanceMetrics()


def timing_decorator(
    metric_name: Optional[str] = None,
    log_slow_queries: bool = True,
    slow_threshold: float = 1.0,
    include_args: bool = False
):
    """
    性能计时装饰器
    
    Args:
        metric_name: 指标名称，默认使用函数名
        log_slow_queries: 是否记录慢查询
        slow_threshold: 慢查询阈值（秒）
        include_args: 是否在日志中包含参数
    """
    def decorator(func: Callable) -> Callable:
        name = metric_name or f"{func.__module__}.{func.__name__}"
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    # 记录异常指标
                    metrics_collector.record_metric(
                        f"{name}.error",
                        1,
                        {"error_type": type(e).__name__}
                    )
                    raise
                finally:
                    # 记录执行时间
                    execution_time = time.time() - start_time
                    metrics_collector.record_metric(f"{name}.duration", execution_time)
                    
                    # 记录慢查询
                    if log_slow_queries and execution_time > slow_threshold:
                        log_data = {
                            "function": name,
                            "duration": execution_time,
                            "threshold": slow_threshold
                        }
                        
                        if include_args:
                            log_data["args"] = str(args)
                            log_data["kwargs"] = str(kwargs)
                        
                        logger.warning(f"慢查询检测: {name} 执行时间 {execution_time:.3f}s", extra=log_data)
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    # 记录异常指标
                    metrics_collector.record_metric(
                        f"{name}.error",
                        1,
                        {"error_type": type(e).__name__}
                    )
                    raise
                finally:
                    # 记录执行时间
                    execution_time = time.time() - start_time
                    metrics_collector.record_metric(f"{name}.duration", execution_time)
                    
                    # 记录慢查询
                    if log_slow_queries and execution_time > slow_threshold:
                        log_data = {
                            "function": name,
                            "duration": execution_time,
                            "threshold": slow_threshold
                        }
                        
                        if include_args:
                            log_data["args"] = str(args)
                            log_data["kwargs"] = str(kwargs)
                        
                        logger.warning(f"慢查询检测: {name} 执行时间 {execution_time:.3f}s", extra=log_data)
            
            return sync_wrapper
    
    return decorator


def monitor_memory_usage(func: Callable) -> Callable:
    """内存使用监控装饰器"""
    import psutil
    import os
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        process = psutil.Process(os.getpid())
        
        # 记录执行前内存使用
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # 记录执行后内存使用
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_diff = memory_after - memory_before
            
            # 记录内存指标
            function_name = f"{func.__module__}.{func.__name__}"
            metrics_collector.record_metric(f"{function_name}.memory_usage", memory_after)
            metrics_collector.record_metric(f"{function_name}.memory_diff", memory_diff)
            
            # 如果内存增长过多，记录警告
            if memory_diff > 100:  # 100MB
                logger.warning(
                    f"内存使用异常: {function_name} 内存增长 {memory_diff:.2f}MB",
                    extra={
                        "function": function_name,
                        "memory_before": memory_before,
                        "memory_after": memory_after,
                        "memory_diff": memory_diff
                    }
                )
    
    return wrapper


@contextmanager
def performance_context(operation_name: str, **context_data):
    """性能监控上下文管理器"""
    start_time = time.time()
    
    # 设置请求上下文
    set_request_context(operation=operation_name, **context_data)
    
    try:
        yield
    except Exception as e:
        # 记录异常
        metrics_collector.record_metric(
            f"{operation_name}.error",
            1,
            {"error_type": type(e).__name__}
        )
        logger.error(f"操作异常: {operation_name}", exc_info=True)
        raise
    finally:
        # 记录执行时间
        execution_time = time.time() - start_time
        metrics_collector.record_metric(f"{operation_name}.duration", execution_time)
        
        logger.info(
            f"操作完成: {operation_name} 耗时 {execution_time:.3f}s",
            extra={"operation": operation_name, "duration": execution_time}
        )


class RequestMetrics:
    """请求指标收集器"""
    
    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.total_duration = 0.0
        self._lock = threading.Lock()
    
    def record_request(self, duration: float, status_code: int):
        """记录请求"""
        with self._lock:
            self.request_count += 1
            self.total_duration += duration
            
            if status_code >= 400:
                self.error_count += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            if self.request_count == 0:
                return {
                    "request_count": 0,
                    "error_count": 0,
                    "error_rate": 0.0,
                    "average_duration": 0.0
                }
            
            return {
                "request_count": self.request_count,
                "error_count": self.error_count,
                "error_rate": self.error_count / self.request_count,
                "average_duration": self.total_duration / self.request_count
            }
    
    def reset(self):
        """重置统计"""
        with self._lock:
            self.request_count = 0
            self.error_count = 0
            self.total_duration = 0.0


# 全局请求指标收集器
request_metrics = RequestMetrics()


def get_performance_summary(since_minutes: int = 60) -> Dict[str, Any]:
    """获取性能摘要"""
    since = datetime.now() - timedelta(minutes=since_minutes)
    
    summary = {
        "timestamp": datetime.now().isoformat(),
        "period_minutes": since_minutes,
        "request_stats": request_metrics.get_stats(),
        "top_slow_operations": [],
        "error_summary": {}
    }
    
    # 获取慢操作
    all_metrics = metrics_collector._metrics
    duration_metrics = {k: v for k, v in all_metrics.items() if k.endswith('.duration')}
    
    for metric_name, records in duration_metrics.items():
        recent_records = [r for r in records if r["timestamp"] >= since]
        if recent_records:
            avg_duration = sum(r["value"] for r in recent_records) / len(recent_records)
            max_duration = max(r["value"] for r in recent_records)
            
            summary["top_slow_operations"].append({
                "operation": metric_name.replace('.duration', ''),
                "count": len(recent_records),
                "avg_duration": avg_duration,
                "max_duration": max_duration
            })
    
    # 按平均时间排序
    summary["top_slow_operations"].sort(key=lambda x: x["avg_duration"], reverse=True)
    summary["top_slow_operations"] = summary["top_slow_operations"][:10]
    
    return summary


def log_performance_summary():
    """记录性能摘要到日志"""
    summary = get_performance_summary()
    logger.info("性能摘要", extra={"performance_summary": summary})
