"""
类型定义和类型提示工具
提供项目中使用的通用类型定义
"""

from typing import (
    TypeVar, Generic, Optional, Union, Dict, List, Any, 
    Callable, Awaitable, Protocol, runtime_checkable
)
from datetime import datetime
from enum import Enum
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

# 通用类型变量
T = TypeVar('T')
ModelType = TypeVar('ModelType')
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)


class ResponseStatus(str, Enum):
    """响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


# 数据库相关类型
DatabaseSession = AsyncSession
DatabaseResult = Dict[str, Any]
DatabaseRecord = Dict[str, Any]
DatabaseRecords = List[DatabaseRecord]

# 请求响应类型
RequestData = Dict[str, Any]
ResponseData = Dict[str, Any]
QueryParams = Dict[str, Union[str, int, float, bool]]
PathParams = Dict[str, Union[str, int]]

# 分页类型
class PaginationParams(BaseModel):
    """分页参数"""
    page: int = 1
    size: int = 20
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.size


class PaginationResult(BaseModel, Generic[T]):
    """分页结果"""
    items: List[T]
    total: int
    page: int
    size: int
    pages: int
    
    @classmethod
    def create(cls, items: List[T], total: int, page: int, size: int) -> 'PaginationResult[T]':
        pages = (total + size - 1) // size
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )


class APIResponse(BaseModel, Generic[T]):
    """API响应模型"""
    status: ResponseStatus
    message: str
    data: Optional[T] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = datetime.now()
    
    @classmethod
    def success(cls, data: T, message: str = "操作成功") -> 'APIResponse[T]':
        return cls(status=ResponseStatus.SUCCESS, message=message, data=data)
    
    @classmethod
    def error(cls, message: str, errors: Optional[List[str]] = None) -> 'APIResponse[None]':
        return cls(status=ResponseStatus.ERROR, message=message, errors=errors)
    
    @classmethod
    def warning(cls, message: str, data: Optional[T] = None) -> 'APIResponse[T]':
        return cls(status=ResponseStatus.WARNING, message=message, data=data)


class TaskResult(BaseModel, Generic[T]):
    """任务结果模型"""
    task_id: str
    status: TaskStatus
    result: Optional[T] = None
    error: Optional[str] = None
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None
    
    def mark_completed(self, result: T):
        """标记任务完成"""
        self.status = TaskStatus.COMPLETED
        self.result = result
        self.completed_at = datetime.now()
        if self.started_at:
            self.duration = (self.completed_at - self.started_at).total_seconds()
    
    def mark_failed(self, error: str):
        """标记任务失败"""
        self.status = TaskStatus.FAILED
        self.error = error
        self.completed_at = datetime.now()
        if self.started_at:
            self.duration = (self.completed_at - self.started_at).total_seconds()


# 协议定义
@runtime_checkable
class Repository(Protocol[T]):
    """仓储协议"""
    
    async def get_by_id(self, id: Any) -> Optional[T]:
        """根据ID获取实体"""
        ...
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[T]:
        """获取所有实体"""
        ...
    
    async def create(self, obj_in: CreateSchemaType) -> T:
        """创建实体"""
        ...
    
    async def update(self, id: Any, obj_in: UpdateSchemaType) -> Optional[T]:
        """更新实体"""
        ...
    
    async def delete(self, id: Any) -> bool:
        """删除实体"""
        ...


@runtime_checkable
class Service(Protocol[T]):
    """服务协议"""
    
    async def get(self, id: Any) -> Optional[T]:
        """获取实体"""
        ...
    
    async def list(self, pagination: PaginationParams) -> PaginationResult[T]:
        """获取实体列表"""
        ...
    
    async def create(self, data: CreateSchemaType) -> T:
        """创建实体"""
        ...
    
    async def update(self, id: Any, data: UpdateSchemaType) -> Optional[T]:
        """更新实体"""
        ...
    
    async def delete(self, id: Any) -> bool:
        """删除实体"""
        ...


@runtime_checkable
class Controller(Protocol):
    """控制器协议"""
    
    async def get(self, id: Any) -> APIResponse[Any]:
        """获取资源"""
        ...
    
    async def list(self, pagination: PaginationParams) -> APIResponse[PaginationResult[Any]]:
        """获取资源列表"""
        ...
    
    async def create(self, data: CreateSchemaType) -> APIResponse[Any]:
        """创建资源"""
        ...
    
    async def update(self, id: Any, data: UpdateSchemaType) -> APIResponse[Any]:
        """更新资源"""
        ...
    
    async def delete(self, id: Any) -> APIResponse[bool]:
        """删除资源"""
        ...


# 函数类型
AsyncHandler = Callable[..., Awaitable[Any]]
SyncHandler = Callable[..., Any]
EventHandler = Callable[[str, Dict[str, Any]], Awaitable[None]]
ErrorHandler = Callable[[Exception], Awaitable[APIResponse[None]]]

# 配置类型
ConfigDict = Dict[str, Any]
EnvironmentConfig = Dict[str, Union[str, int, float, bool]]

# 数据验证类型
ValidationRule = Callable[[Any], bool]
ValidationRules = Dict[str, List[ValidationRule]]

# 缓存类型
CacheKey = str
CacheValue = Any
CacheTTL = int

# 任务调度类型
ScheduleExpression = str
TaskFunction = Callable[[], Awaitable[Any]]

# 文件处理类型
FilePath = str
FileContent = bytes
FileMetadata = Dict[str, Any]

# 数据库查询类型
class QueryFilter(BaseModel):
    """查询过滤器"""
    field: str
    operator: str  # eq, ne, gt, gte, lt, lte, in, like
    value: Any


class QuerySort(BaseModel):
    """查询排序"""
    field: str
    direction: str = "asc"  # asc, desc


class QueryOptions(BaseModel):
    """查询选项"""
    filters: List[QueryFilter] = []
    sorts: List[QuerySort] = []
    pagination: Optional[PaginationParams] = None
    include_deleted: bool = False


# 监控指标类型
class MetricPoint(BaseModel):
    """指标点"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = {}


class HealthStatus(BaseModel):
    """健康状态"""
    service: str
    status: str  # healthy, unhealthy, degraded
    timestamp: datetime
    details: Dict[str, Any] = {}


# 事件类型
class Event(BaseModel):
    """事件模型"""
    id: str
    type: str
    source: str
    data: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[str] = None


# 权限类型
class Permission(BaseModel):
    """权限模型"""
    resource: str
    action: str
    conditions: Dict[str, Any] = {}


class Role(BaseModel):
    """角色模型"""
    name: str
    permissions: List[Permission]
    description: Optional[str] = None


# 导出所有类型
__all__ = [
    # 基础类型
    'T', 'ModelType', 'CreateSchemaType', 'UpdateSchemaType',
    
    # 枚举
    'ResponseStatus', 'TaskStatus', 'LogLevel',
    
    # 数据库类型
    'DatabaseSession', 'DatabaseResult', 'DatabaseRecord', 'DatabaseRecords',
    
    # 请求响应类型
    'RequestData', 'ResponseData', 'QueryParams', 'PathParams',
    
    # 分页类型
    'PaginationParams', 'PaginationResult',
    
    # API响应类型
    'APIResponse', 'TaskResult',
    
    # 协议
    'Repository', 'Service', 'Controller',
    
    # 函数类型
    'AsyncHandler', 'SyncHandler', 'EventHandler', 'ErrorHandler',
    
    # 配置类型
    'ConfigDict', 'EnvironmentConfig',
    
    # 查询类型
    'QueryFilter', 'QuerySort', 'QueryOptions',
    
    # 监控类型
    'MetricPoint', 'HealthStatus',
    
    # 事件类型
    'Event',
    
    # 权限类型
    'Permission', 'Role',
]
