"""
WebSocket工具函数
提供通用的WebSocket端点实现
"""

from fastapi import WebSocket, WebSocketDisconnect
from app.services.common.websocket_manager import websocket_manager
from app.utils.logger import logger


async def generic_websocket_endpoint(websocket: WebSocket, client_id: str, endpoint_name: str = "WebSocket"):
    """
    通用WebSocket端点实现
    
    Args:
        websocket: WebSocket连接对象
        client_id: 客户端ID
        endpoint_name: 端点名称，用于日志记录
    """
    await websocket_manager.connect(websocket, client_id)
    try:
        while True:
            # 保持连接活跃，接收客户端消息
            data = await websocket.receive_text()
            
            # 记录收到的消息
            logger.info(f"{endpoint_name} - 收到客户端 {client_id} 消息: {data}")
            
            # 发送确认消息
            await websocket_manager.send_personal_message(
                f"服务器已收到消息: {data}",
                client_id
            )
            
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket, client_id)
        logger.info(f"{endpoint_name} - 客户端 {client_id} 断开WebSocket连接")
    except Exception as e:
        logger.error(f"{endpoint_name} - WebSocket连接异常: {e}")
        websocket_manager.disconnect(websocket, client_id)
