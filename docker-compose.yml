version: '3.8'

services:
  # 后端服务
  backend:
    build: .
    container_name: sta-keeper-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Oracle ERP数据库配置
      - ERP_ORACLE_HOST=************
      - ERP_ORACLE_PORT=1521
      - ERP_ORACLE_SERVICE=jgdb
      - ERP_ORACLE_USER=ERP_FB01
      - ERP_ORACLE_PASSWORD=erp_fb01

      # Oracle MES数据库配置
      - MES_ORACLE_HOST=************
      - MES_ORACLE_PORT=1521
      - MES_ORACLE_SERVICE=jgdb
      - MES_ORACLE_USER=imes
      - MES_ORACLE_PASSWORD=sa

      # ERP Web系统配置
      - ERP_WEB_USERNAME=J039760
      - ERP_WEB_PASSWORD=2012qwer

      # JWT配置
      - SECRET_KEY=sta-keeper-jwt-secret-key-2024
      - ACCESS_TOKEN_EXPIRE_MINUTES=480
      - ALG<PERSON>ITHM=HS256

      # 应用配置
      - APP_NAME=STA Keeper Backend
    volumes:
      - ./logs:/app/logs

  # 前端服务
  frontend:
    build: ../sta-keeper-frontend
    container_name: sta-keeper-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend
