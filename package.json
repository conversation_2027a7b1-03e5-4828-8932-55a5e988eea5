{"name": "sta-keeper-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --mode dev", "pro": "vite --host --mode pro", "build": "vite build", "build:dev": "vite build --mode dev", "build:pro": "vite build --mode pro", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tailwindcss/postcss": "^4.1.8", "axios": "^1.9.0", "element-plus": "^2.9.11", "pinia": "^2.1.7", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^22.15.29", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}