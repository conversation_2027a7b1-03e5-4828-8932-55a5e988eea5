<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMenu, ElMenuItem, ElSubMenu, ElIcon, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { Setting, DataLine, Connection, Operation, User, SwitchButton, Document } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 计算属性：是否显示主界面
const showMainLayout = computed(() => {
  return router.currentRoute.value.path !== '/login'
})

// 登出处理
const handleLogout = async () => {
  await userStore.logout()
  router.push('/login')
}
</script>

<template>
  <div class="app-container">
    <!-- 主界面布局 -->
    <div v-if="showMainLayout">
      <div class="header">
        <div class="logo">炼钢日常维护 - STA-Keeper</div>
        <div class="user-info">
          <el-dropdown @command="handleLogout">
            <span class="user-dropdown">
              <el-icon><User /></el-icon>
              {{ userStore.nickname }}
              <el-icon class="el-icon--right"><SwitchButton /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="layout">
        <el-menu
          router
          default-active="/steel-making"
          class="side-menu"
        >
        
          <el-menu-item index="/bar-wire">
            <el-icon><Connection /></el-icon>
            <span>棒线管理</span>
          </el-menu-item>
          <el-menu-item index="/rolling">
            <el-icon><Operation /></el-icon>
            <span>轧钢管理</span>
          </el-menu-item>
         <el-menu-item index="/steel-making">
            <el-icon><DataLine /></el-icon>
            <span>炼钢维护</span>
          </el-menu-item>
          <el-sub-menu index="maintenance">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>炼钢维护历史</span>
            </template>
            <el-sub-menu index="erp-maintenance">
              <template #title>
                <span>ERP</span>
              </template>
              <el-menu-item index="/maintenance/data-comparison">物料消耗对比</el-menu-item>
              <el-menu-item index="/maintenance/steel-cost-data-verification">成本数据核对</el-menu-item>
              <el-menu-item index="/maintenance/erp-message-check">电文检查</el-menu-item>
              <el-menu-item index="/maintenance/ix-receiva">IX收账作业检查</el-menu-item>
              <el-menu-item index="/maintenance/ix-batch-check">IX批次检查</el-menu-item>
              <el-menu-item index="/maintenance/auto-schedule">自动排程</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="mes-maintenance">
              <template #title>
                <span>MES</span>
              </template>
              <el-menu-item index="/maintenance/mes-message-check">电文检查</el-menu-item>
              <el-menu-item index="/maintenance/mes-maintenance">维护任务</el-menu-item>
              <el-menu-item index="/maintenance/manual-warehouse">人工入库</el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="other-maintenance">
              <template #title>
                <span>其他功能</span>
              </template>
              <el-menu-item index="/maintenance/scheduler-status">定时任务状态</el-menu-item>
            </el-sub-menu>
          </el-sub-menu>
          <el-sub-menu index="system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/users">用户管理</el-menu-item>
            <el-menu-item index="/system/roles">角色管理</el-menu-item>
            <el-menu-item index="/system/logs">操作日志</el-menu-item>
          </el-sub-menu>
        </el-menu>

        <div class="content">
          <router-view />
        </div>
      </div>
    </div>

    <!-- 登录页面 -->
    <div v-else>
      <router-view />
    </div>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #409EFF;
  color: white;
  padding: 0 16px;
  height: 60px;
}

.logo {
  font-weight: bold;
  font-size: 18px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-dropdown .el-icon {
  margin: 0 4px;
}

.layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.side-menu {
  width: 220px;
  flex-shrink: 0;
  border-right: none;
}

.side-menu :deep(.el-menu-item),
.side-menu :deep(.el-sub-menu__title) {
  height: 40px;
  line-height: 40px;
}

.side-menu :deep(.el-sub-menu .el-menu-item) {
  height: 36px;
  line-height: 36px;
  padding-left: 50px !important;
}

.side-menu :deep(.el-sub-menu .el-sub-menu .el-menu-item) {
  height: 32px;
  line-height: 32px;
  padding-left: 70px !important;
  font-size: 13px;
}

.content {
  flex: 1;
  padding: 8px;
  overflow: auto;
  background-color: #f5f7fa;
}
</style>