import { getWebSocketUrl} from '@/config'
import apiClient from './config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 自动排程请求接口
export interface AutoScheduleRequest {
  // 暂时不需要参数
}

// 任务状态接口
export interface TaskStatus {
  id: number
  task_name: string
  status: string
  progress: number
  error_message?: string
  step_timings?: Record<string, number>
  execution_result?: any[]
  steps?: StepInfo[]
  created_at?: string
  started_at?: string
  completed_at?: string
}

// 步骤信息接口
export interface StepInfo {
  step_name: string
  step_order: number
  status: string
  duration_seconds?: number
  error_message?: string
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: 'task_progress' | 'task_completed'
  task_id: number
  progress?: number
  message?: string
  success?: boolean
  result_summary?: string
  data?: any  // 添加额外数据字段
  timestamp: string
}

export const autoScheduleApi = {
  /**
   * 启动自动排程任务
   */
  async startAutoSchedule(request: AutoScheduleRequest): Promise<ApiResponse<{ task_id: number; message: string }>> {
    return await apiClient.post('/api/auto-schedule/start', request)
  },

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: number): Promise<ApiResponse<TaskStatus>> {
    return await apiClient.get(`/api/auto-schedule/task/${taskId}/status`)
  },

  /**
   * 创建WebSocket连接
   */
  createWebSocket(clientId: string): WebSocket {
    const wsUrl = `${getWebSocketUrl()}/api/auto-schedule/ws/${clientId}`
    console.log('创建自动排程WebSocket连接:', wsUrl)
    return new WebSocket(wsUrl)
  }
}
