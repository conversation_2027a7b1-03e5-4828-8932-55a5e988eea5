import {getWebSocketUrl} from '@/config'
import apiClient from './config'

// 数据对比请求接口
export interface DataComparisonRequest {
  start_date: string  // 格式: YYYY-MM-DD
  end_date: string    // 格式: YYYY-MM-DD
  factory: string     // 一厂 或 二厂
  heat_no?: string    // 可选：炉号
  material_code?: string  // 可选：物料编码
}

// 任务状态响应接口
export interface TaskStatus {
  id: number
  task_name: string
  status: string
  progress: number
  total_records: number
  processed_records: number
  difference_count: number
  error_message?: string
  created_at?: string
  started_at?: string
  completed_at?: string
}





// WebSocket消息接口
export interface WebSocketMessage {
  type: 'task_progress' | 'task_completed'
  task_id: number
  progress?: number
  message?: string
  success?: boolean
  result_summary?: string
  data?: any  // 添加额外数据字段
  timestamp: string
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

export const dataComparisonApi = {
  /**
   * 启动数据对比任务
   */
  async startComparison(request: DataComparisonRequest): Promise<ApiResponse<{ task_id: number; message: string }>> {
    return await apiClient.post('/api/data-comparison/start', request)
  },

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: number): Promise<ApiResponse<TaskStatus>> {
    return await apiClient.get(`/api/data-comparison/task/${taskId}/status`)
  },

  /**
   * 创建WebSocket连接
   */
  createWebSocket(clientId: string): WebSocket {
    const wsUrl = `${getWebSocketUrl()}/api/data-comparison/ws/${clientId}`
    console.log('创建WebSocket连接:', wsUrl)
    return new WebSocket(wsUrl)
  }
}
