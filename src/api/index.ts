// 统一导出所有API服务
export { userApi } from './user'
export { roleApi } from './role'
export { logApi } from './log'
export { dataComparisonApi } from './dataComparison'
export { messageCheckApi } from './messageCheck'
export { autoScheduleApi } from './autoSchedule'
export { initialCastingApi } from './initialCasting'
export { mesMessageCheckApi } from './mesMessageCheck'
export { mesMaintenanceApi } from './mesMaintenance'
export { maintenanceHistoryApi } from './maintenanceHistory'
export { ixReceivaApi } from './ixReceiva'
export { steelCostDataVerificationApi } from './steelCostDataVerification'
export { heatNoSwitchApi } from './heatNoSwitch'
export { ixBatchCheckApi } from './ixBatchCheck'
export { authApi } from './auth'

// 导出通用配置
export { default as apiClient } from './config'

// 导出类型定义
export type {
  User,
  UserCreate,
  UserUpdate,
  PaginationParams,
  PaginationResponse,
  ApiResponse
} from './user'

export type {
  Role,
  RoleCreate,
  RoleUpdate
} from './role'

export type {
  OperationLog
} from './log'

export type {
  DataComparisonRequest,
  TaskStatus,
  ComparisonResult,
  TaskResultsResponse,
  WebSocketMessage,
  ApiResponse
} from './dataComparison'

export type {
  MessageCheckRequest
} from './messageCheck'

export type {
  AutoScheduleRequest
} from './autoSchedule'

export type {
  InitialCastingRequest,
  InitialCastingResponse
} from './initialCasting'

export type {
  MESMessageCheckRequest,
  MESMessageCheckResponse
} from './mesMessageCheck'

export type {
  UpdateProdResSummaryRequest,
  UpdateProdResSummaryResponse
} from './mesMaintenance'

export type {
  LoginRequest,
  LoginResponse,
  UserInfo
} from './auth'

export type {
  IxReceivaCheckRequest,
  IxReceivaCheckResult,
  IxReceivaCheckResponse,
  IxReceivaAutoCheckRequest
} from './ixReceiva'

export type {
  SteelCostDataVerificationRequest
} from './steelCostDataVerification'

export type {
  TaskHistoryQuery,
  TaskHistoryItem,
  TaskDetails
} from './maintenanceHistory'
