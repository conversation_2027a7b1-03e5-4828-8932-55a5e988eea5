import { getWebSocketUrl} from '@/config'
import apiClient from './config'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

// IX收账作业检查请求参数
export interface IxReceivaCheckRequest {
  system_id: string // 'IB' 或 'IS'
  start_date: string // 开始日期 YYYYMMDD
  end_date: string // 结束日期 YYYYMMDD
  status_filter?: string[] // 状态筛选 ['error', 'processing', 'ip_error']
}

// IX收账作业检查结果
export interface IxReceivaCheckResult {
  batch_no: string
  sys_id: string
  start_date: string
  start_time: string
  end_date: string
  end_time: string
  exec_emp_no: string
  exec_result: string
  exec_msg: string
  succeed: number
  error: number
  processing: number
  ip_succeed: number
  ip_error: number
  ip_unprocess: number
  duration_minutes?: number // 耗时分钟数
  status_description: string // 状态描述
}

// IX收账作业检查响应
export interface IxReceivaCheckResponse {
  summary: {
    check_date: string
    system_id: string
    total_count: number
    error_count: number
    processing_count: number
    ip_error_count: number
    long_duration_count: number
  }
  results: IxReceivaCheckResult[]
}

// 自动检查请求参数
export interface IxReceivaAutoCheckRequest {
  min_duration_minutes: number // 最小耗时分钟数，默认3分钟
}

// IX收账作业检查API
export const ixReceivaApi = {
  // 手动检查
  async startCheck(data: IxReceivaCheckRequest): Promise<ApiResponse<{ task_id: number; message: string }>> {
    return await apiClient.post('/api/ix-receiva/check', data)
  },

  // 自动检查（定时任务）
  async startAutoCheck(data: IxReceivaAutoCheckRequest): Promise<ApiResponse<{ task_id: number; message: string }>> {
    return await apiClient.post('/api/ix-receiva/auto-check', data)
  },

  // 获取检查结果
  async getCheckResult(taskId: number): Promise<ApiResponse<IxReceivaCheckResponse>> {
    return await apiClient.get(`/api/ix-receiva/result/${taskId}`)
  },

  // 创建WebSocket连接
  createWebSocket: (clientId: string) => {
    return new WebSocket(`${getWebSocketUrl()}/api/ix-receiva/ws/${clientId}`)
  },

  // 获取历史记录
  async getHistory(params: {
    page?: number
    size?: number
    start_date?: string
    end_date?: string
    system_id?: string
    status?: string
  }): Promise<ApiResponse<{
    total: number
    items: Array<{
      id: number
      task_id: number
      system_id: string
      check_date: string
      total_count: number
      error_count: number
      processing_count: number
      ip_error_count: number
      long_duration_count: number
      status: string // 'success', 'warning', 'error'
      created_time: string
      execution_time: number
    }>
  }>> {
    return await apiClient.get('/api/ix-receiva/history', { params })
  }
}
