import apiClient from './config'
import type { PaginationParams, PaginationResponse, ApiResponse } from './user'

// 操作日志相关接口类型定义
export interface OperationLog {
  id: number
  operation_time: string
  operation_type: string
  operation_content: string
  operator_id: number
  operator_name: string
  duration: number
  ip_address: string
  user_agent: string
  status: string
  error_message?: string
}

// 操作日志API服务
export const logApi = {
  // 获取操作日志列表
  async getLogList(params: PaginationParams): Promise<ApiResponse<PaginationResponse<OperationLog>>> {
    return await apiClient.post('/api/log/', params)
  }
}
