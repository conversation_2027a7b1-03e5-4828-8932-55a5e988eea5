import apiClient from './config'

// 物料信息接口
export interface MaterialInfo {
  MATE_CODE: string
  MATE_NAME: string
}

// 人工入库请求接口
export interface ManualWarehouseRequest {
  materialCode: string
  warehouseDate: string
  weight: number
  factory: string
}

// 人工入库响应接口
export interface ManualWarehouseResponse {
  lesNo: string
  insertSql: string
}

// 参考数据接口
export interface ReferenceData {
  LES_NO: string
  MAT_CODE: string
  WORK_SHOP: string
  SUTTLE: number
  SUTTLE_TIME: string
  [key: string]: any
}

// 历史记录查询参数
export interface WarehouseHistoryQuery {
  materialCode?: string
  factory?: string
  startDate?: string
  endDate?: string
  lesNo?: string
  page?: number
  pageSize?: number
}

// 历史记录项
export interface WarehouseHistoryItem {
  id: number
  lesNo: string
  materialCode: string
  materialName: string
  weight: number
  factory: string
  warehouseDate: string
  createdAt: string
  createdBy: string
  status: string
}

// 历史记录响应
export interface WarehouseHistoryResponse {
  items: WarehouseHistoryItem[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// API响应基础接口
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
}

export const manualWarehouseApi = {
  /**
   * 搜索物料信息
   */
  async searchMaterials(query: string): Promise<ApiResponse<MaterialInfo[]>> {
    return await apiClient.get(`/api/manual-warehouse/materials/search?q=${encodeURIComponent(query)}`)
  },

  /**
   * 获取所有物料信息
   */
  async getAllMaterials(): Promise<ApiResponse<MaterialInfo[]>> {
    return await apiClient.get('/api/manual-warehouse/materials/all')
  },

  /**
   * 获取参考数据
   */
  async getReferenceData(materialCode: string, factory: string): Promise<ApiResponse<ReferenceData>> {
    return await apiClient.get(`/api/manual-warehouse/reference?materialCode=${materialCode}&factory=${factory}`)
  },

  /**
   * 创建入库记录
   */
  async createWarehouseRecord(request: ManualWarehouseRequest): Promise<ApiResponse<ManualWarehouseResponse>> {
    return await apiClient.post('/api/manual-warehouse/create', request)
  },

  /**
   * 获取入库历史记录
   */
  async getWarehouseHistory(params: WarehouseHistoryQuery): Promise<ApiResponse<WarehouseHistoryResponse>> {
    const queryParams = new URLSearchParams()

    if (params.materialCode) queryParams.append('materialCode', params.materialCode)
    if (params.factory) queryParams.append('factory', params.factory)
    if (params.startDate) queryParams.append('startDate', params.startDate)
    if (params.endDate) queryParams.append('endDate', params.endDate)
    if (params.lesNo) queryParams.append('lesNo', params.lesNo)
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString())

    return await apiClient.get(`/api/manual-warehouse/history?${queryParams.toString()}`)
  },

  /**
   * 获取入库记录详情
   */
  async getWarehouseDetail(id: number): Promise<ApiResponse<WarehouseHistoryItem>> {
    return await apiClient.get(`/api/manual-warehouse/detail/${id}`)
  },

  /**
   * 删除入库记录
   */
  async deleteWarehouseRecord(id: number): Promise<ApiResponse<void>> {
    return await apiClient.delete(`/api/manual-warehouse/delete/${id}`)
  }
}

export default manualWarehouseApi
