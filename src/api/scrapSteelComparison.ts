import { getWebSocketUrl } from "@/config";
import apiClient from "./config";
import type { ApiResponse } from "./dataComparison";

/**
 * @file src/api/scrapSteelComparison.ts
 * @description 钢坯废钢对比相关API接口请求定义
 */
export interface ScraptSteelComparisonRequest {
    comparisonStartDate: string;
    comparisonEndDate: string;
    factory: string;
}


export const scraptSteelComparisonApi = {
    async startComparison(request: ScraptSteelComparisonRequest): Promise<ApiResponse<any>> {
        return await apiClient.post('/api/scrap-steel-comparison/start', request)
    },

    createWebSocket(clientId: string): WebSocket {
        const wsUrl = `${getWebSocketUrl()}/api/scrap-steel-comparison/ws/${clientId}`

        console.log('创建WebSocket连接:', wsUrl)
        return new WebSocket(wsUrl)
    }

}