import devConfig from './dev.json'
import prodConfig from './pro.json'

export interface AppConfig {
  API_BASE_URL: string,
  WEB_SOCKET_URL: string,
  WEB_SOCKET_PORT: string,
}

const config: AppConfig = import.meta.env.MODE === 'pro' ? prodConfig : devConfig

export default config

export const getApiBaseUrl = (): string => config.API_BASE_URL
export const getWebSocketUrl = (): string => config.WEB_SOCKET_URL
export const getWebSocketPort = (): string => config.WEB_SOCKET_PORT

export const isProduction = import.meta.env.MODE === 'pro'
export const isDevelopment = import.meta.env.MODE === 'dev'