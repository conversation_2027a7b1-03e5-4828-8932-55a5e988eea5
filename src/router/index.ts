import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import SteelMakingView from '@/views/steel-making/SteelMakingView.vue'
import BarWireView from '@/views/BarWireView.vue'
import RollingView from '@/views/RollingView.vue'
import SystemView from '@/views/system/SystemView.vue'
import UsersView from '@/views/system/UsersView.vue'
import RolesView from '@/views/system/RolesView.vue'
import LogsView from '@/views/system/LogsView.vue'
import LoginView from '@/views/LoginView.vue'
import DataComparisonHistoryView from '@/views/steel-making/maintenance/DataComparisonHistoryView.vue'
import ErpMessageCheckHistoryView from '@/views/steel-making/maintenance/ErpMessageCheckHistoryView.vue'
import IxReceivaHistoryView from '@/views/steel-making/maintenance/IxReceivaHistoryView.vue'
import MesMessageCheckHistoryView from '@/views/steel-making/maintenance/MesMessageCheckHistoryView.vue'
import MesMaintenanceHistoryView from '@/views/steel-making/maintenance/MesMaintenanceHistoryView.vue'
import ManualWarehouseHistoryView from '@/views/steel-making/maintenance/ManualWarehouseHistoryView.vue'
import SchedulerStatusView from '@/views/steel-making/maintenance/SchedulerStatusView.vue'
import AutoScheduleHistoryView from '@/views/steel-making/maintenance/AutoScheduleHistoryView.vue'
import SteelCostDataVerificationHistoryView from '@/views/steel-making/maintenance/SteelCostDataVerificationHistoryView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/steel-making'
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requiresAuth: false }
    },
    {
      path: '/steel-making',
      name: 'steel-making',
      component: SteelMakingView,
      meta: { requiresAuth: true }
    },
    {
      path: '/bar-wire',
      name: 'bar-wire',
      component: BarWireView,
      meta: { requiresAuth: true }
    },
    {
      path: '/rolling',
      name: 'rolling',
      component: RollingView,
      meta: { requiresAuth: true }
    },
    {
      path: '/system',
      name: 'system',
      component: SystemView,
      redirect: '/system/users',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'users',
          name: 'users',
          component: UsersView,
          meta: { requiresAuth: true }
        },
        {
          path: 'roles',
          name: 'roles',
          component: RolesView,
          meta: { requiresAuth: true }
        },
        {
          path: 'logs',
          name: 'logs',
          component: LogsView,
          meta: { requiresAuth: true }
        }
      ]
    },
    {
      path: '/maintenance',
      name: 'maintenance',
      redirect: '/maintenance/data-comparison',
      meta: { requiresAuth: true },
      children: [
        {
          path: 'data-comparison',
          name: 'data-comparison-history',
          component: DataComparisonHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'erp-message-check',
          name: 'erp-message-check-history',
          component: ErpMessageCheckHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'ix-receiva',
          name: 'ix-receiva-history',
          component: IxReceivaHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'mes-message-check',
          name: 'mes-message-check-history',
          component: MesMessageCheckHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'mes-maintenance',
          name: 'mes-maintenance-history',
          component: MesMaintenanceHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'manual-warehouse',
          name: 'manual-warehouse-history',
          component: ManualWarehouseHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'auto-schedule',
          name: 'auto-schedule-history',
          component: AutoScheduleHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'scheduler-status',
          name: 'scheduler-status',
          component: SchedulerStatusView,
          meta: { requiresAuth: true }
        },
        {
          path: 'steel-cost-data-verification',
          name: 'steel-cost-data-verification-history',
          component: SteelCostDataVerificationHistoryView,
          meta: { requiresAuth: true }
        },
        {
          path: 'ix-batch-check',
          name: 'ix-batch-check-history',
          component: () => import('../views/steel-making/maintenance/IxBatchCheckHistoryView.vue'),
          meta: { requiresAuth: true }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 如果路由需要认证
  if (to.meta.requiresAuth !== false) {
    // 检查是否已登录
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 已登录但用户信息为空，尝试获取用户信息
    if (!userStore.userInfo) {
      const success = await userStore.getCurrentUser()
      if (!success) {
        // 获取用户信息失败，跳转到登录页
        next('/login')
        return
      }
    }
  }

  // 如果已登录且访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

export default router