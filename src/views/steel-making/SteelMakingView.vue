<template>
  <div class="steel-making-container">
    <el-tabs v-model="activeTab" type="card" class="main-tabs">
      <el-tab-pane label="ERP维护" name="erp">
        <ErpMaintenanceTab />
      </el-tab-pane>

      <el-tab-pane label="MES维护" name="mes">
        <MesMaintenanceTab />
      </el-tab-pane>
    </el-tabs>

    <!-- 全局进度对话框 -->
    <ProgressDialog />



    <!-- 数据对比结果对话框 -->
    <TaskResultDialog v-model:visible="comparisonResultsDialogVisible" :task-id="currentComparisonTaskId"
      :task-type="'data_comparison'" :result-data="comparisonResultData" title="数据对比结果" />
    <!-- 电文检查结果对话框 -->
    <MessageCheckResultDialog
      v-model="messageCheckResultsDialogVisible"
      :results="messageCheckResults"
    />

    <!-- IX收账作业检查结果对话框 -->
    <IxReceivaResultDialog
      v-model="ixReceivaResultsDialogVisible"
      :results="ixReceivaResults"
    />

    <!-- 炼钢成本数据核对结果对话框 -->
    <TaskResultDialog v-model:visible="steelCostVerificationResultsDialogVisible" :task-id="currentSteelCostTaskId"
      :task-type="'steel_cost_verification'" :result-data="steelCostVerificationResultData" title="炼钢成本数据核对结果" />


    <!-- IX批次检查结果对话框 -->
    <IxBatchCheckResultDialog
      v-model="ixBatchCheckResultsDialogVisible"
      :results="ixBatchCheckResults"
    />

    <ScrapSteelComparisonDialog
      v-model="scrapSteekComparisonDialogVisible"
      :task-id="scrapSteekComparisonTaskId"
      :result-data="scrapSteekComparisonResultData"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, provide } from 'vue'
import ErpMaintenanceTab from './erp/ErpMaintenanceTab.vue'
import MesMaintenanceTab from './mes/MesMaintenanceTab.vue'
import ProgressDialog from './components/ProgressDialog.vue'
import TaskResultDialog from './components/TaskResultDialog.vue'
import MessageCheckResultDialog from './components/MessageCheckResultDialog.vue'
import IxReceivaResultDialog from './components/IxReceivaResultDialog.vue'
import IxBatchCheckResultDialog from './components/IxBatchCheckResultDialog.vue'

const activeTab = ref('erp')


// 数据对比结果对话框状态
const comparisonResultsDialogVisible = ref(false)
const currentComparisonTaskId = ref(0)
const comparisonResultData = ref<any>(null)

// 电文检查结果对话框状态
const messageCheckResultsDialogVisible = ref(false)
const messageCheckResults = ref<any>(null)

// IX收账作业检查结果对话框状态
const ixReceivaResultsDialogVisible = ref(false)
const ixReceivaResults = ref<any>(null)

// 炼钢成本数据核对结果对话框状态
const steelCostVerificationResultsDialogVisible = ref(false)
const currentSteelCostTaskId = ref(0)
const steelCostVerificationResultData = ref<any>(null)

// 废钢分类投料数据对比状态
const scrapSteekComparisonDialogVisible = ref(false)
const scrapSteekComparisonTaskId = ref(0)
const scrapSteekComparisonResultData = ref<any>(null)



// 显示数据对比结果
const showComparisonResults = (data: any) => {
  console.log('SteelMakingView: 显示数据对比结果', data)
  console.log('SteelMakingView: 接收到的taskId:', data.task_id)

  // 设置任务ID（如果有的话）
  currentComparisonTaskId.value = data.task_id || 0
  console.log('SteelMakingView: 设置的currentComparisonTaskId:', currentComparisonTaskId.value)

  // 设置结果数据
  comparisonResultData.value = data

  // 显示对话框
  comparisonResultsDialogVisible.value = true
}

// 显示电文检查结果
const showMessageCheckResults = (data: any) => {
  messageCheckResults.value = data
  messageCheckResultsDialogVisible.value = true
}

// 显示IX收账作业检查结果
const showIxReceivaResults = (data: any) => {
  ixReceivaResults.value = data
  ixReceivaResultsDialogVisible.value = true
}


// 显示炼钢成本数据核对结果
const showVerificationResults = (data: any) => {
  console.log('SteelMakingView: 显示炼钢成本数据核对结果', data)
  console.log('SteelMakingView: 接收到的taskId:', data.task_id)

  // 设置任务ID（如果有的话）
  currentSteelCostTaskId.value = data.task_id || 0
  console.log('SteelMakingView: 设置的currentSteelCostTaskId:', currentSteelCostTaskId.value)

  // 设置结果数据
  steelCostVerificationResultData.value = data

  // 显示对话框
  steelCostVerificationResultsDialogVisible.value = true
}

// IX批次检查结果对话框状态
const ixBatchCheckResultsDialogVisible = ref(false)
const ixBatchCheckResults = ref<any>(null)

// 显示IX批次检查结果
const showIxBatchCheckResults = (data: any) => {
  ixBatchCheckResults.value = data
  ixBatchCheckResultsDialogVisible.value = true
}


// 显示废钢投料对比解决
const showScrapSteekComparisonResults = (data: any) =>{
  scrapSteekComparisonDialogVisible.value = true
  scrapSteekComparisonTaskId.value = data.task_id || 0
  scrapSteekComparisonResultData.value = data
}

// 提供给子组件使用
provide('showComparisonResults', showComparisonResults)
provide('showMessageCheckResults', showMessageCheckResults)
provide('showIxReceivaResults', showIxReceivaResults)
provide('showVerificationResults', showVerificationResults)
provide('showIxBatchCheckResults', showIxBatchCheckResults)
provide('showScrapSteekComparisonResults', showScrapSteekComparisonResults)

</script>

<style scoped>
.steel-making-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.header {
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 14px;
  color: #909399;
}

.main-tabs {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

</style>
