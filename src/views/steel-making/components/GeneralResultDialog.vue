<template>
  <el-dialog v-model="visible" :title="title" width="70%" :close-on-click-modal="false" append-to-body>
    <div v-if="content">
      <div v-html="content"></div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: boolean
  title: string
  content: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = ref(props.modelValue)
const title = ref(props.title)
const content = ref(props.content)

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.title, (newVal) => {
  title.value = newVal
})

watch(() => props.content, (newVal) => {
  content.value = newVal
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>