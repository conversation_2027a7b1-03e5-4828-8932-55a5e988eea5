<template>
  <el-dialog v-model="visible" title="IX批次检查结果" width="80%" :close-on-click-modal="false" append-to-body>
    <div v-if="ixBatchCheckResults">
      <!-- 检查摘要 -->
      <div class="results-header">
        <el-alert :title="`IX批次检查完成`"
          :description="`检查时间: ${ixBatchCheckResults.summary?.check_time || '未知'} | 错误记录数量: ${ixBatchCheckResults.summary?.error_count || 0} | 系统: ${ixBatchCheckResults.summary?.system || 'IX'}`"
          :type="(ixBatchCheckResults.summary?.error_count || 0) > 0 ? 'warning' : 'success'" show-icon
          :closable="false" />
      </div>

      <!-- 错误记录详情 -->
      <div v-if="ixBatchCheckResults.error_records && ixBatchCheckResults.error_records.length > 0"
        style="margin-top: 20px;">
        <h3>错误记录详情</h3>
        <el-table :data="ixBatchCheckResults.error_records" style="width: 100%" max-height="400" border>
          <el-table-column prop="batchno" label="批次号" min-width="150" />
          <el-table-column prop="sysid" label="系统ID" min-width="100" />
          <el-table-column prop="startdate" label="开始日期" min-width="120" />
          <el-table-column prop="starttime" label="开始时间" min-width="120" />
          <el-table-column prop="execempno" label="执行员工号" min-width="120" />
          <el-table-column prop="execresult" label="执行结果" min-width="100">
            <template #default="scope">
              <el-tag type="danger">{{ scope.row.execresult }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="execmsg" label="错误信息" min-width="200" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 无错误提示 -->
      <div v-else style="margin-top: 20px; text-align: center; padding: 40px;">
        <el-result icon="success" title="检查完成" sub-title="未发现错误记录">
        </el-result>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: boolean
  results: any
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = ref(props.modelValue)
const ixBatchCheckResults = ref(props.results)

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.results, (newVal) => {
  ixBatchCheckResults.value = newVal
})
</script>

<style scoped>
.results-header {
  margin-bottom: 20px;
}

h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>