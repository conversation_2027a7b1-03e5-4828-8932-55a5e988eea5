<template>
  <el-dialog v-model="visible" title="IX收账作业检查结果" width="90%" :close-on-click-modal="false" append-to-body>
    <div class="ix-receiva-result" v-if="ixReceivaResults">
      <!-- 检查摘要 -->
      <div class="results-header">
        <el-alert :title="`${ixReceivaResults.summary?.system_id || 'IX'}收账作业检查完成`"
          :description="`检查日期: ${ixReceivaResults.summary?.check_date || '未知'} | 系统: ${ixReceivaResults.summary?.system_id || '未知'} | 异常记录数量: ${(ixReceivaResults.summary?.error_count || 0) + (ixReceivaResults.summary?.processing_count || 0) + (ixReceivaResults.summary?.ip_error_count || 0)} | 总检查数量: ${ixReceivaResults.summary?.total_count || 0}`"
          :type="((ixReceivaResults.summary?.error_count || 0) + (ixReceivaResults.summary?.processing_count || 0) + (ixReceivaResults.summary?.ip_error_count || 0)) > 0 ? 'warning' : 'success'"
          show-icon :closable="false" />
      </div>

      <!-- 统计摘要 -->
      <div class="results-summary" style="margin-top: 20px;">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card>
              <template #header>
                <span>收账错误</span>
              </template>
              <div class="summary-stats">
                <p style="color: #F56C6C; font-size: 24px; font-weight: bold;">{{
                  ixReceivaResults.summary?.error_count ||
                  0 }}</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <template #header>
                <span>收账处理中</span>
              </template>
              <div class="summary-stats">
                <p style="color: #E6A23C; font-size: 24px; font-weight: bold;">{{
                  ixReceivaResults.summary?.processing_count || 0 }}</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <template #header>
                <span>抛IP错误</span>
              </template>
              <div class="summary-stats">
                <p style="color: #F56C6C; font-size: 24px; font-weight: bold;">{{
                  ixReceivaResults.summary?.ip_error_count
                  || 0 }}</p>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <template #header>
                <span>耗时过长</span>
              </template>
              <div class="summary-stats">
                <p style="color: #F56C6C; font-size: 24px; font-weight: bold;">{{
                  ixReceivaResults.summary?.long_duration_count || 0 }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 异常记录详情 -->
      <div v-if="ixReceivaResults.results && ixReceivaResults.results.length > 0" style="margin-top: 20px;">
        <h3>异常记录详情</h3>
        <el-table :data="ixReceivaResults.results" style="width: 100%" max-height="400">
          <el-table-column prop="batch_no" label="批次号" min-width="120" />
          <el-table-column prop="sys_id" label="系统" min-width="80" />
          <el-table-column prop="start_date" label="开始日期" min-width="100" />
          <el-table-column prop="start_time" label="开始时间" min-width="100" />
          <el-table-column prop="end_date" label="结束日期" min-width="100" />
          <el-table-column prop="end_time" label="结束时间" min-width="100" />
          <el-table-column prop="duration_minutes" label="耗时(分钟)" min-width="100" align="right">
            <template #default="scope">
              <span :style="{ color: (scope.row.duration_minutes || 0) > 3 ? '#F56C6C' : '#67C23A' }">
                {{ scope.row.duration_minutes || 0 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="error" label="收账错误" min-width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.error > 0" type="danger">{{ scope.row.error }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="processing" label="收账处理中" min-width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.processing > 0" type="warning">{{ scope.row.processing }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="ip_error" label="抛IP错误" min-width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.ip_error > 0" type="danger">{{ scope.row.ip_error }}</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status_description" label="状态描述" min-width="200" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 无异常提示 -->
      <div v-else style="margin-top: 20px; text-align: center; padding: 40px;">
        <el-result icon="success" title="检查完成" sub-title="未发现异常的收账作业">
        </el-result>
      </div>

      <!-- 解决办法 -->
      <div v-if="hasIxReceivaErrors" style="margin-top: 30px;">
        <el-divider content-position="left">
          <h3 style="color: #E6A23C;">
            <el-icon>
              <Tools />
            </el-icon>
            解决办法
          </h3>
        </el-divider>

        <el-alert title="发现异常记录，请根据以下SQL语句进行修复" type="warning" :closable="false" show-icon
          style="margin-bottom: 20px;" />

        <div class="solution-section">
          <div v-if="(ixReceivaResults.summary?.error_count || 0) > 0" class="solution-item">
            <h4 style="color: #F56C6C; margin-bottom: 10px;">
              <el-icon>
                <Warning />
              </el-icon>
              收账错误 ({{ ixReceivaResults.summary?.error_count || 0 }} 条)
            </h4>
            <el-card class="sql-card">
              <div class="sql-content">
                <code>update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(E)%';</code>
              </div>
              <el-button size="small" type="primary" @click="copySqlToClipboard(`update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(E)%';`)"
                style="margin-top: 10px;">
                <el-icon>
                  <CopyDocument />
                </el-icon>
                复制SQL
              </el-button>
            </el-card>
          </div>

          <div v-if="(ixReceivaResults.summary?.processing_count || 0) > 0" class="solution-item">
            <h4 style="color: #E6A23C; margin-bottom: 10px;">
              <el-icon>
                <Loading />
              </el-icon>
              收账处理中 ({{ ixReceivaResults.summary?.processing_count || 0 }} 条)
            </h4>
            <el-card class="sql-card">
              <div class="sql-content">
                <code>update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(P)%';</code>
              </div>
              <el-button size="small" type="primary"
                @click="copySqlToClipboard(`update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(P)%';`)"
                style="margin-top: 10px;">
                <el-icon>
                  <CopyDocument />
                </el-icon>
                复制SQL
              </el-button>
            </el-card>
          </div>

          <div v-if="(ixReceivaResults.summary?.ip_error_count || 0) > 0" class="solution-item">
            <h4 style="color: #F56C6C; margin-bottom: 10px;">
              <el-icon>
                <Connection />
              </el-icon>
              抛送IP错误 ({{ ixReceivaResults.summary?.ip_error_count || 0 }} 条)
            </h4>
            <el-card class="sql-card">
              <div class="sql-content">
                <code>update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(XE)%';</code>
              </div>
              <el-button size="small" type="primary"
                @click="copySqlToClipboard(`update db.TBIBTRANSLOGD set batchNo='' where batchNo like '(XE)%';`)"
                style="margin-top: 10px;">
                <el-icon>
                  <CopyDocument />
                </el-icon>
                复制SQL
              </el-button>
            </el-card>
          </div>
        </div>

        <el-alert title="注意：执行SQL前请确认数据库连接正确，建议先备份相关数据" type="info" :closable="false" show-icon
          style="margin-top: 20px;" />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Tools, Warning, Loading, Connection, CopyDocument } from '@element-plus/icons-vue'

const props = defineProps<{
  modelValue: boolean
  results: any
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = ref(props.modelValue)
const ixReceivaResults = ref(props.results)

// 计算是否有IX收账错误
const hasIxReceivaErrors = computed(() => {
  if (!ixReceivaResults.value?.summary) return false
  const summary = ixReceivaResults.value.summary
  return (summary.error_count || 0) > 0 ||
    (summary.processing_count || 0) > 0 ||
    (summary.ip_error_count || 0) > 0
})

// 复制SQL到剪贴板
const copySqlToClipboard = async (sql: string) => {
  try {
    await navigator.clipboard.writeText(sql)
    ElMessage.success('SQL语句已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.results, (newVal) => {
  ixReceivaResults.value = newVal
})
</script>

<style scoped>
.ix-receiva-result {
  max-height: 70vh;
  overflow-y: auto;
}

.results-header {
  margin-bottom: 20px;
}

.summary-stats p {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 解决办法样式 */
.solution-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.solution-item {
  margin-bottom: 20px;
}

.solution-item h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
}

.sql-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.sql-content {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  margin-bottom: 10px;
}

.sql-content code {
  background: none;
  color: inherit;
  padding: 0;
  font-size: inherit;
}
</style>