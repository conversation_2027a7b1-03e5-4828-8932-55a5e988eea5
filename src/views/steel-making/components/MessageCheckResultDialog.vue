<template>
  <el-dialog v-model="visible" title="电文检查结果" width="90%" :close-on-click-modal="false" append-to-body>
    <div class="message-check-result" v-if="messageCheckResults">
      <!-- 检查摘要 -->
      <div class="results-header">
        <el-alert :title="`${messageCheckResults.summary?.system || 'ERP'}电文检查完成`"
          :description="`检查日期: ${messageCheckResults.summary?.check_date || '未知'} | 错误电文数量: ${messageCheckResults.summary?.error_count || 0} | 总检查数量: ${messageCheckResults.summary?.total_count || 0}`"
          :type="(messageCheckResults.summary?.error_count || 0) > 0 ? 'warning' : 'success'" show-icon :closable="false" />
      </div>

      <!-- 错误电文详情 -->
      <div v-if="messageCheckResults.error_messages && messageCheckResults.error_messages.length > 0"
        style="margin-top: 20px;">
        <h3>错误电文详情</h3>
        <el-table :data="messageCheckResults.error_messages" style="width: 100%" max-height="400">
          <el-table-column prop="message_id" label="电文ID" min-width="150" />
          <el-table-column prop="error_type" label="错误类型" min-width="120" />
          <el-table-column prop="error_description" label="错误描述" min-width="200" />
          <el-table-column prop="created_time" label="发生时间" min-width="180" />
          <el-table-column prop="message_content" label="电文内容" min-width="300" show-overflow-tooltip />
        </el-table>
      </div>

      <!-- 无错误提示 -->
      <div v-else style="margin-top: 20px; text-align: center; padding: 40px;">
        <el-result icon="success" title="检查完成" sub-title="未发现错误电文">
        </el-result>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: boolean
  results: any
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const visible = ref(props.modelValue)
const messageCheckResults = ref(props.results)

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.results, (newVal) => {
  messageCheckResults.value = newVal
})
</script>

<style scoped>
.message-check-result {
  max-height: 70vh;
  overflow-y: auto;
}

.message-check-result h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.results-header {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
