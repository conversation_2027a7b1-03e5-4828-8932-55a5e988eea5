<template>
  <el-dialog
    v-model="progressDialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div class="progress-content">
      <el-progress
        :percentage="taskProgress"
        :status="taskStatus === 'failed' ? 'exception' : (taskProgress === 100 ? 'success' : undefined)"
        :stroke-width="18"
      />
      <div class="progress-message">{{ progressMessage }}</div>
      <div v-if="taskStatus === 'completed'" class="result-summary">
        <el-alert
          title="任务完成"
          :description="resultSummary"
          type="success"
          show-icon
          :closable="false"
        />
      </div>
      <div v-if="taskStatus === 'failed'" class="error-summary">
        <el-alert
          title="执行失败"
          :description="errorMessage"
          type="error"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" :disabled="isTaskRunning">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useProgressDialog } from '../composables/useProgressDialog'

const {
  progressDialogVisible,
  taskProgress,
  progressMessage,
  taskStatus,
  resultSummary,
  errorMessage,
  dialogTitle,
  closeProgressDialog
} = useProgressDialog()

// 计算是否任务正在运行
const isTaskRunning = computed(() => {
  return taskStatus.value === 'running' && taskProgress.value < 100
})

// 处理关闭对话框
const handleClose = () => {
  if (isTaskRunning.value) {
    ElMessageBox.confirm(
      '任务正在执行中，确定要关闭吗？',
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      closeProgressDialog()
    }).catch(() => {
      // 用户取消
    })
  } else {
    closeProgressDialog()
  }
}
</script>

<style scoped>
.progress-content {
  text-align: center;
  padding: 20px 0;
}

.progress-message {
  margin-top: 16px;
  font-size: 14px;
  color: #606266;
}

.result-summary,
.error-summary {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
