<template>
  <!-- 嵌入模式：直接显示内容 -->
  <div v-if="embedded" class="embedded-container">
    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-form :model="filters" inline>
        <el-form-item label="炉号">
          <el-input v-model="filters.heat_no" placeholder="请输入炉号" clearable @change="handleFilterChange" />
        </el-form-item>
        <el-form-item label="物料编码">
          <el-input v-model="filters.material_code" placeholder="请输入物料编码" clearable @change="handleFilterChange" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="请选择状态" clearable @change="handleFilterChange">
            <el-option label="数据不一致" value="数据不一致" />
            <el-option label="OJ无数据" value="OJ无数据" />
            <el-option label="MR无数据" value="MR无数据" />
            <el-option label="IP无数据" value="IP无数据" />
            <el-option label="未发送" value="未发送" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="refreshData">刷新</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      border
      stripe
      v-loading="loading"
      max-height="400"
      @sort-change="handleSortChange"
    >
      <!-- 炼钢成本数据核对列 -->
      <template v-if="taskType === 'steel_cost_verification'">
        <el-table-column prop="heat_no" label="炉号" width="120" sortable="custom" />
        <el-table-column prop="material_code" label="物料编码" width="120" sortable="custom" />
        <el-table-column prop="oj_value" label="OJ重量(kg)" width="120" sortable="custom">
          <template #default="scope">
            {{ formatNumber(scope.row.oj_value) }}
          </template>
        </el-table-column>
        <el-table-column prop="mr_value" label="MR重量(kg)" width="120" sortable="custom">
          <template #default="scope">
            {{ formatNumber(scope.row.mr_value) }}
          </template>
        </el-table-column>
        <el-table-column prop="ip_value" label="IP重量(kg)" width="120" sortable="custom">
          <template #default="scope">
            {{ formatNumber(scope.row.ip_value) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
      </template>

      <!-- 数据对比列 -->
      <template v-else-if="taskType === 'data_comparison'">
        <el-table-column prop="heat_no" label="炉号" width="120" sortable="custom" />
        <el-table-column prop="material_code" label="物料编码" width="120" sortable="custom" />
        <el-table-column prop="mes_value" label="MES重量(kg)" width="120" sortable="custom">
          <template #default="scope">
            {{ formatNumber(scope.row.mes_value) }}
          </template>
        </el-table-column>
        <el-table-column prop="erp_value" label="ERP重量(kg)" width="120" sortable="custom">
          <template #default="scope">
            {{ formatNumber(scope.row.erp_value) }}
          </template>
        </el-table-column>
        <el-table-column prop="difference" label="差异(kg)" width="120" sortable="custom">
          <template #default="scope">
            <span :class="getDifferenceClass(scope.row.difference)">
              {{ formatNumber(scope.row.difference, true) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="difference_rate" label="差异率(%)" width="120" sortable="custom">
          <template #default="scope">
            <span :class="getDifferenceClass(scope.row.difference_rate)">
              {{ formatNumber(scope.row.difference_rate, true) }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 无数据提示 -->
    <div v-if="!loading && tableData.length === 0" class="no-data">
      <el-empty>
        <template #description>
          <div v-if="taskType === 'data_comparison'">
            <p>暂无差异数据</p>
            <p style="color: #909399; font-size: 12px; margin-top: 8px;">
              系统只显示MES与ERP数据不一致或未发送的记录<br>
              如果没有数据，说明所有数据都是一致的
            </p>
          </div>
          <div v-else-if="taskType === 'steel_cost_verification'">
            <p>暂无差异数据</p>
            <p style="color: #909399; font-size: 12px; margin-top: 8px;">
              系统只显示OJ/MR/IP数据不一致的记录<br>
              如果没有数据，说明所有数据都是一致的
            </p>
          </div>
          <div v-else>
            <p>暂无数据</p>
          </div>
        </template>
      </el-empty>
    </div>
  </div>

  <!-- 对话框模式：完整的对话框 -->
  <el-dialog
    v-else
    :model-value="visible"
    :title="dialogTitle"
    width="95%"
    :close-on-click-modal="false"
    append-to-body
    @update:model-value="handleClose"
  >

    <!-- 对话框模式的内容与嵌入模式相同，这里省略 -->
    <div class="result-container">
      <!-- 内容与嵌入模式相同 -->
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportResults">导出结果</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import apiClient from '../../../api/config'

interface Props {
  visible: boolean
  taskId: number
  taskType: string
  resultData?: any
  title?: string
  embedded?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '任务结果详情'
})

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const tableData = ref<any[]>([])
const filters = ref({
  heat_no: '',
  material_code: '',
  status: '',
  min_difference: null as number | null,
  max_difference: null as number | null
})

const pagination = ref({
  page: 1,
  pageSize: 20,
  total: 0
})

// 计算对话框标题
const dialogTitle = computed(() => {
  return `${props.taskType} - ${props.title} - 详细结果`
})

// 获取API端点
const getApiEndpoint = () => {
  switch (props.taskType) {
    case 'steel_cost_verification':
      return 'steel-cost-verification'
    case 'data_comparison':
      return 'data-comparison'
    case 'message_check':
      return 'message-check'
    case 'ix_receiva':
      return 'ix-receiva'
    default:
      return 'steel-cost-verification'
  }
}

// 加载数据
const loadData = async () => {
  console.log('TaskResultDetailsPaginated: 开始加载数据')
  console.log('TaskResultDetailsPaginated: props.taskId:', props.taskId)
  console.log('TaskResultDetailsPaginated: props.taskType:', props.taskType)
  console.log('TaskResultDetailsPaginated: props.embedded:', props.embedded)

  if (!props.taskId || props.taskId === 0) {
    console.warn('TaskResultDetailsPaginated: taskId为空或为0:', props.taskId)
    ElMessage.warning('任务ID无效，无法加载详细数据')
    return
  }

  loading.value = true
  try {
    const endpoint = getApiEndpoint()
    const params: any = {
      page: pagination.value.page,
      page_size: pagination.value.pageSize,
      ...filters.value
    }

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const url = `/api/task-result-details/${endpoint}/${props.taskId}`
    console.log('TaskResultDetailsPaginated: 请求URL:', url)
    console.log('TaskResultDetailsPaginated: 请求参数:', params)
    console.log('TaskResultDetailsPaginated: taskId:', props.taskId, 'taskType:', props.taskType)

    const response: any = await apiClient.get(url, { params })

    console.log('TaskResultDetailsPaginated: 响应数据:', response)

    // 适配后端统一响应格式：{ code: 200, msg: "操作成功", data: PaginationResponse }
    // apiClient响应拦截器已经返回response.data，所以这里response就是{ code, msg, data }
    if (response && response.code === 200 && response.data) {
      const paginationData = response.data
      tableData.value = paginationData.data || []
      pagination.value.total = paginationData.total || 0
      pagination.value.page = paginationData.page || 1
      pagination.value.pageSize = paginationData.page_size || 20

      console.log('TaskResultDetailsPaginated: 设置表格数据:', tableData.value.length, '条记录')
      console.log('TaskResultDetailsPaginated: 总记录数:', pagination.value.total)
    } else {
      console.warn('TaskResultDetailsPaginated: 响应数据格式异常:', response)
      tableData.value = []
      pagination.value.total = 0
    }
  } catch (error: any) {
    console.error('TaskResultDetailsPaginated: 加载详细数据失败:', error)
    console.error('TaskResultDetailsPaginated: 错误详情:', error.response?.data)

    // 更详细的错误处理
    if (error.response?.status === 404) {
      ElMessage.error('任务不存在或没有详细数据')
    } else if (error.response?.status === 500) {
      ElMessage.error('服务器内部错误，请稍后重试')
    } else {
      ElMessage.error(`加载详细数据失败: ${error.response?.data?.detail || error.message}`)
    }

    tableData.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 监听visible变化，加载数据
watch(() => props.visible, (newVal) => {
  if (newVal && props.taskId) {
    loadData()
  }
})

// 嵌入模式下，监听taskId变化自动加载数据
watch(() => props.taskId, (newVal) => {
  if (props.embedded && newVal) {
    loadData()
  }
}, { immediate: true })

// 处理筛选变化
const handleFilterChange = () => {
  pagination.value.page = 1
  loadData()
}

// 重置筛选
const resetFilters = () => {
  filters.value = {
    heat_no: '',
    material_code: '',
    status: '',
    min_difference: null,
    max_difference: null
  }
  pagination.value.page = 1
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  loadData()
}

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  // TODO: 实现排序功能
  console.log('排序:', prop, order)
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 数字格式化
const formatNumber = (value: any, showSign = false) => {
  if (value === null || value === undefined) return '-'
  const num = parseFloat(value)
  if (isNaN(num)) return '-'
  const formatted = num.toFixed(2)
  return showSign && num > 0 ? `+${formatted}` : formatted
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case '数据不一致':
      return 'warning'
    case 'OJ无数据':
    case 'MR无数据':
    case 'IP无数据':
    case '未发送':
      return 'danger'
    case '一致':
      return 'success'
    default:
      return 'info'
  }
}

// 获取差异值样式类
const getDifferenceClass = (value: any) => {
  if (value === null || value === undefined) return ''
  const num = parseFloat(value)
  if (isNaN(num) || num === 0) return ''
  return num > 0 ? 'positive-difference' : 'negative-difference'
}

// 导出结果
const exportResults = () => {
  try {
    const dataStr = JSON.stringify({
      summary: props.resultData?.summary,
      details: tableData.value,
      filters: filters.value,
      total: pagination.value.total
    }, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${props.taskType}_详细结果_${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    URL.revokeObjectURL(url)
    ElMessage.success('结果导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}
</script>

<style scoped>
.embedded-container {
  width: 100%;
}

.filter-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.no-data {
  text-align: center;
  padding: 40px;
}

.result-container {
  max-height: 80vh;
  overflow-y: auto;
}

.summary-card,
.filter-card,
.table-card,
.no-data-card {
  margin-bottom: 20px;
}

.summary-card:last-child,
.filter-card:last-child,
.table-card:last-child,
.no-data-card:last-child {
  margin-bottom: 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-header h3 {
  margin: 0;
  color: #303133;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.positive-difference {
  color: #f56c6c;
}

.negative-difference {
  color: #67c23a;
}
</style>
