<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="90%"
    :close-on-click-modal="false"
    append-to-body
    @update:model-value="handleClose"
  >
    <div v-if="resultData" class="result-container">
      <!-- 炼钢成本数据核对结果 -->
      <div v-if="taskType === 'steel_cost_verification'" class="verification-result">
        <!-- 1. 摘要 -->
        <el-card class="summary-card" shadow="never">
          <template #header>
            <h3>摘要</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="核对日期">{{ resultData.summary?.check_date }}</el-descriptions-item>
            <el-descriptions-item label="厂别">{{ resultData.summary?.factory }}</el-descriptions-item>
            <el-descriptions-item label="炉号">{{ resultData.summary?.heat_no || '全部' }}</el-descriptions-item>
            <el-descriptions-item label="物料编码">{{ resultData.summary?.material_code || '全部' }}</el-descriptions-item>
            <el-descriptions-item label="总记录数">{{ resultData.summary?.total_records || 0 }}</el-descriptions-item>
            <el-descriptions-item label="差异记录数">{{ resultData.summary?.difference_count || 0 }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 2. 统计 -->
        <el-card class="stats-card" shadow="never">
          <template #header>
            <h3>统计</h3>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-title">OJ数据</div>
                <div class="stat-value">{{ resultData.summary?.oj_count || 0 }} 条</div>
                <div class="stat-weight">{{ formatWeight(resultData.summary?.total_oj_weight) }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-title">MR数据</div>
                <div class="stat-value">{{ resultData.summary?.mr_count || 0 }} 条</div>
                <div class="stat-weight">{{ formatWeight(resultData.summary?.total_mr_weight) }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-title">IP数据</div>
                <div class="stat-value">{{ resultData.summary?.ip_count || 0 }} 条</div>
                <div class="stat-weight">{{ formatWeight(resultData.summary?.total_ip_weight) }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 3. 表格 -->
        <el-card class="table-card" shadow="never">
          <template #header>
            <h3>详细表格</h3>
          </template>
          <TaskResultDetailsPaginated
            :visible="true"
            :task-id="taskId"
            :task-type="taskType"
            :result-data="resultData"
            :embedded="true"
          />
        </el-card>
      </div>

      <!-- 数据对比结果 -->
      <div v-else-if="taskType === 'data_comparison'" class="comparison-result">
        <!-- 1. 摘要 -->
        <el-card class="summary-card" shadow="never">
          <template #header>
            <h3>摘要</h3>
          </template>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="对比日期">{{ resultData.summary?.comparison_date }}</el-descriptions-item>
            <el-descriptions-item label="厂别">{{ resultData.summary?.factory }}</el-descriptions-item>
            <el-descriptions-item label="对比模式">{{ resultData.summary?.comparison_mode }}</el-descriptions-item>
            <el-descriptions-item label="总记录数">{{ resultData.summary?.total_records || 0 }}</el-descriptions-item>
            <el-descriptions-item label="差异记录数">{{ resultData.summary?.difference_count || 0 }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 2. 统计 -->
        <el-card class="stats-card" shadow="never">
          <template #header>
            <h3>统计</h3>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="stat-item">
                <div class="stat-title">MES数据（主数据源）</div>
                <div class="stat-details">
                  <p>记录数: {{ resultData.statistics?.mes_stats?.record_count || 0 }}</p>
                  <p>炉号数量: {{ resultData.statistics?.mes_stats?.heat_count || 0 }}</p>
                  <p>物料数量: {{ resultData.statistics?.mes_stats?.material_count || 0 }}</p>
                  <p>总重量: {{ formatWeight(resultData.statistics?.mes_stats?.total_weight) }}</p>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="stat-item">
                <div class="stat-title">ERP数据</div>
                <div class="stat-details">
                  <p>记录数: {{ resultData.statistics?.erp_stats?.record_count || 0 }}</p>
                  <p>炉号数量: {{ resultData.statistics?.erp_stats?.heat_count || 0 }}</p>
                  <p>物料数量: {{ resultData.statistics?.erp_stats?.material_count || 0 }}</p>
                  <p>总重量: {{ formatWeight(resultData.statistics?.erp_stats?.total_weight) }}</p>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 3. 表格 -->
        <el-card class="table-card" shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <h3>差异明细表格</h3>
              <el-tag v-if="taskType === 'data_comparison'" type="info" size="small">
                只显示不一致或未发送的记录
              </el-tag>
            </div>
          </template>
          <TaskResultDetailsPaginated
            :visible="true"
            :task-id="taskId"
            :task-type="taskType"
            :result-data="resultData"
            :embedded="true"
          />
        </el-card>
      </div>

      <!-- 其他类型的结果 -->
      <div v-else class="generic-result">
        <el-card shadow="never">
          <template #header>
            <h3>执行结果</h3>
          </template>
          <pre class="result-json">{{ JSON.stringify(resultData, null, 2) }}</pre>
        </el-card>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportResults">导出结果</el-button>
      </span>
    </template>
  </el-dialog>


</template>

<script setup lang="ts">
import { computed, ref, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View } from '@element-plus/icons-vue'

// 异步加载分页组件
const TaskResultDetailsPaginated = defineAsyncComponent(() => import('./TaskResultDetailsPaginated.vue'))

interface Props {
  visible: boolean
  taskId: number
  taskType: string
  resultData: any
  title?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '任务结果详情'
})

const emit = defineEmits<Emits>()

// 响应式数据
// detailsDialogVisible 不再需要，因为表格直接嵌入在对话框中

// 计算对话框标题
const dialogTitle = computed(() => {
  return `${props.taskType} - ${props.title}`
})

// 格式化重量
const formatWeight = (weight: number | undefined) => {
  if (weight === undefined || weight === null) return '0.00kg'
  return `${weight.toFixed(2)}kg`
}

// 格式化数字
const formatNumber = (value: number | undefined, showSign = false) => {
  if (value === undefined || value === null) return '0.00'
  const formatted = value.toFixed(2)
  if (showSign && value > 0) {
    return `+${formatted}`
  }
  return formatted
}

// 获取总重量差异
const getTotalWeightDifference = () => {
  if (!props.resultData?.differences) return 0
  return props.resultData.differences.reduce((sum: number, item: any) => {
    const ojMrDiff = Math.abs(item.oj_mr_diff || 0)
    const ojIpDiff = Math.abs(item.oj_ip_diff || 0)
    const mrIpDiff = Math.abs(item.mr_ip_diff || 0)
    return sum + Math.max(ojMrDiff, ojIpDiff, mrIpDiff)
  }, 0)
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '一致': 'success',
    '数据不一致': 'warning',
    'OJ无数据': 'danger',
    'MR无数据': 'danger',
    'IP无数据': 'danger',
    '未发送': 'warning',
    '仅ERP有数据': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取差异值的样式类
const getDifferenceClass = (value: number) => {
  if (value > 0) return 'positive-diff'
  if (value < 0) return 'negative-diff'
  return 'zero-diff'
}

// showDetailedResults 方法不再需要，因为表格直接嵌入在对话框中

// 获取摘要文本
const getSummaryText = () => {
  if (!props.resultData?.summary) return ''

  const summary = props.resultData.summary
  const statistics = props.resultData.statistics

  if (props.taskType === 'steel_cost_verification') {
    if (statistics) {
      return `OJ: ${statistics.oj_stats?.count || 0}条，MR: ${statistics.mr_stats?.count || 0}条，IP: ${statistics.ip_stats?.count || 0}条`
    }
    return `总记录数: ${summary.total_records || 0}条`
  } else if (props.taskType === 'data_comparison') {
    if (statistics) {
      return `MES: ${statistics.mes_stats?.heat_count || 0}个炉号，ERP: ${statistics.erp_stats?.heat_count || 0}个炉号`
    }
    return `总记录数: ${summary.total_records || 0}条`
  }
  return ''
}

// 关闭对话框
const handleClose = (value?: boolean) => {
  // 如果value是boolean类型，使用它；否则默认为false
  const newValue = typeof value === 'boolean' ? value : false
  emit('update:visible', newValue)
}



// 导出结果
const exportResults = () => {
  try {
    const dataStr = JSON.stringify(props.resultData, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${props.taskType}_结果_${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    URL.revokeObjectURL(url)
    ElMessage.success('结果导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}
</script>

<style scoped>
.result-container {
  max-height: 70vh;
  overflow-y: auto;
}

.summary-card,
.data-stats-card,
.action-card,
.no-differences-card {
  margin-bottom: 20px;
}

.summary-card:last-child,
.data-stats-card:last-child,
.action-card:last-child,
.no-differences-card:last-child {
  margin-bottom: 0;
}

.action-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.summary-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.summary-text {
  color: #606266;
  font-size: 14px;
}

.no-differences {
  text-align: center;
  padding: 20px;
}



.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.stat-title {
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 12px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-weight {
  font-size: 14px;
  color: #606266;
}

.stat-details p {
  margin: 8px 0;
  color: #606266;
}

.positive-diff {
  color: #F56C6C;
}

.negative-diff {
  color: #67C23A;
}

.zero-diff {
  color: #909399;
}

.result-json {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
}

.stat-group {
  margin-bottom: 16px;
}

.stat-group h4 {
  margin: 0 0 12px 0;
  color: #409EFF;
  font-size: 14px;
  font-weight: 600;
}

.stat-tag {
  margin: 4px 8px 4px 0;
  display: inline-block;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
