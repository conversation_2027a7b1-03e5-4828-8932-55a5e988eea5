import { ref } from 'vue'

// 进度对话框状态 - 使用单例模式，但支持队列
const progressDialogVisible = ref(false)
const taskProgress = ref(0)
const progressMessage = ref('')
const taskStatus = ref('')
const resultSummary = ref('')
const errorMessage = ref('')
const dialogTitle = ref('任务进度')
const currentTaskId = ref<number | null>(null)

export function useProgressDialog() {
  const showProgressDialog = (title: string, message: string, taskId?: number) => {
    // 如果已经有任务在运行，先关闭
    if (progressDialogVisible.value) {
      closeProgressDialog()
    }

    dialogTitle.value = title
    progressMessage.value = message
    taskProgress.value = 0
    taskStatus.value = 'running'
    resultSummary.value = ''
    errorMessage.value = ''
    currentTaskId.value = taskId || null
    progressDialogVisible.value = true
  }

  const updateProgress = (progress: number, message: string) => {
    taskProgress.value = progress
    progressMessage.value = message
  }

  const setCompleted = (summary: string) => {
    taskStatus.value = 'completed'
    taskProgress.value = 100
    resultSummary.value = summary
  }

  const setFailed = (error: string) => {
    taskStatus.value = 'failed'
    errorMessage.value = error
  }

  const closeProgressDialog = () => {
    progressDialogVisible.value = false
    // 重置状态
    taskProgress.value = 0
    progressMessage.value = ''
    taskStatus.value = ''
    resultSummary.value = ''
    errorMessage.value = ''
    currentTaskId.value = null
  }

  return {
    // 状态
    progressDialogVisible,
    taskProgress,
    progressMessage,
    taskStatus,
    resultSummary,
    errorMessage,
    dialogTitle,

    // 方法
    showProgressDialog,
    updateProgress,
    setCompleted,
    setFailed,
    closeProgressDialog
  }
}
