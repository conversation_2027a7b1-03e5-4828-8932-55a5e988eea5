import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

// WebSocket连接状态
let websocket: WebSocket | null = null
const currentTaskId = ref<number | null>(null)

export function useWebSocket() {
  const connectWebSocket = (
    taskId: number,
    createWebSocketFn: (clientId: string) => WebSocket,
    messageHandler: (message: any) => void
  ) => {
    currentTaskId.value = taskId

    // 使用实际的用户ID作为客户端ID
    const userStore = useUserStore()
    const clientId = userStore.userId ? String(userStore.userId) : "1"
    console.log(`连接WebSocket，客户端ID: ${clientId}, 任务ID: ${taskId}`)
    websocket = createWebSocketFn(clientId)

    websocket.onopen = () => {
      console.log(`WebSocket连接已建立，客户端ID: ${clientId}`)
      // 发送一个测试消息确认连接
      if (websocket) {
        websocket.send(JSON.stringify({
          type: 'test',
          message: 'WebSocket连接测试',
          timestamp: Date.now()
        }))
      }
    }

    websocket.onmessage = (event) => {
      try {
        console.log(`收到WebSocket消息:`, event.data)
        const message = JSON.parse(event.data)
        console.log(`解析后的消息:`, message)
        console.log(`当前任务ID: ${currentTaskId.value}, 消息任务ID: ${message.task_id}`)
        if (message.task_id === currentTaskId.value) {
          console.log('任务ID匹配，处理消息')
          messageHandler(message)
        } else {
          console.log('任务ID不匹配，忽略消息')
        }
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    websocket.onclose = (event) => {
      console.log(`WebSocket连接已关闭，客户端ID: ${clientId}, 代码: ${event.code}, 原因: ${event.reason}`)
    }

    websocket.onerror = (error) => {
      console.error(`WebSocket连接错误，客户端ID: ${clientId}:`, error)
    }
  }

  const disconnectWebSocket = () => {
    if (websocket) {
      websocket.close()
      websocket = null
    }
    currentTaskId.value = null
  }

  return {
    connectWebSocket,
    disconnectWebSocket
  }
}
