<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Upload /></el-icon>
        <span>自动排程（IS=>IX=>IP）</span>
      </div>
    </template>
    <div class="card-content">
      <p>自动执行IX收数，然后抛送到IP（板坯-IS）</p>
      <el-button 
        type="primary" 
        class="action-btn" 
        @click="startAutoScheduleIS" 
        :loading="isRunning" 
        :disabled="isRunning"
      >
        {{ isRunning ? '执行中...' : '执行' }}
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { autoScheduleApi, type AutoScheduleRequest } from '../../../api'
import { useProgressDialog } from '../composables/useProgressDialog'
import { useWebSocket } from '../composables/useWebSocket'

// 任务状态
const isRunning = ref(false)

// 使用进度对话框组合式函数
const { showProgressDialog, updateProgress, setCompleted, setFailed, closeProgressDialog } = useProgressDialog()

// 使用WebSocket组合式函数
const { connectWebSocket, disconnectWebSocket } = useWebSocket()

// 开始自动排程IS
const startAutoScheduleIS = async () => {
  try {
    isRunning.value = true

    const request: AutoScheduleRequest = { task_type: 'IS_IX_IP' }

    const response = await autoScheduleApi.startAutoSchedule(request)

    if (response.code === 200) {
      const taskId = response.data.task_id
      
      // 显示进度对话框
      showProgressDialog('自动排程IS=>IX=>IP进度', '自动排程IS=>IX=>IP任务已启动，正在初始化...')
      
      // 建立WebSocket连接
      connectWebSocket(
        taskId,
        autoScheduleApi.createWebSocket,
        (message) => {
          if (message.type === 'task_progress') {
            // 更新进度
            updateProgress(message.progress || 0, message.message || '')

            if (message.progress === 100 && message.data) {
              isRunning.value = false

              // 显示排程结果
              const stepTimings = message.data.step_timings || {}
              const totalTime = Object.values(stepTimings).reduce((sum: number, time: any) => sum + (Number(time) || 0), 0)
              const resultMessage = `自动排程IS=>IX=>IP完成！总耗时: ${(totalTime as number).toFixed(2)}秒`

              setCompleted(resultMessage)
              ElMessage.success(resultMessage)

              // 3秒后自动关闭进度对话框
              setTimeout(() => {
                closeProgressDialog()
                disconnectWebSocket()
              }, 3000)
            }
          } else if (message.type === 'task_completed') {
            isRunning.value = false

            if (message.success) {
              setCompleted(message.result_summary || '自动排程IS=>IX=>IP完成')
              ElMessage.success('自动排程IS=>IX=>IP完成')
            } else {
              setFailed(message.result_summary || '自动排程IS=>IX=>IP失败')
              ElMessage.error('自动排程IS=>IX=>IP失败')
            }

            // 3秒后自动关闭进度对话框
            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        }
      )

      ElMessage.success('自动排程IS=>IX=>IP任务已启动')
    } else {
      ElMessage.error(response.msg || '启动任务失败')
      isRunning.value = false
    }
  } catch (error) {
    console.error('启动自动排程IS失败:', error)
    ElMessage.error('启动自动排程IS失败')
    isRunning.value = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}
</style>
