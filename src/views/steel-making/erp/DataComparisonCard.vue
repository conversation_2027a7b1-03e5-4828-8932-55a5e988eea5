<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><DataAnalysis /></el-icon>
        <span>物料消耗对比</span>
      </div>
    </template>
    <div class="card-content">
      <p>以MES数据为主，对比ERP与MES每日物料消耗的数据差异</p>
      <el-button type="primary" class="action-btn" @click="showDialog">开始对比</el-button>
    </div>

    <!-- 数据对比对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="物料消耗对比"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="form.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="form.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="厂别" prop="factory">
          <el-select v-model="form.factory" placeholder="请选择厂别" style="width: 100%">
            <el-option label="一厂" value="一厂" />
            <el-option label="二厂" value="二厂" />
          </el-select>
        </el-form-item>
        <el-form-item label="炉号">
          <el-input
            v-model="form.heatNo"
            placeholder="请输入炉号（可选，为空则对比所有炉号）"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="物料编码">
          <el-input
            v-model="form.materialCode"
            placeholder="请输入物料编码（可选，为空则对比所有物料）"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="isRunning">取消</el-button>
          <el-button type="primary" @click="startComparison" :loading="isRunning">
            {{ isRunning ? '执行中...' : '开始对比' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { DataAnalysis } from '@element-plus/icons-vue'
import { dataComparisonApi, type DataComparisonRequest } from '../../../api'
import { useProgressDialog } from '../composables/useProgressDialog'
import { useWebSocket } from '../composables/useWebSocket'

// 从父组件注入结果显示方法
const showComparisonResults = inject('showComparisonResults') as (data: any) => void

// 对话框状态
const dialogVisible = ref(false)
const isRunning = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  startDate: '',
  endDate: '',
  factory: '',
  heatNo: '',
  materialCode: ''
})

const rules = {
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  factory: [{ required: true, message: '请选择厂别', trigger: 'change' }]
}

// 使用进度对话框组合式函数
const { showProgressDialog, updateProgress, setCompleted, setFailed, closeProgressDialog } = useProgressDialog()

// 使用WebSocket组合式函数
const { connectWebSocket, disconnectWebSocket } = useWebSocket()

// 显示对话框
const showDialog = () => {
  dialogVisible.value = true
}



// 开始对比
const startComparison = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证日期范围
    if (form.startDate > form.endDate) {
      ElMessage.error('开始日期不能大于结束日期')
      return
    }

    isRunning.value = true

    const request: DataComparisonRequest = {
      start_date: form.startDate,
      end_date: form.endDate,
      factory: form.factory,
      heat_no: form.heatNo || undefined,
      material_code: form.materialCode || undefined
    }

    const response = await dataComparisonApi.startComparison(request)

    if (response.code === 200) {
      const taskId = response.data.task_id
      dialogVisible.value = false
      
      // 显示进度对话框
      showProgressDialog('数据对比进度', '任务已启动，正在初始化...')
      
      // 建立WebSocket连接
      connectWebSocket(
        taskId,
        dataComparisonApi.createWebSocket,
        (message) => {
          // 处理WebSocket消息的回调
          if (message.type === 'task_progress') {
            // 更新进度
            updateProgress(message.progress || 0, message.message || '')

            // 如果进度达到100%且有数据，说明对比完成
            if (message.progress === 100 && message.data) {
              isRunning.value = false
              setCompleted('数据对比完成')

              // 显示成功消息
              ElMessage.success('数据对比完成！')

              // 处理结果数据并显示
              if (message.data) {
                // 确保传递taskId
                const resultData = {
                  ...message.data,
                  task_id: message.task_id || taskId
                }
                showComparisonResults(resultData)
              }

              // 3秒后自动关闭进度对话框
              setTimeout(() => {
                closeProgressDialog()
                disconnectWebSocket()
              }, 3000)
            }
          } else if (message.type === 'task_completed') {
            isRunning.value = false
            if (message.success) {
              setCompleted(message.result_summary || '数据对比完成')
              ElMessage.success('数据对比完成')
            } else {
              setFailed(message.result_summary || '数据对比失败')
              ElMessage.error('数据对比失败')
            }

            // 3秒后自动关闭进度对话框
            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        }
      )

      ElMessage.success('数据对比任务已启动')
    } else {
      ElMessage.error(response.msg || '启动任务失败')
      isRunning.value = false
    }
  } catch (error) {
    console.error('启动对比失败:', error)
    ElMessage.error('启动对比失败')
    isRunning.value = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
