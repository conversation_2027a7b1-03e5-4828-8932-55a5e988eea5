<template>
  <div class="tab-content">
    <el-row :gutter="24">
      <el-col :span="6">
        <!-- MES 和 ERP 物料对比 -->
        <DataComparisonCard />
      </el-col>
      <el-col :span="6">
        <!-- 电文检查 -->
        <MessageCheckCard />
      </el-col>
      <el-col :span="6">
        <!-- IX收账检查 -->
        <IxReceivaCard />
      </el-col>
      <el-col :span="6">
        <!-- 炼钢成本数据核对 -->
        <SteelCostDataVerificationCard />
      </el-col>
      <el-col :span="6">
        <!-- IX批次检查 -->
        <IxBatchCheckCard />
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="6">
        <!-- 自动排程-抛账 -->
        <AutoSchedulePostingCard />
      </el-col>
      <el-col :span="6">
        <!-- 自动排程（IB=>IX=>IP） -->
        <AutoScheduleIBCard />
      </el-col>
      <el-col :span="6">
        <!-- 自动排程（IS=>IX=>IP） -->
        <AutoScheduleISCard />
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="6">
        <!-- 增加期初连铸资料 -->
        <InitialCastingCard />
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <!-- 炉订号置换异常-->
      <el-col :span="6">
        <HeatNoSwitchErrorCard />
      </el-col>
      <!-- 产量报表数据异常 -->
      <el-col :span="6">
        <ProductionReportAnomalyCard />
      </el-col>
      <!-- 折罐回炉异常-->
      <el-col :span="6">
        <LaundryReturnAnomalyCard />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import DataComparisonCard from './DataComparisonCard.vue'
import MessageCheckCard from './MessageCheckCard.vue'
import AutoSchedulePostingCard from './AutoSchedulePostingCard.vue'
import AutoScheduleIBCard from './AutoScheduleIBCard.vue'
import AutoScheduleISCard from './AutoScheduleISCard.vue'
import InitialCastingCard from './InitialCastingCard.vue'
import IxReceivaCard from './IxReceivaCard.vue'
import SteelCostDataVerificationCard from './SteelCostDataVerificationCard.vue'

import HeatNoSwitchErrorCard from './HeatNoSwitchErrorCard.vue'
import ProductionReportAnomalyCard from './ProductionReportAnomalyCard.vue'
import LaundryReturnAnomalyCard from './LaundryReturnAnomalyCard.vue'
import IxBatchCheckCard from './IxBatchCheckCard.vue'
</script>

<style scoped>
.tab-content {
  padding: 20px 0;
}
</style>
