<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><DataAnalysis /></el-icon>
        <span>炉订号置换异常</span>
      </div>
    </template>

    <div class="card-content">
      <p>PDO(CMWS03)炉号与旧炉订号与当下匹配不一致所导致的问题</p>
      <el-button type="primary" class="action-btn" @click="showInputDialog">
        开始维护
      </el-button>
    </div>

  </el-card>

  <!-- 输入对话框 -->
  <el-dialog
    v-model="inputDialogVisible"
    title="炉订号置换异常维护"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="form" label-width="80px">
      <el-form-item label="炉号" required>
        <el-input
          v-model="form.heatNo"
          type="textarea"
          :rows="4"
          placeholder="请输入炉号，支持多个炉号，每行一个或用逗号分隔&#10;例如：&#10;*********&#10;*********,*********"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-text type="info" size="small">
          支持多种输入格式：每行一个炉号，或用逗号、分号、空格分隔
        </el-text>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="inputDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :disabled="!form.heatNo.trim()"
        >
          生成维护步骤
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 维护步骤对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="炉订号置换异常维护步骤"
    width="90%"
    :close-on-click-modal="false"
    class="maintenance-dialog"
    append-to-body
  >
      <div class="maintenance-steps">
        <el-alert
          title="请按照以下步骤依次执行维护操作"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;"
        />

        <div class="progress-section">
          <el-progress
            :percentage="progressPercentage"
            :color="progressColor"
            :stroke-width="8"
            text-inside
          />
          <p class="progress-text">
            进度: {{ completedSteps }}/{{ maintenanceSteps.length }} 步骤已完成
          </p>
        </div>

        <div class="steps-container">
          <div
            v-for="(step, index) in maintenanceSteps"
            :key="index"
            class="step-item"
            :class="{ 'completed': step.completed }"
          >
            <div class="step-header" @click="toggleStep(index)">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-title">
                <h4>{{ step.title }}</h4>
                <p class="step-description">{{ step.description }}</p>
              </div>
              <div class="step-actions">
                <el-checkbox
                  v-model="step.completed"
                  @change="updateProgress"
                  size="large"
                >
                  完成
                </el-checkbox>
                <el-button
                  v-if="step.sql"
                  size="small"
                  type="primary"
                  @click.stop="copySql(step.sql)"
                >
                  复制SQL
                </el-button>
                <el-button
                  v-if="step.url"
                  size="small"
                  type="success"
                  @click.stop="openUrl(step.url)"
                >
                  打开链接
                </el-button>
                <el-button
                  v-if="step.queryable"
                  size="small"
                  type="warning"
                  @click.stop="queryStepData(step)"
                  :loading="step.queryLoading"
                >
                  查询数据
                </el-button>
              </div>
            </div>

            <div v-if="step.expanded" class="step-content">
              <div v-if="step.sql" class="sql-section">
                <h5>SQL语句:</h5>
                <pre class="sql-code">{{ step.sql }}</pre>
              </div>

              <div v-if="step.note" class="note-section">
                <el-alert
                  :title="step.note"
                  type="warning"
                  :closable="false"
                  show-icon
                />
              </div>

              <div v-if="step.url" class="url-section">
                <p><strong>操作链接:</strong></p>
                <el-link :href="step.url" target="_blank" type="primary">
                  {{ step.linkText || step.url }}
                </el-link>
              </div>

              <div v-if="step.queryResult" class="query-result-section">
                <h5>查询结果:</h5>
                <div v-if="step.queryType === 'heat-plan-mapping'" class="result-content">
                  <p><strong>查询总数:</strong> {{ step.queryResult.total_count }}</p>
                  <div class="table-container">
                    <el-table :data="step.queryResult.results" size="small" border>
                      <el-table-column prop="heat_id" label="炉号" width="120" />
                      <el-table-column prop="plan_heat_id" label="计划炉号" width="120" />
                      <el-table-column prop="found" label="状态" width="80">
                        <template #default="scope">
                          <el-tag :type="scope.row.found ? 'success' : 'danger'">
                            {{ scope.row.found ? '找到' : '未找到' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>

                <div v-if="step.queryType === 'heat-status'" class="result-content">
                  <p><strong>查询总数:</strong> {{ step.queryResult.total_count }}</p>
                  <div class="table-container">
                    <el-table :data="step.queryResult.results" size="small" border>
                      <el-table-column prop="heat_no" label="炉号" width="120" />
                      <el-table-column prop="status" label="状态" width="80" />
                      <el-table-column prop="found" label="查询状态" width="100">
                        <template #default="scope">
                          <el-tag :type="scope.row.found ? 'success' : 'danger'">
                            {{ scope.row.found ? '找到' : '未找到' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作建议" min-width="200">
                        <template #default="scope">
                          <div v-if="scope.row.found">
                            <el-tag
                              v-if="scope.row.status === 'A' || scope.row.status === 'X'"
                              type="warning"
                              size="small"
                            >
                              重收CMWS01状态是B的电文
                            </el-tag>
                            <el-tag
                              v-else-if="scope.row.status === 'B'"
                              type="warning"
                              size="small"
                            >
                              重收CMWS01状态是E的电文
                            </el-tag>
                            <span v-else class="text-muted">无特殊操作</span>
                          </div>
                          <span v-else class="text-muted">-</span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>

                <div v-if="step.queryType === 'message-assist'" class="result-content">
                  <p><strong>查询日期:</strong> {{ step.queryResult.check_date }}</p>
                  <p><strong>记录总数:</strong> {{ step.queryResult.total_count }}</p>
                  <div v-if="step.queryResult.records && step.queryResult.records.length > 0" class="table-container">
                    <el-table :data="step.queryResult.records.slice(0, 10)" size="small" border>
                      <el-table-column prop="recsysdate" label="日期" width="100" />
                      <el-table-column prop="recsystime" label="时间" width="100" />
                      <el-table-column prop="heatno" label="炉号" width="120" />
                      <el-table-column prop="formid" label="电文类型" width="100" />
                      <el-table-column prop="datastr" label="数据内容" show-overflow-tooltip />
                    </el-table>
                    <p v-if="step.queryResult.records.length > 10" class="more-records">
                      显示前10条记录，共 {{ step.queryResult.records.length }} 条
                    </p>
                  </div>
                  <div v-else class="no-data">
                    <p>未找到相关电文记录</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="resetSteps">重置步骤</el-button>
        </div>
      </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DataAnalysis } from '@element-plus/icons-vue'
import { heatNoSwitchApi } from '../../../api/heatNoSwitch'

// 响应式数据
const inputDialogVisible = ref(false)
const dialogVisible = ref(false)
const form = ref({
  heatNo: ''
})

// 解析炉号输入
const parseHeatNumbers = (input: string): string[] => {
  if (!input.trim()) return []

  // 支持多种分隔符：换行、逗号、分号、空格
  const heatNumbers = input
    .split(/[\n,;，；\s]+/)
    .map(h => h.trim())
    .filter(h => h.length > 0)

  return [...new Set(heatNumbers)] // 去重
}

interface MaintenanceStep {
  title: string
  description: string
  sql?: string
  url?: string
  linkText?: string
  note?: string
  completed: boolean
  expanded: boolean
  queryable?: boolean
  queryType?: string
  queryResult?: any
  queryLoading?: boolean
}

const maintenanceSteps = ref<MaintenanceStep[]>([])

// 计算属性
const completedSteps = computed(() => {
  return maintenanceSteps.value.filter(step => step.completed).length
})

const progressPercentage = computed(() => {
  if (maintenanceSteps.value.length === 0) return 0
  return Math.round((completedSteps.value / maintenanceSteps.value.length) * 100)
})

const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
})

// 显示输入对话框
const showInputDialog = () => {
  form.value.heatNo = ''
  inputDialogVisible.value = true
}

// 生成维护步骤
const generateMaintenanceSteps = (heatNumbers: string[]) => {
  // 获取当天日期
  const today = new Date().toISOString().split('T')[0].replace(/-/g, '')

  // 生成IN子句用于SQL
  const heatNoList = heatNumbers.map(h => `'${h}'`).join(',')

  return [
    {
      title: '查询炉号和计划炉号对照关系',
      description: '连接MES的Oracle数据库，查询炉号和计划炉号的对照关系',
      sql: `select HEAT_ID, PLAN_HEAT_ID from jgpss.PPS_B_CHEAT_PLAN where HEAT_ID in (${heatNoList});`,
      note: '请记录查询结果中的HEAT_ID和PLAN_HEAT_ID，下一步需要使用这些信息',
      completed: false,
      expanded: false,
      queryable: true,
      queryType: 'heat-plan-mapping'
    },
    {
      title: '执行CMWS03电文转换',
      description: '跳转到ERP系统，执行CMWS03电文，将炉号和炉订号转换正确',
      url: 'https://jgerp.jggroup.cn/erp/dzp/jsp/dzpjQueue.jsp',
      linkText: '打开ERP电文队列管理系统',
      note: '在打开的页面中执行CMWS03电文，使用第1步查询到的炉号和计划炉号信息进行转换',
      completed: false,
      expanded: false
    },
    {
      title: '查询炉号状态',
      description: '查询ERP数据库，获取炉号的当前状态',
      sql: `SELECT heatno, status from db.tbws101 where heatno in (${heatNoList});`,
      note: '请记录查询结果中的status值，根据状态执行下一步操作',
      completed: false,
      expanded: false,
      queryable: true,
      queryType: 'heat-status'
    },
    {
      title: '根据状态重收电文',
      description: '根据炉号状态决定重收哪种电文',
      note: '如果状态为A或X，重收CMWS01状态是B的电文；如果状态为B，重收CMWS01状态是E的电文',
      completed: false,
      expanded: false
    },
    {
      title: '查询电文',
      description: '跳转到ERP系统，通过炉号查询相关电文',
      url: 'https://jgerp.jggroup.cn/erp/dzp/jsp/dzpjQueue.jsp',
      linkText: '打开ERP电文队列管理系统',
      note: '在电文队列中查找与该炉号相关的电文记录',
      completed: false,
      expanded: false
    },
    {
      title: '辅助查询SQL',
      description: '收完电文后，使用以下SQL查询列表获取辅助提示',
      sql: `SELECT
    RECSYSDATE,
    RECSYSTIME,
    JSON_VALUE(DATASTR, '$.HEATNO') AS HEATNO,
    FORMID,
    DATASTR
FROM DB.TBDZPQUEUEREC
WHERE QUEUEID LIKE '%REV'
  AND RECSYSDATE >= '${today}'
  AND (
            FORMID IN ('CMOJ10', 'CMOJ05', 'CMOJ05_1')
        OR
            (FORMID NOT IN ('CMOJ10', 'CMOJ05', 'CMOJ05_1') AND DATASTUS = 'F')
    )
    and FORMID not like '%CMWS%'
 AND (${heatNumbers.map(h => `DATASTR LIKE '%${h}%'`).join(' OR ')})
ORDER BY
    JSON_VALUE(DATASTR, '$.HEATNO'),
    CASE FORMID
        -- 连铸实绩
        WHEN 'CMOJ05' THEN 7
        -- 连铸实绩
        WHEN 'CMOJ05_1' THEN 8
        -- 合金辅料实绩
        WHEN 'CMOJ10' THEN 9
        -- 回炉钢水实绩
        WHEN 'CMOJ11' THEN 10
        -- 方坯产出实绩
        WHEN 'XMIB01' THEN 11
        -- 方坯入库及储位变更
        WHEN 'XMIB03' THEN 14
        -- 方坯存货移动
        WHEN 'XMIB04' THEN 15
        -- 方坯出库
        WHEN 'XMIB05' THEN 16
        -- 方坯低倍处置
        WHEN 'XMIB06' THEN 17
        -- 方坯并炉同步
        WHEN 'XMIB07' THEN 17
        -- 板坯产出
        WHEN 'CMIS01' THEN 18
        -- 板坯入库及储位变更
        WHEN 'CMIS03' THEN 19
        -- 板坯存货异动
        WHEN 'CMIS04' THEN 20
        -- 板坯出库信息
        WHEN 'CMIS05' THEN 21
        -- 生产日报栏位同步
        WHEN 'CMIS07' THEN 22
        ELSE 999
        END,
    RECSYSDATE,
    RECSYSTIME;`,
      note: '此SQL用于查询电文处理情况，可以取消注释最后的条件来过滤特定炉号的记录',
      completed: false,
      expanded: false,
      queryable: true,
      queryType: 'message-assist'
    }
  ]
}

// 提交表单
const handleSubmit = () => {
  if (!form.value.heatNo.trim()) {
    ElMessage.warning('请输入炉号')
    return
  }

  // 解析炉号
  const heatNumbers = parseHeatNumbers(form.value.heatNo)
  if (heatNumbers.length === 0) {
    ElMessage.warning('请输入有效的炉号')
    return
  }

  // 关闭输入对话框
  inputDialogVisible.value = false

  // 生成维护步骤
  maintenanceSteps.value = generateMaintenanceSteps(heatNumbers)
  dialogVisible.value = true

  ElMessage.success(`已生成 ${heatNumbers.length} 个炉号的维护步骤`)
}

// 切换步骤展开状态
const toggleStep = (index: number) => {
  maintenanceSteps.value[index].expanded = !maintenanceSteps.value[index].expanded
}

// 复制SQL到剪贴板
const copySql = async (sql: string) => {
  try {
    await navigator.clipboard.writeText(sql)
    ElMessage.success('SQL语句已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 打开URL
const openUrl = (url: string) => {
  window.open(url, '_blank')
}

// 更新进度
const updateProgress = () => {
  // 进度更新会自动通过计算属性反映
}

// 重置步骤
const resetSteps = () => {
  maintenanceSteps.value.forEach(step => {
    step.completed = false
    step.expanded = false
    step.queryResult = null
    step.queryLoading = false
  })
}

// 查询步骤数据
const queryStepData = async (step: MaintenanceStep) => {
  if (!step.queryable || !step.queryType) return

  step.queryLoading = true

  try {
    let result: any = null
    const heatNumbers = parseHeatNumbers(form.value.heatNo)

    switch (step.queryType) {
      case 'heat-plan-mapping':
        const mappingResponse = await heatNoSwitchApi.getHeatPlanMapping({ heat_ids: heatNumbers })
        if (mappingResponse.code === 200) {
          result = mappingResponse.data
        } else {
          throw new Error(mappingResponse.msg || '查询失败')
        }
        break

      case 'heat-status':
        const statusResponse = await heatNoSwitchApi.getHeatStatus({ heat_nos: heatNumbers })
        if (statusResponse.code === 200) {
          result = statusResponse.data
        } else {
          throw new Error(statusResponse.msg || '查询失败')
        }
        break

      case 'message-assist':
        // 对于电文辅助查询，我们可以传入多个炉号或者不传炉号
        const assistResponse = await heatNoSwitchApi.getMessageAssist({
          heat_no: heatNumbers.length === 1 ? heatNumbers[0] : undefined,
          check_date: new Date().toISOString().split('T')[0]
        })
        if (assistResponse.code === 200) {
          result = assistResponse.data
        } else {
          throw new Error(assistResponse.msg || '查询失败')
        }
        break

      default:
        throw new Error('未知的查询类型')
    }

    step.queryResult = result
    step.expanded = true
    ElMessage.success('查询成功')

  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error(`查询失败: ${error.message || error}`)
  } finally {
    step.queryLoading = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

/* 维护步骤对话框样式 */
:deep(.maintenance-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.maintenance-steps {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.progress-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-text {
  margin-top: 10px;
  text-align: center;
  color: #666;
  font-weight: 500;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-item {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  background: white;
  transition: all 0.3s ease;
  overflow: hidden;
}

.step-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.step-item.completed {
  border-color: #67c23a;
  background: #f0f9ff;
}

.step-header {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  cursor: pointer;
  gap: 16px;
}

.step-number {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.step-item.completed .step-number {
  background: #67c23a;
}

.step-title {
  flex: 1;
  min-width: 0;
}

.step-title h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.step-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.step-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.step-content {
  padding: 0 20px 20px 68px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  padding-top: 16px;
}

.sql-section {
  margin-bottom: 16px;
}

.sql-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.sql-code {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #2c3e50;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.note-section {
  margin-bottom: 16px;
}

.url-section p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 查询结果样式 */
.query-result-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.query-result-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.result-content {
  font-size: 14px;
}

.success-result {
  color: #67c23a;
}

.success-result p {
  margin: 4px 0;
}

.error-result {
  color: #f56c6c;
}

.error-result p {
  margin: 4px 0;
}

.table-container {
  margin-top: 12px;
}

.more-records {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.no-data {
  color: #909399;
  text-align: center;
  padding: 20px;
}
</style>
