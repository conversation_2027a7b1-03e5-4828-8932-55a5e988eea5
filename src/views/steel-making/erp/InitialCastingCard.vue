<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><DataAnalysis /></el-icon>
        <span>增加期初连铸资料</span>
      </div>
    </template>
    <div class="card-content">
      <p>适用于一开始的钢坯，系统还未上线，对其增加期初的连铸资料</p>
      <el-button type="primary" class="action-btn" @click="showDialog">增加</el-button>
    </div>

    <!-- 期初连铸资料对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="增加期初连铸资料"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="炉号" prop="heatNo">
          <el-input
            v-model="form.heatNo"
            placeholder="请输入炉号"
            style="width: 100%"
            maxlength="20"
            show-word-limit
          />
          <div class="form-tip">
            <el-text size="small" type="info">
              如果炉号长度大于9位，将生成两条SQL语句（原炉号和去掉最后一位的炉号）
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="isRunning">取消</el-button>
          <el-button type="primary" @click="generateSQL" :loading="isRunning">
            {{ isRunning ? '生成中...' : '生成SQL' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 期初连铸资料结果对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      title="期初连铸资料SQL"
      width="90%"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="initial-casting-result">
        <div class="result-header">
          <el-alert
            :title="`成功生成 ${result.generated_count} 条SQL语句`"
            :description="`炉号: ${result.heat_no}`"
            type="success"
            show-icon
            :closable="false"
          />
        </div>

        <div class="sql-statements" style="margin-top: 20px;">
          <div v-for="(sql, index) in result.sql_statements" :key="index" class="sql-item">
            <div class="sql-header">
              <h4>SQL语句 {{ index + 1 }}</h4>
              <el-button size="small" @click="copySQLToClipboard(sql)">复制</el-button>
            </div>
            <el-input
              type="textarea"
              :value="sql"
              readonly
              :rows="8"
              style="font-family: monospace; font-size: 12px;"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resultDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { DataAnalysis } from '@element-plus/icons-vue'
import { initialCastingApi, type InitialCastingRequest, type InitialCastingResponse } from '../../../api'

// 对话框状态
const dialogVisible = ref(false)
const resultDialogVisible = ref(false)
const isRunning = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  heatNo: ''
})

const rules = {
  heatNo: [{ required: true, message: '请输入炉号', trigger: 'blur' }]
}

// 结果数据
const result = ref<InitialCastingResponse>({
  sql_statements: [],
  heat_no: '',
  generated_count: 0
})

// 显示对话框
const showDialog = () => {
  // 清空表单
  form.heatNo = ''
  dialogVisible.value = true
}

// 生成SQL
const generateSQL = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    isRunning.value = true

    const request: InitialCastingRequest = {
      heat_no: form.heatNo.trim()
    }

    const response = await initialCastingApi.generateSQL(request)

    if (response.code === 200) {
      // 保存结果
      result.value = response.data

      // 关闭输入对话框
      dialogVisible.value = false

      // 显示结果对话框
      resultDialogVisible.value = true

      ElMessage.success(`成功生成 ${response.data.generated_count} 条SQL语句`)
    } else {
      ElMessage.error(response.msg || '生成SQL失败')
    }
  } catch (error) {
    console.error('生成期初连铸资料SQL失败:', error)
    ElMessage.error('生成SQL失败')
  } finally {
    isRunning.value = false
  }
}

// 复制SQL到剪贴板
const copySQLToClipboard = async (sql: string) => {
  try {
    await navigator.clipboard.writeText(sql)
    ElMessage.success('SQL已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-tip {
  margin-top: 8px;
}

.initial-casting-result {
  max-height: 70vh;
  overflow-y: auto;
}

.sql-item {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.sql-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sql-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}
</style>
