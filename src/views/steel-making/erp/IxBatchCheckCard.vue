<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Warning /></el-icon>
        <span>IX批次检查</span>
      </div>
    </template>
    <div class="card-content">
      <p>检查IX系统批次日志中的错误记录</p>
      <el-button type="primary" class="action-btn" @click="startCheck" :loading="isRunning">
        {{ isRunning ? '检查中...' : '开始检查' }}
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { ixBatchCheckApi } from '../../../api'
import { useProgressDialog } from '../composables/useProgressDialog'
import { useWebSocket } from '../composables/useWebSocket'

// 从父组件注入结果显示方法
const showIxBatchCheckResults = inject('showIxBatchCheckResults') as (data: any) => void

// 运行状态
const isRunning = ref(false)

// 使用进度对话框
const {
  progressDialogVisible,
  progressPercentage,
  progressMessage,
  isCompleted,
  isFailed,
  showProgressDialog,
  closeProgressDialog,
  updateProgress,
  setCompleted,
  setFailed
} = useProgressDialog()

// 使用WebSocket
const {
  connectWebSocket,
  disconnectWebSocket
} = useWebSocket()

// 开始检查
const startCheck = async () => {
  if (isRunning.value) return

  try {
    isRunning.value = true
    
    // 显示进度对话框
    showProgressDialog('IX批次检查', '正在启动检查任务...')
    
    // 启动检查任务
    const response = await ixBatchCheckApi.startCheck()
    
    if (response.code === 200) {
      const taskId = response.data.task_id
      
      // 建立WebSocket连接
      connectWebSocket(
        taskId,
        ixBatchCheckApi.createWebSocket,
        (message) => {
          if (message.type === 'task_progress') {
            // 更新进度
            updateProgress(message.progress || 0, message.message || '')

            if (message.progress === 100 && message.data) {
              isRunning.value = false

              // 保存检查结果
              const errorCount = message.data.summary?.error_count || 0
              setCompleted(`IX批次检查完成，发现 ${errorCount} 条错误记录`)
              ElMessage.success(`IX批次检查完成，发现 ${errorCount} 条错误记录`)

              // 显示检查结果
              if (message.data) {
                showIxBatchCheckResults(message.data)
              }

              // 3秒后自动关闭进度对话框
              setTimeout(() => {
                closeProgressDialog()
                disconnectWebSocket()
              }, 3000)
            }
          } else if (message.type === 'task_completed') {
            isRunning.value = false

            if (message.success) {
              setCompleted(message.result_summary || 'IX批次检查完成')
              ElMessage.success('IX批次检查完成')
            } else {
              setFailed(message.result_summary || 'IX批次检查失败')
              ElMessage.error('IX批次检查失败')
            }

            // 3秒后自动关闭进度对话框
            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        }
      )

      ElMessage.success('IX批次检查任务已启动')
    } else {
      ElMessage.error(response.msg || '启动任务失败')
      isRunning.value = false
    }
  } catch (error) {
    console.error('启动IX批次检查失败:', error)
    ElMessage.error('启动IX批次检查失败')
    isRunning.value = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}
</style>
