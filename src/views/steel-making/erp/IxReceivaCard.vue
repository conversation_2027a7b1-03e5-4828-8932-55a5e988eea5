<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><DataAnalysis /></el-icon>
        <span>IX收账作业检查</span>
      </div>
    </template>
    <div class="card-content">
      <p>检查IX收账处理作业中的异常情况，包括收账错误、收账处理中、抛IP错误等</p>
      <el-button type="primary" class="action-btn" @click="showDialog">开始检查</el-button>
    </div>

    <!-- IX收账作业检查对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="IX收账作业检查"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="系统" prop="systemId">
          <el-select v-model="form.systemId" placeholder="请选择系统" style="width: 100%">
            <el-option label="IB系统" value="IB" />
            <el-option label="IS系统" value="IS" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="form.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="form.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态筛选" prop="statusFilter">
          <el-checkbox-group v-model="form.statusFilter">
            <el-checkbox label="error">收账错误</el-checkbox>
            <el-checkbox label="processing">收账处理中</el-checkbox>
            <el-checkbox label="ip_error">抛IP错误</el-checkbox>
          </el-checkbox-group>
          <div class="form-tip">
            <el-text size="small" type="info">
              可选择多个状态进行筛选，不选择则显示所有异常状态
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="isRunning">取消</el-button>
          <el-button type="primary" @click="startCheck" :loading="isRunning">
            {{ isRunning ? '检查中...' : '开始检查' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { DataAnalysis } from '@element-plus/icons-vue'
import { ixReceivaApi, type IxReceivaCheckRequest } from '../../../api'
import { useProgressDialog } from '../composables/useProgressDialog'
import { useWebSocket } from '../composables/useWebSocket'

// 从父组件注入结果显示方法
const showIxReceivaResults = inject('showIxReceivaResults') as (data: any) => void

// 对话框状态
const dialogVisible = ref(false)
const isRunning = ref(false)

// 表单引用和数据
const formRef = ref<FormInstance>()
const form = reactive({
  systemId: '',
  startDate: '',
  endDate: '',
  statusFilter: ['error', 'processing', 'ip_error'] // 默认选择所有异常状态
})

// 表单验证规则
const rules = {
  systemId: [{ required: true, message: '请选择系统', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }]
}

// 使用进度对话框组合式函数
const { showProgressDialog, updateProgress, setCompleted, setFailed, closeProgressDialog } = useProgressDialog()

// 使用WebSocket组合式函数
const { connectWebSocket, disconnectWebSocket } = useWebSocket()

// 显示对话框
const showDialog = () => {
  // 设置默认值
  const today = new Date().toISOString().split('T')[0]
  form.startDate = today
  form.endDate = today
  form.systemId = 'IB'
  dialogVisible.value = true
}

// 开始检查
const startCheck = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证日期范围
    if (form.startDate > form.endDate) {
      ElMessage.error('开始日期不能大于结束日期')
      return
    }

    isRunning.value = true

    const request: IxReceivaCheckRequest = {
      system_id: form.systemId,
      start_date: form.startDate.replace(/-/g, ''), // 转换为YYYYMMDD格式
      end_date: form.endDate.replace(/-/g, ''),
      status_filter: form.statusFilter.length > 0 ? form.statusFilter : undefined
    }

    const response = await ixReceivaApi.startCheck(request)

    if (response.code === 200) {
      const taskId = response.data.task_id
      dialogVisible.value = false

      // 显示进度对话框
      showProgressDialog('IX收账作业检查', '任务已启动，正在检查...', taskId)

      // 建立WebSocket连接
      connectWebSocket(
        taskId,
        ixReceivaApi.createWebSocket,
        (message) => {
          if (message.type === 'task_progress') {
            // 更新进度
            updateProgress(message.progress || 0, message.message || '')

            if (message.progress === 100 && message.data) {
              isRunning.value = false
              setCompleted('IX收账作业检查完成')
              ElMessage.success('IX收账作业检查完成')

              // 显示检查结果
              if (message.data) {
                showIxReceivaResults(message.data)
              }

              // 3秒后自动关闭进度对话框
              setTimeout(() => {
                closeProgressDialog()
                disconnectWebSocket()
              }, 3000)
            }
          } else if (message.type === 'task_completed') {
            isRunning.value = false

            if (message.success) {
              setCompleted(message.result_summary || 'IX收账作业检查完成')
              ElMessage.success('IX收账作业检查完成')

              // 显示检查结果
              if (message.data) {
                showIxReceivaResults(message.data)
              }
            } else {
              setFailed(message.result_summary || 'IX收账作业检查失败')
              ElMessage.error('IX收账作业检查失败')
            }

            // 3秒后自动关闭进度对话框
            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        }
      )

      ElMessage.success('IX收账作业检查任务已启动')
    } else {
      ElMessage.error(response.msg || '启动任务失败')
      isRunning.value = false
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('启动IX收账作业检查失败:', error)
      ElMessage.error(error.response?.data?.msg || error.message || '启动任务失败')
    }
    isRunning.value = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-tip {
  margin-top: 8px;
}

</style>
