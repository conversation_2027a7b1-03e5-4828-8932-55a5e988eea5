<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Warning /></el-icon>
        <span>折罐回炉异常</span>
      </div>
    </template>

    <div class="card-content">
      <p>处理折罐回炉异常问题，生成完整的维护步骤清单</p>
      <el-button type="primary" class="action-btn" @click="showInputDialog">
        开始维护
      </el-button>
    </div>
  </el-card>

  <!-- 输入炉号对话框 -->
  <el-dialog
    v-model="inputDialogVisible"
    title="折罐回炉异常维护"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="form" label-width="80px" @submit.prevent="handleSubmit">
      <el-form-item label="炉号" required>
        <el-input
          v-model="form.heatNo"
          placeholder="请输入炉号，例如：A12345"
          @keyup.enter="handleSubmit"
          ref="heatNoInput"
        />
      </el-form-item>
      
      <el-form-item>
        <div class="input-tips">
          <el-alert
            title="请输入需要维护的炉号，系统将生成完整的维护步骤清单"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="inputDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :disabled="!form.heatNo.trim()"
        >
          生成维护步骤
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 维护步骤对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="折罐回炉异常维护步骤"
    width="90%"
    :close-on-click-modal="false"
    class="maintenance-dialog"
    append-to-body
  >
    <div class="maintenance-steps">
      <el-alert
        title="请按照以下步骤依次执行维护操作"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 20px;"
      />

      <div class="steps-container">
        <div 
          v-for="(step, index) in maintenanceSteps" 
          :key="index"
          class="step-item"
          :class="{ 'completed': step.completed }"
        >
          <div class="step-header" @click="toggleStep(index)">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-title">
              <h4>{{ step.title }}</h4>
              <p class="step-description">{{ step.description }}</p>
            </div>
            <div class="step-actions">
              <el-checkbox 
                v-model="step.completed" 
                @change="updateProgress"
                size="large"
              >
                完成
              </el-checkbox>
              <el-button 
                v-if="step.sql"
                size="small" 
                type="primary" 
                @click.stop="copySql(step.sql)"
              >
                复制SQL
              </el-button>
              <el-button 
                v-if="step.url"
                size="small" 
                type="success" 
                @click.stop="openUrl(step.url)"
              >
                打开链接
              </el-button>
            </div>
          </div>
          
          <div v-if="step.expanded" class="step-content">
            <div v-if="step.sql" class="sql-block">
              <div class="sql-header">
                <span>SQL语句：</span>
                <el-button size="small" @click="copySql(step.sql)">复制</el-button>
              </div>
              <pre class="sql-content">{{ step.sql }}</pre>
            </div>
            
            <div v-if="step.note" class="step-note">
              <el-alert :title="step.note" type="warning" :closable="false" />
            </div>
            
            <div v-if="step.url" class="step-link">
              <el-button type="success" @click="openUrl(step.url)">
                <el-icon><Link /></el-icon>
                {{ step.linkText || '打开链接' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="progress-section">
        <el-divider />
        <div class="progress-info">
          <span>完成进度：{{ completedSteps }}/{{ totalSteps }}</span>
          <el-progress 
            :percentage="progressPercentage" 
            :color="progressColor"
            style="margin-top: 10px;"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetSteps">重置步骤</el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Warning } from '@element-plus/icons-vue'

// 表单数据
const form = ref({
  heatNo: ''
})

// 对话框状态
const inputDialogVisible = ref(false)
const dialogVisible = ref(false)

// 输入框引用
const heatNoInput = ref()

// 维护步骤数据
const maintenanceSteps = ref<Array<{
  title: string
  description: string
  sql?: string
  note?: string
  url?: string
  linkText?: string
  completed: boolean
  expanded: boolean
}>>([])

// 计算属性
const totalSteps = computed(() => maintenanceSteps.value.length)
const completedSteps = computed(() => maintenanceSteps.value.filter(step => step.completed).length)
const progressPercentage = computed(() => {
  if (totalSteps.value === 0) return 0
  return Math.round((completedSteps.value / totalSteps.value) * 100)
})
const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
})

// 生成维护步骤
const generateMaintenanceSteps = (heatNo: string) => {
  return [
    {
      title: '查询折罐电文信息',
      description: '通过CMOJ11电文查询折罐电文里的炉号信息',
      sql: `SELECT *\nFROM DB.TBDZPQUEUEREC\nWHERE QUEUEID = 'OJREV'\n  and FORMID = 'CMOJ11'\n  AND DATASTR like '%${heatNo}%'\n  AND DATASTUS = 'F'\nORDER BY RECSYSDATE || RECSYSTIME ASC;`,
      note: '查找包含指定炉号的折罐电文，解析datastr中的heatno和heatnonew信息',
      completed: false,
      expanded: false
    },
    {
      title: '查询原炉号连铸实绩',
      description: '根据heatno查询连铸实绩是否收到',
      sql: `SELECT *\nFROM DB.TBDZPQUEUEREC\nWHERE QUEUEID = 'OJREV'\n  and FORMID = 'CMOJ05'\n  AND DATASTR like '%${heatNo}%'\nORDER BY RECSYSDATE || RECSYSTIME ASC;`,
      note: '请将${heatNo}替换为第1步中解析出的原始heatno值',
      completed: false,
      expanded: false
    },
    {
      title: '查询新炉号连铸实绩',
      description: '根据heatnonew查询连铸实绩是否收到',
      sql: `SELECT *\nFROM DB.TBDZPQUEUEREC\nWHERE QUEUEID = 'OJREV'\n  and FORMID = 'CMOJ05'\n  AND DATASTR like '%heatnonew%'\nORDER BY RECSYSDATE || RECSYSTIME ASC;`,
      note: '请将heatnonew替换为第1步中解析出的新炉号值',
      completed: false,
      expanded: false
    },
    {
      title: '重收CMOJ11电文',
      description: '当连铸实绩收到后，重收CMOJ11的电文',
      url: 'https://jgerp.jggroup.cn/erp/dzp/jsp/dzpjQueue.jsp',
      linkText: '打开电文队列管理',
      note: '在电文队列管理页面中，查询并重收对应的CMOJ11电文',
      completed: false,
      expanded: false
    }
  ]
}

// 显示输入对话框
const showInputDialog = async () => {
  inputDialogVisible.value = true
  // 等待DOM更新后聚焦输入框
  await nextTick()
  if (heatNoInput.value) {
    heatNoInput.value.focus()
  }
}

// 提交表单
const handleSubmit = () => {
  if (!form.value.heatNo.trim()) {
    ElMessage.warning('请输入炉号')
    return
  }
  
  // 关闭输入对话框
  inputDialogVisible.value = false
  
  // 生成维护步骤
  maintenanceSteps.value = generateMaintenanceSteps(form.value.heatNo.trim())
  dialogVisible.value = true
}

// 切换步骤展开状态
const toggleStep = (index: number) => {
  maintenanceSteps.value[index].expanded = !maintenanceSteps.value[index].expanded
}

// 复制SQL到剪贴板
const copySql = async (sql: string) => {
  try {
    await navigator.clipboard.writeText(sql)
    ElMessage.success('SQL语句已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 打开URL
const openUrl = (url: string) => {
  window.open(url, '_blank')
}

// 更新进度
const updateProgress = () => {
  // 进度更新会自动通过计算属性反映
}

// 重置步骤
const resetSteps = () => {
  maintenanceSteps.value.forEach(step => {
    step.completed = false
    step.expanded = false
  })
  // 同时清空输入框
  form.value.heatNo = ''
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.input-tips {
  margin-top: 16px;
}

.maintenance-dialog {
  max-height: 80vh;
}

.maintenance-steps {
  max-height: 70vh;
  overflow-y: auto;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.step-item.completed {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.step-header {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  background-color: #fafafa;
  transition: background-color 0.3s ease;
}

.step-header:hover {
  background-color: #f0f0f0;
}

.step-item.completed .step-header {
  background-color: #f0f9ff;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 16px;
  flex-shrink: 0;
}

.step-item.completed .step-number {
  background-color: #67c23a;
}

.step-title {
  flex: 1;
  margin-right: 16px;
}

.step-title h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.step-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.step-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-content {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background-color: white;
}

.sql-block {
  margin-bottom: 16px;
}

.sql-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.sql-content {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
}

.step-note {
  margin-bottom: 16px;
}

.step-link {
  margin-bottom: 16px;
}

.progress-section {
  margin-top: 20px;
}

.progress-info {
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
