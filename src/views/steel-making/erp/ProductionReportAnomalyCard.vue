<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Tools /></el-icon>
        <span>产量报表数据异常</span>
      </div>
    </template>

    <div class="card-content">
      <p>异常情况基本上为：CMIS04重量调整(入炉)电文接收异常(电文滞后)</p>
      <el-button type="primary" class="action-btn" @click="showInputDialog">
        开始维护
      </el-button>
    </div>

  </el-card>

  <!-- 输入板坯号对话框 -->
  <el-dialog
    v-model="inputDialogVisible"
    title="产量报表数据异常维护"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
  >
      <el-form :model="form" label-width="80px" @submit.prevent="handleSubmit">
        <el-form-item label="板坯号" required>
          <el-input
            v-model="form.slabId"
            placeholder="请输入板坯号，例如：25106053305"
            @keyup.enter="handleSubmit"
            ref="slabIdInput"
          />
        </el-form-item>

        <el-form-item>
          <div class="input-tips">
            <el-alert
              title="请输入需要维护的板坯号，系统将生成完整的维护步骤清单"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="inputDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :disabled="!form.slabId.trim()"
          >
            生成维护步骤
          </el-button>
        </div>
      </template>
    </el-dialog>

  <!-- 维护步骤对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="产量报表数据异常维护步骤"
    width="90%"
    :close-on-click-modal="false"
    class="maintenance-dialog"
    append-to-body
  >
      <div class="maintenance-steps">
        <el-alert
          title="请按照以下步骤依次执行维护操作"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;"
        />

        <div class="steps-container">
          <div 
            v-for="(step, index) in maintenanceSteps" 
            :key="index"
            class="step-item"
            :class="{ 'completed': step.completed }"
          >
            <div class="step-header" @click="toggleStep(index)">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-title">
                <h4>{{ step.title }}</h4>
                <p class="step-description">{{ step.description }}</p>
              </div>
              <div class="step-actions">
                <el-checkbox 
                  v-model="step.completed" 
                  @change="updateProgress"
                  size="large"
                >
                  完成
                </el-checkbox>
                <el-button 
                  v-if="step.sql"
                  size="small" 
                  type="primary" 
                  @click.stop="copySql(step.sql)"
                >
                  复制SQL
                </el-button>
                <el-button 
                  v-if="step.url"
                  size="small" 
                  type="success" 
                  @click.stop="openUrl(step.url)"
                >
                  打开链接
                </el-button>
              </div>
            </div>
            
            <div v-if="step.expanded" class="step-content">
              <div v-if="step.sql" class="sql-block">
                <div class="sql-header">
                  <span>SQL语句：</span>
                  <el-button size="small" @click="copySql(step.sql)">复制</el-button>
                </div>
                <pre class="sql-content">{{ step.sql }}</pre>
              </div>
              
              <div v-if="step.note" class="step-note">
                <el-alert :title="step.note" type="warning" :closable="false" />
              </div>
              
              <div v-if="step.url" class="step-link">
                <el-button type="success" @click="openUrl(step.url)">
                  <el-icon><Link /></el-icon>
                  {{ step.linkText || '打开链接' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="progress-section">
          <el-divider />
          <div class="progress-info">
            <span>完成进度：{{ completedSteps }}/{{ totalSteps }}</span>
            <el-progress 
              :percentage="progressPercentage" 
              :color="progressColor"
              style="margin-top: 10px;"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetSteps">重置步骤</el-button>
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Link, Tools } from '@element-plus/icons-vue'

// 表单数据
const form = ref({
  slabId: ''
})

// 对话框状态
const inputDialogVisible = ref(false)
const dialogVisible = ref(false)

// 输入框引用
const slabIdInput = ref()

// 维护步骤数据
const maintenanceSteps = ref<Array<{
  title: string
  description: string
  sql?: string
  note?: string
  url?: string
  linkText?: string
  completed: boolean
  expanded: boolean
}>>([])

// 计算属性
const totalSteps = computed(() => maintenanceSteps.value.length)
const completedSteps = computed(() => maintenanceSteps.value.filter(step => step.completed).length)
const progressPercentage = computed(() => {
  if (totalSteps.value === 0) return 0
  return Math.round((completedSteps.value / totalSteps.value) * 100)
})
const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
})

// 生成维护步骤
const generateMaintenanceSteps = (slabId: string) => {
  return [
    {
      title: '检查板坯是否存在',
      description: '查询TBIS102表确认板坯记录',
      sql: `select *\nfrom TBIS102\nwhere SLABID = '${slabId}';`,
      completed: false,
      expanded: false
    },
    {
      title: '修改板坯状态',
      description: '将STATUS改为14，AREAID设为null',
      sql: `update TBIS102\nset STATUS = 14,\n    AREAID = null\nwhere SLABID = '${slabId}';`,
      completed: false,
      expanded: false
    },
    {
      title: '查询最后一笔异常电文',
      description: '查找最新的异常电文记录',
      sql: `select *\nfrom (SELECT t.*, ROW_NUMBER() OVER (ORDER BY t.RECSYSDATE DESC,t.RECSYSTIME desc) AS rn\n      FROM DB.TBDZPQUEUEREC t\n      WHERE t.QUEUEID like '%REV'\n        and t.RECSYSDATE >= '20250605'\n        and t.FORMID = 'CMIS04'\n        and t.DATASTUS = 'F'\n        and t.DATASTR like '%${slabId}%'\n      order by t.RECSYSDATE || t.RECSYSTIME) s\nWHERE rn = 1;`,
      completed: false,
      expanded: false
    },
    {
      title: '调用接口重收电文',
      description: '在ERP系统中重新接收最后一笔电文',
      url: 'https://jgerp.jggroup.cn/erp/dzp/jsp/dzpjQueue.jsp',
      linkText: '打开电文队列管理',
      note: '请在打开的页面中查找对应的电文记录并重新接收',
      completed: false,
      expanded: false
    },
    {
      title: '还原板坯数据',
      description: '将STATUS改为21，AREAID改为OF',
      sql: `update TBIS102\nset STATUS = 21,\n    AREAID = 'OF'\nwhere SLABID = '${slabId}';`,
      completed: false,
      expanded: false
    },
    {
      title: '查询事务日志数据',
      description: '检查TBISTRANSLOGD中的数据顺序',
      sql: `select *\nfrom DB.TBISTRANSLOGD\nwhere slabid = '${slabId}'\norder by 1, 2;`,
      completed: false,
      expanded: false
    },
    {
      title: '查询OUTFUR的TRANSID',
      description: '找到OUTFUR活动的最新TRANSID',
      sql: `select s.*, s.rn, s.TRANSID\nfrom (select t.*, ROW_NUMBER() OVER (ORDER BY t.transid desc) as rn\n      from DB.TBISTRANSLOGD t\n      where slabid = '${slabId}'\n        and activityid = 'OUTFUR'\n      order by 1, 2) s\nwhere rn = 1;`,
      note: '记录查询结果中的TRANSID值，下一步需要使用',
      completed: false,
      expanded: false
    },
    {
      title: '修改空BATCHNO记录的TRANSID',
      description: '将BATCHNO为空的记录TRANSID改为OUTFUR的TRANSID加1',
      sql: `update TBISTRANSLOGD\nset TRANSID = ? -- 请替换为上一步查询到的TRANSID加1\nwhere slabid = '${slabId}'\n  and BATCHNO = '';`,
      note: '请将?替换为第7步查询到的TRANSID加1的值',
      completed: false,
      expanded: false
    },
    {
      title: '设置BATCHNO为X',
      description: '避免被抛账，将空BATCHNO设为X',
      sql: `update TBISTRANSLOGD\nset BATCHNO = 'X'\nwhere slabid = '${slabId}'\n  and BATCHNO = '';`,
      completed: false,
      expanded: false
    },
    {
      title: '更新重量字段',
      description: '将WGT字段改为电文传递的重量',
      sql: `update TBISTRANSLOGD\nset WGT = '?' -- 请替换为电文中的matactwt值\nwhere slabid = '${slabId}'\n  and BATCHNO = 'X';`,
      note: '请将?替换为电文中的实际重量值(matactwt)',
      completed: false,
      expanded: false
    },
    {
      title: '打开IX收账管理',
      description: '进入IX收账管理页面进行撤回操作',
      url: 'https://jgerp.jggroup.cn/erp/ix/jsp/ixjjA0040.jsp',
      linkText: '打开IX收账管理',
      completed: false,
      expanded: false
    },
    {
      title: '查询并撤回收账记录',
      description: '根据存货编号查询记录并全部撤回',
      note: '在IX收账管理页面中，输入板坯号查询记录，然后全部撤回（注意：只能撤回当月数据）',
      completed: false,
      expanded: false
    },
    {
      title: '检查IPSTUS状态',
      description: '查询并删除IPSTUS为空的记录',
      sql: `SELECT count(*)\nFROM DB.TBIXEACCTLOG\nwhere prodno = '${slabId}'\n  and (IPSTUS is null or IPSTUS = '');\n\ndelete\nfrom DB.TBIXEACCTLOG\nwhere prodno = '${slabId}'\n  and (IPSTUS is null or IPSTUS = '');`,
      completed: false,
      expanded: false
    },
    {
      title: '清空BATCHNO',
      description: '清空当月TBISTRANSLOGD中的BATCHNO',
      sql: `update TBISTRANSLOGD\nset BATCHNO = ''\nwhere slabid = '${slabId}'\n  and BATCHNO = 'X';`,
      note: '注意：只处理当月数据，与IX规则保持一致',
      completed: false,
      expanded: false
    },
    {
      title: '更新IS9051上料量',
      description: '将上料量改为IS102的数据',
      sql: `update TBIS9051\nset UPWGT = (select wgt\n             from TBIS102\n             where SLABID = '${slabId}')\nwhere SLABID = '${slabId}';`,
      completed: false,
      expanded: false
    },
    {
      title: '等待自动抛送',
      description: '等待系统自动抛送数据',
      note: '维护完成后，系统会自动进行数据抛送，请耐心等待',
      completed: false,
      expanded: false
    }
  ]
}

// 显示输入对话框
const showInputDialog = async () => {
  inputDialogVisible.value = true
  // 等待DOM更新后聚焦输入框
  await nextTick()
  if (slabIdInput.value) {
    slabIdInput.value.focus()
  }
}

// 提交表单
const handleSubmit = () => {
  if (!form.value.slabId.trim()) {
    ElMessage.warning('请输入板坯号')
    return
  }

  // 关闭输入对话框
  inputDialogVisible.value = false

  // 生成维护步骤
  maintenanceSteps.value = generateMaintenanceSteps(form.value.slabId.trim())
  dialogVisible.value = true
}

// 切换步骤展开状态
const toggleStep = (index: number) => {
  maintenanceSteps.value[index].expanded = !maintenanceSteps.value[index].expanded
}

// 复制SQL到剪贴板
const copySql = async (sql: string) => {
  try {
    await navigator.clipboard.writeText(sql)
    ElMessage.success('SQL语句已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 打开URL
const openUrl = (url: string) => {
  window.open(url, '_blank')
}

// 更新进度
const updateProgress = () => {
  // 进度更新会自动通过计算属性反映
}

// 重置步骤
const resetSteps = () => {
  maintenanceSteps.value.forEach(step => {
    step.completed = false
    step.expanded = false
  })
  // 同时清空输入框
  form.value.slabId = ''
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.input-tips {
  margin-top: 16px;
}

.maintenance-dialog {
  max-height: 80vh;
}

.maintenance-steps {
  max-height: 70vh;
  overflow-y: auto;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.step-item.completed {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.step-header {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;
  background-color: #fafafa;
  transition: background-color 0.3s ease;
}

.step-header:hover {
  background-color: #f0f0f0;
}

.step-item.completed .step-header {
  background-color: #f0f9ff;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 16px;
  flex-shrink: 0;
}

.step-item.completed .step-number {
  background-color: #67c23a;
}

.step-title {
  flex: 1;
  margin-right: 16px;
}

.step-title h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.step-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.step-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.step-content {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  background-color: white;
}

.sql-block {
  margin-bottom: 16px;
}

.sql-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.sql-content {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 6px;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
}

.step-note {
  margin-bottom: 16px;
}

.step-link {
  margin-bottom: 16px;
}

.progress-section {
  margin-top: 20px;
}

.progress-info {
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
