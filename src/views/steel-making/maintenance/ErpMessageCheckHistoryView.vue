<template>
  <div class="maintenance-history-container">
    <div class="page-header">
      <h2>ERP维护 - 电文检查</h2>
      <p class="page-description">查看ERP电文检查任务的执行历史和结果</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryForm" inline>
        <el-form-item label="任务状态">
          <el-select v-model="queryForm.status" placeholder="请选择状态" clearable style="width: 150px">
            <el-option label="全部" value="" />
            <el-option label="待执行" value="待执行" />
            <el-option label="执行中" value="执行中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="失败" value="执行失败" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleRefresh">刷新</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="id" label="任务ID" width="80" />
        <el-table-column prop="task_name" label="任务名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="result_status" label="结果状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getResultStatusTagType(scope.row.result_status)"
              size="small"
            >
              {{ scope.row.result_status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="started_at" label="开始时间" width="160">
          <template #default="scope">
            {{ scope.row.started_at ? formatDateTime(scope.row.started_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="completed_at" label="完成时间" width="160">
          <template #default="scope">
            {{ scope.row.completed_at ? formatDateTime(scope.row.completed_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="执行时长" width="100">
          <template #default="scope">
            {{ scope.row.duration ? formatDuration(scope.row.duration) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_by_name" label="创建人" width="100">
          <template #default="scope">
            {{ scope.row.created_by_name || (scope.row.created_by === 0 ? '系统' : '未知') }}
          </template>
        </el-table-column>
        <el-table-column prop="error_message" label="错误信息" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.error_message || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDetails(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryForm.page"
          v-model:page-size="queryForm.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="电文检查任务详情"
      width="80%"
      :close-on-click-modal="false"
      append-to-body
    >
      <div v-if="taskDetails" class="task-details">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <span class="detail-card-title">基本信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务ID">{{ taskDetails.id }}</el-descriptions-item>
            <el-descriptions-item label="任务名称">{{ taskDetails.task_name }}</el-descriptions-item>
            <el-descriptions-item label="任务类型">{{ taskDetails.task_type }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(taskDetails.status)">
                {{ getStatusText(taskDetails.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="检查日期">{{ taskDetails.check_date || '-' }}</el-descriptions-item>
            <el-descriptions-item label="电文类型">{{ taskDetails.message_type || '-' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDateTime(taskDetails.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="开始时间">{{ taskDetails.started_at ? formatDateTime(taskDetails.started_at) : '-' }}</el-descriptions-item>
            <el-descriptions-item label="完成时间">{{ taskDetails.completed_at ? formatDateTime(taskDetails.completed_at) : '-' }}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{ taskDetails.created_by_name || '-' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 执行统计 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <span class="detail-card-title">执行统计</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskDetails.progress || 0 }}%</div>
                <div class="stat-label">执行进度</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskDetails.total_records || 0 }}</div>
                <div class="stat-label">总记录数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskDetails.processed_records || 0 }}</div>
                <div class="stat-label">已处理记录</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskDetails.error_count || 0 }}</div>
                <div class="stat-label">错误记录数</div>
              </div>
            </el-col>
          </el-row>
          <div v-if="taskDetails.duration" class="duration-info">
            <el-tag type="info">执行时长: {{ formatDuration(taskDetails.duration) }}</el-tag>
          </div>
        </el-card>

        <!-- 错误信息 -->
        <el-card v-if="taskDetails.error_message" class="detail-card" shadow="never">
          <template #header>
            <span class="detail-card-title">错误信息</span>
          </template>
          <el-alert
            :title="taskDetails.error_message"
            type="error"
            :closable="false"
            show-icon
          />
        </el-card>

        <!-- 检查结果 -->
        <el-card v-if="taskDetails.check_result" class="detail-card" shadow="never">
          <template #header>
            <div class="detail-card-header">
              <span class="detail-card-title">检查结果</span>
              <el-button type="primary" size="small" @click="showCheckResults">查看详细结果</el-button>
            </div>
          </template>
          <div class="check-summary">
            <p>点击"查看详细结果"按钮查看完整的检查结果数据</p>
          </div>
        </el-card>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { maintenanceHistoryApi, type TaskHistoryQuery, type TaskHistoryItem, type TaskDetails } from '../../../api/maintenanceHistory'

// 从父组件注入结果显示方法
const showMessageCheckResultsFromParent = inject('showMessageCheckResults') as ((data: any) => void) | undefined

// 响应式数据
const loading = ref(false)
const tableData = ref<TaskHistoryItem[]>([])
const total = ref(0)
const dateRange = ref<[string, string] | null>(null)

// 详情对话框相关
const detailDialogVisible = ref(false)
const taskDetails = ref<TaskDetails | null>(null)

// 查询表单
const queryForm = reactive<TaskHistoryQuery>({
  status: '',
  page: 1,
  page_size: 20
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: TaskHistoryQuery = {
      ...queryForm
    }
    
    if (dateRange.value) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const response = await maintenanceHistoryApi.getErpMessageCheckHistory(params)
    
    if (response.code === 200) {
      tableData.value = response.data.items
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryForm.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  queryForm.status = ''
  queryForm.page = 1
  dateRange.value = null
  fetchData()
}

// 刷新
const handleRefresh = () => {
  fetchData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  queryForm.page_size = size
  queryForm.page = 1
  fetchData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  queryForm.page = page
  fetchData()
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    '待执行': 'info',
    '执行中': 'warning',
    '已完成': 'success',
    '执行失败': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  return status
}

// 获取结果状态标签类型
const getResultStatusTagType = (resultStatus: string) => {
  return resultStatus === '有异常' ? 'danger' : 'success'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化执行时长
const formatDuration = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}秒`
  } else if (seconds < 3600) {
    return `${(seconds / 60).toFixed(1)}分钟`
  } else {
    return `${(seconds / 3600).toFixed(1)}小时`
  }
}

// 查看详情
const viewDetails = async (row: TaskHistoryItem) => {
  try {
    const response = await maintenanceHistoryApi.getTaskDetails(row.id, row.task_type)
    if (response.code === 200) {
      taskDetails.value = response.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取任务详情失败')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  }
}

// 显示检查结果详情
const showCheckResults = () => {
  if (taskDetails.value?.check_result && showMessageCheckResultsFromParent) {
    showMessageCheckResultsFromParent(taskDetails.value.check_result)
  } else {
    ElMessage.warning('暂无检查结果数据')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.maintenance-history-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 任务详情样式 */
.task-details {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card-title {
  font-weight: 600;
  color: #303133;
}

.detail-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.duration-info {
  margin-top: 20px;
  text-align: center;
}

.check-summary {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  text-align: center;
  color: #606266;
}
</style>
