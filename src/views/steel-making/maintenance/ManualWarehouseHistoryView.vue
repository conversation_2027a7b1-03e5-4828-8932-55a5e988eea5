<template>
  <div class="history-container">
    <div class="header">
      <h2>人工入库历史</h2>
    </div>
    
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="queryForm" inline>
        <el-form-item label="LES编号">
          <el-input
            v-model="queryForm.lesNo"
            placeholder="请输入LES编号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="物料编码">
          <el-input
            v-model="queryForm.materialCode"
            placeholder="请输入物料编码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="工厂">
          <el-select v-model="queryForm.factory" placeholder="请选择工厂" clearable style="width: 120px">
            <el-option label="全部" value="" />
            <el-option label="一厂" value="L1" />
            <el-option label="二厂" value="L2" />
          </el-select>
        </el-form-item>
        <el-form-item label="入库日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" stripe>
        <el-table-column prop="lesNo" label="LES编号" width="150" />
        <el-table-column prop="materialCode" label="物料编码" width="120" />
        <el-table-column prop="materialName" label="物料名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="weight" label="重量(吨)" width="100">
          <template #default="scope">
            {{ scope.row.weight.toFixed(3) }}
          </template>
        </el-table-column>
        <el-table-column prop="factory" label="工厂" width="80">
          <template #default="scope">
            {{ scope.row.factory === 'L1' ? '一厂' : '二厂' }}
          </template>
        </el-table-column>
        <el-table-column prop="warehouseDate" label="入库日期" width="120" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column prop="createdBy" label="创建人" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.status === 'success' ? 'success' : scope.row.status === 'failed' ? 'danger' : 'warning'"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewDetail(scope.row)">详情</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteRecord(scope.row)"
              :disabled="scope.row.status === 'success'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryForm.page"
          v-model:page-size="queryForm.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="入库记录详情"
      width="600px"
      append-to-body
    >
      <div v-if="currentRecord" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="LES编号" :span="2">
            <el-tag type="success" size="large">{{ currentRecord.lesNo }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="物料编码">{{ currentRecord.materialCode }}</el-descriptions-item>
          <el-descriptions-item label="物料名称">{{ currentRecord.materialName }}</el-descriptions-item>
          <el-descriptions-item label="重量">{{ currentRecord.weight.toFixed(3) }} 吨</el-descriptions-item>
          <el-descriptions-item label="工厂">{{ currentRecord.factory === 'L1' ? '一厂' : '二厂' }}</el-descriptions-item>
          <el-descriptions-item label="入库日期">{{ currentRecord.warehouseDate }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentRecord.createdAt }}</el-descriptions-item>
          <el-descriptions-item label="创建人">{{ currentRecord.createdBy }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag 
              :type="currentRecord.status === 'success' ? 'success' : currentRecord.status === 'failed' ? 'danger' : 'warning'"
            >
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { manualWarehouseApi, type WarehouseHistoryQuery, type WarehouseHistoryItem } from '../../../api/manualWarehouse'

// 响应式数据
const loading = ref(false)
const tableData = ref<WarehouseHistoryItem[]>([])
const total = ref(0)
const dateRange = ref<[string, string] | null>(null)
const detailDialogVisible = ref(false)
const currentRecord = ref<WarehouseHistoryItem | null>(null)

// 查询表单
const queryForm = reactive<WarehouseHistoryQuery>({
  lesNo: '',
  materialCode: '',
  factory: '',
  page: 1,
  pageSize: 20
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params: WarehouseHistoryQuery = {
      ...queryForm
    }
    
    if (dateRange.value) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }
    
    const response = await manualWarehouseApi.getWarehouseHistory(params)
    
    if (response.code === 200) {
      tableData.value = response.data.items
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryForm.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  queryForm.lesNo = ''
  queryForm.materialCode = ''
  queryForm.factory = ''
  queryForm.page = 1
  dateRange.value = null
  fetchData()
}

// 查看详情
const viewDetail = (record: WarehouseHistoryItem) => {
  currentRecord.value = record
  detailDialogVisible.value = true
}

// 删除记录
const deleteRecord = async (record: WarehouseHistoryItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除LES编号为 ${record.lesNo} 的入库记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await manualWarehouseApi.deleteWarehouseRecord(record.id)
    
    if (response.code === 200) {
      ElMessage.success('删除成功')
      fetchData()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'success':
      return '成功'
    case 'failed':
      return '失败'
    case 'pending':
      return '处理中'
    default:
      return '未知'
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.history-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.detail-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
