<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><DataAnalysis /></el-icon>
        <span>转炉无法选择废钢号</span>
      </div>
    </template>
    <div class="card-content">
      <p>修复转炉无法选择废钢号问题</p>
      <el-button type="primary" class="action-btn" @click="showDialog">修复</el-button>
    </div>

    <!-- 转炉无法选择废钢号对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="转炉无法选择废钢号"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="炉次号" prop="heatId">
          <el-input
            v-model="form.heatId"
            placeholder="请输入炉次号，例如：L12100021"
            style="width: 100%"
            maxlength="20"
            show-word-limit
          />
          <div class="form-tip">
            <el-text size="small" type="warning">
              此操作将清空指定炉次的BO_CSMTWO字段，并返回原始值
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="isRunning">取消</el-button>
          <el-button type="primary" @click="startClear" :loading="isRunning">
            {{ isRunning ? '处理中...' : '开始处理' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { DataAnalysis } from '@element-plus/icons-vue'
import { mesMaintenanceApi, type BofScrapSteelClearRequest } from '../../../api/mesMaintenance'
import { useProgressDialog } from '../composables/useProgressDialog'
import { useWebSocket } from '../composables/useWebSocket'

// 对话框状态
const dialogVisible = ref(false)
const isRunning = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  heatId: ''
})

const rules = {
  heatId: [{ required: true, message: '请输入炉次号', trigger: 'blur' }]
}

// 使用进度对话框组合式函数
const { showProgressDialog, updateProgress, setCompleted, setFailed, closeProgressDialog } = useProgressDialog()

// 使用WebSocket组合式函数
const { connectWebSocket, disconnectWebSocket } = useWebSocket()

// 显示对话框
const showDialog = () => {
  // 清空表单
  form.heatId = ''
  dialogVisible.value = true
}

// 开始处理
const startClear = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 确认对话框
    await ElMessageBox.confirm(
      `确定要处理炉次号 ${form.heatId} 的转炉无法选择废钢号问题吗？此操作将清空BO_CSMTWO字段。`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    isRunning.value = true

    const request: BofScrapSteelClearRequest = {
      heat_id: form.heatId
    }

    const response = await mesMaintenanceApi.bofScrapSteelClear(request)

    if (response.code === 200) {
      const taskId = response.data.task_id
      dialogVisible.value = false
      
      // 显示进度对话框
      showProgressDialog('转炉无法选择废钢号处理进度', '转炉无法选择废钢号处理任务已启动，正在初始化...')
      
      // 建立WebSocket连接
      connectWebSocket(
        taskId,
        mesMaintenanceApi.createWebSocket,
        (message) => {
          if (message.type === 'task_progress') {
            // 更新进度
            updateProgress(message.progress || 0, message.message || '')

            if (message.progress === 100) {
              isRunning.value = false

              // 显示原始BO_CSMTWO值
              if (message.data && message.data.original_bo_csmtwo !== undefined) {
                const originalValue = message.data.original_bo_csmtwo || '(空值)'
                const resultMessage = `转炉无法选择废钢号处理完成！原BO_CSMTWO值: ${originalValue}`
                setCompleted(resultMessage)
                ElMessage.success(resultMessage)
              } else {
                setCompleted('转炉无法选择废钢号处理完成')
                ElMessage.success('转炉无法选择废钢号处理完成')
              }

              // 3秒后自动关闭进度对话框
              setTimeout(() => {
                closeProgressDialog()
                disconnectWebSocket()
              }, 3000)
            }
          } else if (message.type === 'task_completed') {
            isRunning.value = false

            if (message.success) {
              // 显示原始BO_CSMTWO值
              if (message.data && message.data.original_bo_csmtwo !== undefined) {
                const originalValue = message.data.original_bo_csmtwo || '(空值)'
                const resultMessage = `转炉无法选择废钢号处理完成！原BO_CSMTWO值: ${originalValue}`
                setCompleted(resultMessage)
                ElMessage.success(resultMessage)
              } else {
                setCompleted(message.result_summary || '转炉无法选择废钢号处理完成')
                ElMessage.success('转炉无法选择废钢号处理完成')
              }
            } else {
              setFailed(message.result_summary || '转炉无法选择废钢号处理失败')
              ElMessage.error('转炉无法选择废钢号处理失败')
            }

            // 3秒后自动关闭进度对话框
            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        }
      )

      ElMessage.success('转炉无法选择废钢号处理任务已启动')
    } else {
      ElMessage.error(response.msg || '启动任务失败')
      isRunning.value = false
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('启动转炉无法选择废钢号处理失败:', error)
      ElMessage.error(error.response?.data?.msg || error.message || '启动任务失败')
    }
    isRunning.value = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-tip {
  margin-top: 8px;
}
</style>
