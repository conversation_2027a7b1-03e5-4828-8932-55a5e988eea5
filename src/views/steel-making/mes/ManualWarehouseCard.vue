<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Box /></el-icon>
        <span>人工入库</span>
      </div>
    </template>

    <div class="card-content">
      <p>手动创建物料入库记录，支持物料选择、重量录入和工厂选择</p>
      <el-button type="primary" class="action-btn" @click="showDialog">
        开始入库
      </el-button>
    </div>
  </el-card>

  <!-- 人工入库对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="人工入库"
    width="600px"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="物料信息" prop="materialCode">
        <el-select
          v-model="form.materialCode"
          placeholder="请选择物料"
          filterable
          :loading="materialLoading"
          style="width: 100%"
          @change="onMaterialChange"
          @focus="loadAllMaterials"
        >
          <el-option
            v-for="item in materialOptions"
            :key="item.MATE_CODE"
            :label="`${item.MATE_CODE} - ${item.MATE_NAME}`"
            :value="item.MATE_CODE"
          />
        </el-select>
        <div v-if="selectedMaterial" class="material-info">
          <el-text size="small" type="info">
            已选择：{{ selectedMaterial.MATE_CODE }} - {{ selectedMaterial.MATE_NAME }}
          </el-text>
        </div>
      </el-form-item>

      <el-form-item label="入库日期" prop="warehouseDate">
        <el-date-picker
          v-model="form.warehouseDate"
          type="date"
          placeholder="选择入库日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="重量(吨)" prop="weight">
        <el-input-number
          v-model="form.weight"
          :precision="3"
          :step="0.001"
          :min="0"
          placeholder="请输入重量"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="工厂" prop="factory">
        <el-radio-group v-model="form.factory">
          <el-radio label="L1">一厂</el-radio>
          <el-radio label="L2">二厂</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 参考数据显示 -->
      <el-form-item v-if="referenceData" label="参考数据">
        <el-card class="reference-card">
          <div class="reference-info">
            <p><strong>LES编号：</strong>{{ referenceData.LES_NO }}</p>
            <p><strong>物料编码：</strong>{{ referenceData.MAT_CODE }}</p>
            <p><strong>工厂：</strong>{{ referenceData.WORK_SHOP }}</p>
            <p><strong>净重：</strong>{{ referenceData.SUTTLE }}</p>
            <p><strong>入库时间：</strong>{{ referenceData.SUTTLE_TIME }}</p>
          </div>
        </el-card>
      </el-form-item>

      <!-- 预览生成的LES编号 -->
      <el-form-item v-if="previewLesNo" label="生成编号">
        <el-input v-model="previewLesNo" readonly>
          <template #prepend>LES编号</template>
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" :disabled="isSubmitting">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="isSubmitting">
          {{ isSubmitting ? '入库中...' : '确认入库' }}
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 结果对话框 -->
  <el-dialog
    v-model="resultDialogVisible"
    title="入库完成"
    width="500px"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="result-content">
      <el-result icon="success" title="入库成功">
        <template #sub-title>
          <p>物料已成功入库到MES系统</p>
        </template>
        <template #extra>
          <div class="result-info">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="LES编号">
                <el-tag type="success" size="large">{{ resultLesNo }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="物料编码">{{ form.materialCode }}</el-descriptions-item>
              <el-descriptions-item label="物料名称">{{ selectedMaterial?.MATE_NAME }}</el-descriptions-item>
              <el-descriptions-item label="重量">{{ form.weight }} 吨</el-descriptions-item>
              <el-descriptions-item label="工厂">{{ form.factory === 'L1' ? '一厂' : '二厂' }}</el-descriptions-item>
              <el-descriptions-item label="入库日期">{{ form.warehouseDate }}</el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="result-actions">
            <el-button type="primary" @click="copyLesNo">复制LES编号</el-button>
            <el-button @click="resetForm">继续入库</el-button>
          </div>
        </template>
      </el-result>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="resultDialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Box } from '@element-plus/icons-vue'
import { manualWarehouseApi, type MaterialInfo, type ManualWarehouseRequest } from '../../../api/manualWarehouse'

// 对话框状态
const dialogVisible = ref(false)
const resultDialogVisible = ref(false)
const isSubmitting = ref(false)

// 表单引用和数据
const formRef = ref<FormInstance>()
const form = reactive({
  materialCode: '',
  warehouseDate: '',
  weight: 0,
  factory: 'L1'
})

// 表单验证规则
const rules = {
  materialCode: [{ required: true, message: '请选择物料', trigger: 'change' }],
  warehouseDate: [{ required: true, message: '请选择入库日期', trigger: 'change' }],
  weight: [
    { required: true, message: '请输入重量', trigger: 'blur' },
    { type: 'number', min: 0.001, message: '重量必须大于0', trigger: 'blur' }
  ],
  factory: [{ required: true, message: '请选择工厂', trigger: 'change' }]
}

// 物料相关数据
const materialOptions = ref<MaterialInfo[]>([])
const materialLoading = ref(false)
const selectedMaterial = ref<MaterialInfo | null>(null)

// 参考数据和结果
const referenceData = ref<any>(null)
const resultLesNo = ref('')

// 预览LES编号
const previewLesNo = computed(() => {
  if (form.warehouseDate) {
    const date = form.warehouseDate.replace(/-/g, '')
    return `${date}0001` // 这里显示预览，实际编号会在后端生成
  }
  return ''
})

// 显示对话框
const showDialog = () => {
  // 设置默认值
  const today = new Date().toISOString().split('T')[0]
  form.warehouseDate = today
  form.factory = 'L1'
  form.weight = 0
  form.materialCode = ''
  
  // 清空之前的数据
  selectedMaterial.value = null
  referenceData.value = null
  materialOptions.value = []
  
  dialogVisible.value = true
}

// 加载所有物料
const loadAllMaterials = async () => {
  if (materialOptions.value.length > 0) {
    return // 已经加载过了
  }

  materialLoading.value = true
  try {
    const response = await manualWarehouseApi.getAllMaterials()
    if (response.code === 200) {
      materialOptions.value = response.data
    }
  } catch (error) {
    console.error('加载物料失败:', error)
    ElMessage.error('加载物料失败')
  } finally {
    materialLoading.value = false
  }
}

// 物料选择变化
const onMaterialChange = async (materialCode: string) => {
  const material = materialOptions.value.find(item => item.MATE_CODE === materialCode)
  selectedMaterial.value = material || null
  
  if (materialCode && form.factory) {
    await loadReferenceData()
  }
}

// 加载参考数据
const loadReferenceData = async () => {
  if (!form.materialCode || !form.factory) return
  
  try {
    const response = await manualWarehouseApi.getReferenceData(form.materialCode, form.factory)
    if (response.code === 200) {
      referenceData.value = response.data
    }
  } catch (error) {
    console.error('获取参考数据失败:', error)
    // 不显示错误消息，因为可能是第一次入库该物料
    referenceData.value = null
  }
}

// 监听工厂变化
watch(() => form.factory, () => {
  if (form.materialCode) {
    loadReferenceData()
  }
})

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    isSubmitting.value = true
    
    const request: ManualWarehouseRequest = {
      materialCode: form.materialCode,
      warehouseDate: form.warehouseDate,
      weight: form.weight,
      factory: form.factory
    }
    
    const response = await manualWarehouseApi.createWarehouseRecord(request)
    
    if (response.code === 200) {
      resultLesNo.value = response.data.lesNo
      dialogVisible.value = false
      resultDialogVisible.value = true
      ElMessage.success('入库成功')
    } else {
      ElMessage.error(response.msg || '入库失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('入库失败:', error)
      ElMessage.error(error.response?.data?.msg || error.message || '入库失败')
    }
  } finally {
    isSubmitting.value = false
  }
}

// 复制LES编号
const copyLesNo = async () => {
  try {
    await navigator.clipboard.writeText(resultLesNo.value)
    ElMessage.success('LES编号已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 重置表单
const resetForm = () => {
  resultDialogVisible.value = false
  showDialog()
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.material-info {
  margin-top: 8px;
}

.reference-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.reference-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.result-content {
  text-align: center;
}

.result-info {
  margin: 20px 0;
}

.result-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
