<template>
  <div class="tab-content">
    <el-row :gutter="24">
      <el-col :span="6">
        <!-- MES电文检查 -->
        <MesMessageCheckCard />
      </el-col>
      <el-col :span="6">
        <!-- 更新钢坯产量调拨汇总报表 -->
        <UpdateProdResSummaryCard />
      </el-col>
      <el-col :span="6">
        <!-- 转炉无法选择废钢号 -->
        <BofScrapSteelClearCard />
      </el-col>
      <el-col :span="6">
        <!-- 物料人工入库 -->
        <ManualWarehouseCard />
      </el-col>
    </el-row>
    <el-row :gutter="24">
      <el-col :span="6">
        <!-- 废钢投料信息对比 -->
        <ScrapSteelComparisonCard />
      </el-col>
    </el-row>

  </div>
</template>

<script setup lang="ts">
import UpdateProdResSummaryCard from './UpdateProdResSummaryCard.vue'
import BofScrapSteelClearCard from './BofScrapSteelClearCard.vue'
import MesMessageCheckCard from './MesMessageCheckCard.vue'
import ManualWarehouseCard from './ManualWarehouseCard.vue'
import ScrapSteelComparisonCard from './ScrapSteelComparisonCard.vue'
</script>

<style scoped>
.tab-content {
  padding: 20px 0;
}
</style>
