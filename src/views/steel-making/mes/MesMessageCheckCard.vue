<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Setting /></el-icon>
        <span>电文检查</span>
      </div>
    </template>
    <div class="card-content">
      <p>检查MES收到或者发送的电文是否有失败情况</p>
      <el-button type="primary" class="action-btn" @click="showDialog">检查</el-button>
    </div>

    <!-- MES电文检查对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="MES电文检查"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="检查日期" prop="checkDate">
          <el-date-picker
            v-model="form.checkDate"
            type="date"
            placeholder="选择检查日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="电文类型" prop="messageType">
          <el-select v-model="form.messageType" placeholder="请选择电文类型" style="width: 100%">
            <el-option label="接收电文" value="receive" />
            <el-option label="发送电文" value="send" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="isRunning">取消</el-button>
          <el-button type="primary" @click="startCheck" :loading="isRunning">
            {{ isRunning ? '检查中...' : '开始检查' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { Setting } from '@element-plus/icons-vue'
import { mesMessageCheckApi, type MESMessageCheckRequest } from '../../../api'
import { useProgressDialog } from '../composables/useProgressDialog'
import { useWebSocket } from '../composables/useWebSocket'

// 从父组件注入结果显示方法
const showMessageCheckResults = inject('showMessageCheckResults') as (data: any) => void

// 对话框状态
const dialogVisible = ref(false)
const isRunning = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  checkDate: '',
  messageType: ''
})

const rules = {
  checkDate: [{ required: true, message: '请选择检查日期', trigger: 'change' }],
  messageType: [{ required: true, message: '请选择电文类型', trigger: 'change' }]
}

// 使用进度对话框组合式函数
const { showProgressDialog, updateProgress, setCompleted, setFailed, closeProgressDialog } = useProgressDialog()

// 使用WebSocket组合式函数
const { connectWebSocket, disconnectWebSocket } = useWebSocket()

// 显示对话框
const showDialog = () => {
  // 设置默认值
  const today = new Date().toISOString().split('T')[0]
  form.checkDate = today
  form.messageType = 'receive'
  dialogVisible.value = true
}

// 开始检查
const startCheck = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    isRunning.value = true

    const request: MESMessageCheckRequest = {
      check_date: form.checkDate,
      message_type: form.messageType
    }

    const response = await mesMessageCheckApi.startCheck(request)

    if (response.code === 200) {
      const taskId = response.data.task_id
      dialogVisible.value = false
      
      // 显示进度对话框
      showProgressDialog('MES电文检查进度', 'MES电文检查任务已启动，正在初始化...')
      
      // 建立WebSocket连接
      connectWebSocket(
        taskId,
        mesMessageCheckApi.createWebSocket,
        (message) => {
          if (message.type === 'task_progress') {
            // 更新进度
            updateProgress(message.progress || 0, message.message || '')

            if (message.progress === 100 && message.data) {
              isRunning.value = false

              // 显示检查结果
              const errorCount = message.data.summary?.error_count || 0
              const systemName = message.data.summary?.system || 'MES'
              const resultMessage = `${systemName}电文检查完成，发现 ${errorCount} 条错误电文`

              setCompleted(resultMessage)
              ElMessage.success(resultMessage)

              // 显示检查结果
              if (message.data) {
                showMessageCheckResults(message.data)
              }

              // 3秒后自动关闭进度对话框
              setTimeout(() => {
                closeProgressDialog()
                disconnectWebSocket()
              }, 3000)
            }
          } else if (message.type === 'task_completed') {
            isRunning.value = false

            if (message.success) {
              setCompleted(message.result_summary || 'MES电文检查完成')
              ElMessage.success('MES电文检查完成')
            } else {
              setFailed(message.result_summary || 'MES电文检查失败')
              ElMessage.error('MES电文检查失败')
            }

            // 3秒后自动关闭进度对话框
            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        }
      )

      ElMessage.success('MES电文检查任务已启动')
    } else {
      ElMessage.error(response.msg || '启动任务失败')
      isRunning.value = false
    }
  } catch (error) {
    console.error('启动MES电文检查失败:', error)
    ElMessage.error('启动MES电文检查失败')
    isRunning.value = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
