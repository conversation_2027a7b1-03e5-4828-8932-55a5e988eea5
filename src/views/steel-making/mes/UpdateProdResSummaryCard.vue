<template>
  <el-card class="function-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><DataAnalysis /></el-icon>
        <span>更新钢坯产量调拨汇总报表</span>
      </div>
    </template>
    <div class="card-content">
      <p>当钢坯产量调拨明细表和汇总表对不上的时候使用</p>
      <el-button type="primary" class="action-btn" @click="showDialog">更新</el-button>
    </div>

    <!-- 更新钢坯产量调拨汇总报表对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="更新钢坯产量调拨汇总报表"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="选择日期" prop="date">
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
          <div class="form-tip">
            <el-text size="small" type="warning">
              此操作将删除指定日期的现有数据并重新生成汇总报表
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="isRunning">取消</el-button>
          <el-button type="primary" @click="startUpdate" :loading="isRunning">
            {{ isRunning ? '更新中...' : '开始更新' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { DataAnalysis } from '@element-plus/icons-vue'
import { mesMaintenanceApi, type UpdateProdResSummaryRequest } from '../../../api'
import { useProgressDialog } from '../composables/useProgressDialog'
import { useWebSocket } from '../composables/useWebSocket'

// 对话框状态
const dialogVisible = ref(false)
const isRunning = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const form = reactive({
  date: ''
})

const rules = {
  date: [{ required: true, message: '请选择日期', trigger: 'change' }]
}

// 使用进度对话框组合式函数
const { showProgressDialog, updateProgress, setCompleted, setFailed, closeProgressDialog } = useProgressDialog()

// 使用WebSocket组合式函数
const { connectWebSocket, disconnectWebSocket } = useWebSocket()

// 显示对话框
const showDialog = () => {
  // 设置默认值为今天
  const today = new Date().toISOString().split('T')[0]
  form.date = today
  dialogVisible.value = true
}

// 开始更新
const startUpdate = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 确认对话框
    await ElMessageBox.confirm(
      `确定要更新 ${form.date} 的钢坯产量调拨汇总报表吗？此操作将删除现有数据并重新生成。`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    isRunning.value = true

    const request: UpdateProdResSummaryRequest = {
      date: form.date
    }

    const response = await mesMaintenanceApi.updateProdResSummary(request)

    if (response.code === 200) {
      const taskId = response.data.task_id
      dialogVisible.value = false
      
      // 显示进度对话框
      showProgressDialog('更新钢坯产量调拨汇总报表进度', '更新钢坯产量调拨汇总报表任务已启动，正在初始化...')
      
      // 建立WebSocket连接
      connectWebSocket(
        taskId,
        mesMaintenanceApi.createWebSocket,
        (message) => {
          if (message.type === 'task_progress') {
            // 更新进度
            updateProgress(message.progress || 0, message.message || '')

            if (message.progress === 100) {
              isRunning.value = false
              setCompleted('更新钢坯产量调拨汇总报表完成')
              ElMessage.success('更新钢坯产量调拨汇总报表完成')

              // 3秒后自动关闭进度对话框
              setTimeout(() => {
                closeProgressDialog()
                disconnectWebSocket()
              }, 3000)
            }
          } else if (message.type === 'task_completed') {
            isRunning.value = false

            if (message.success) {
              setCompleted(message.result_summary || '更新钢坯产量调拨汇总报表完成')
              ElMessage.success('更新钢坯产量调拨汇总报表完成')
            } else {
              setFailed(message.result_summary || '更新钢坯产量调拨汇总报表失败')
              ElMessage.error('更新钢坯产量调拨汇总报表失败')
            }

            // 3秒后自动关闭进度对话框
            setTimeout(() => {
              closeProgressDialog()
              disconnectWebSocket()
            }, 3000)
          }
        }
      )

      ElMessage.success('更新钢坯产量调拨汇总报表任务已启动')
    } else {
      ElMessage.error(response.msg || '启动任务失败')
      isRunning.value = false
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('启动更新钢坯产量调拨汇总报表失败:', error)
      ElMessage.error(error.response?.data?.msg || error.message || '启动任务失败')
    }
    isRunning.value = false
  }
}
</script>

<style scoped>
.function-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}

.function-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.card-content {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-content p {
  color: #606266;
  margin-bottom: 16px;
}

.action-btn {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-tip {
  margin-top: 8px;
}
</style>
