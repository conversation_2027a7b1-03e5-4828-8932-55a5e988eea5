<template>
  <div class="sub-page">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>操作日志</span>
          <el-button type="primary" size="small" @click="exportLogs">导出日志</el-button>
        </div>
      </template>

      <!-- 操作日志列表表格 -->
      <el-table
        :data="logList"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="operation_time" label="操作时间" width="180" />
        <el-table-column prop="operation_type" label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getOperationTypeColor(scope.row.operation_type)">
              {{ scope.row.operation_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operation_content" label="操作内容" />
        <el-table-column prop="operator_name" label="操作人" width="120" />
        <el-table-column prop="duration" label="耗时(s)" width="100" />
        <el-table-column prop="ip_address" label="IP地址" width="140" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button size="small" @click="viewLogDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="操作日志详情"
      width="600px"
    >
      <el-descriptions :column="2" border v-if="selectedLog">
        <el-descriptions-item label="操作ID">{{ selectedLog.id }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ selectedLog.operation_time }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ selectedLog.operation_type }}</el-descriptions-item>
        <el-descriptions-item label="操作人">{{ selectedLog.operator_name }}</el-descriptions-item>
        <el-descriptions-item label="耗时">{{ selectedLog.duration }}s</el-descriptions-item>
        <el-descriptions-item label="状态">{{ selectedLog.status }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedLog.ip_address }}</el-descriptions-item>
        <el-descriptions-item label="User Agent" span="2">{{ selectedLog.user_agent }}</el-descriptions-item>
        <el-descriptions-item label="操作内容" span="2">{{ selectedLog.operation_content }}</el-descriptions-item>
        <el-descriptions-item label="错误信息" span="2" v-if="selectedLog.error_message">
          {{ selectedLog.error_message }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { logApi, type OperationLog } from '@/api'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const logList = ref<OperationLog[]>([])
const selectedLog = ref<OperationLog | null>(null)

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 10,
  total: 0
})

// 获取操作日志列表
const fetchLogList = async () => {
  loading.value = true
  try {
    const response = await logApi.getLogList({
      page: pagination.page,
      page_size: pagination.page_size
    })

    if (response.code === 200) {
      logList.value = response.data.items
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取操作日志失败')
    }
  } catch (error) {
    ElMessage.error('获取操作日志失败')
    console.error('获取操作日志失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取操作类型颜色
const getOperationTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'create': 'success',
    'update': 'warning',
    'delete': 'danger',
    'view': 'info',
    'login': 'primary'
  }
  return colorMap[type] || 'info'
}

// 查看日志详情
const viewLogDetail = (log: OperationLog) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

// 导出日志
const exportLogs = () => {
  ElMessage.info('导出功能开发中...')
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.page_size = val
  pagination.page = 1
  fetchLogList()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchLogList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchLogList()
})
</script>

<style scoped>
.sub-page {
  padding: 10px;
}

.box-card {
  margin-top: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>