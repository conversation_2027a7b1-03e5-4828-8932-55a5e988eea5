<template>
  <div class="sub-page">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
          <el-button type="primary" size="small" @click="showCreateDialog">新增角色</el-button>
        </div>
      </template>

      <!-- 角色列表表格 -->
      <el-table
        :data="roleList"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="角色名称" width="150" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="permissions" label="权限" width="200">
          <template #default="scope">
            <el-tag
              v-for="permission in scope.row.permissions.slice(0, 3)"
              :key="permission"
              size="small"
              style="margin-right: 5px;"
            >
              {{ permission }}
            </el-tag>
            <span v-if="scope.row.permissions.length > 3">
              +{{ scope.row.permissions.length - 3 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editRole(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteRole(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑角色' : '新增角色'"
      width="600px"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleFormRules"
        label-width="80px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="roleForm.description" type="textarea" />
        </el-form-item>
        <el-form-item label="权限" prop="permissions">
          <el-checkbox-group v-model="roleForm.permissions">
            <el-checkbox
              v-for="permission in availablePermissions"
              :key="permission"
              :label="permission"
            >
              {{ permission }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRole" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { roleApi, type Role, type RoleCreate, type RoleUpdate } from '@/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const roleList = ref<Role[]>([])
const roleFormRef = ref<FormInstance>()
const availablePermissions = ref<string[]>([])

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 10,
  total: 0
})

// 角色表单数据
const roleForm = reactive<RoleCreate & { id?: number }>({
  name: '',
  description: '',
  permissions: []
})

// 表单验证规则
const roleFormRules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true
  try {
    const response = await roleApi.getRoleList({
      page: pagination.page,
      page_size: pagination.page_size
    })

    if (response.code === 200) {
      roleList.value = response.data.items
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取角色列表失败')
    }
  } catch (error) {
    ElMessage.error('获取角色列表失败')
    console.error('获取角色列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取所有权限列表
const fetchPermissions = async () => {
  try {
    const response = await roleApi.getAllPermissions()
    if (response.code === 200) {
      availablePermissions.value = response.data.permissions || []
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    // 设置默认权限列表
    availablePermissions.value = [
      'user:view', 'user:create', 'user:update', 'user:delete',
      'role:view', 'role:create', 'role:update', 'role:delete',
      'log:view'
    ]
  }
}

// 显示创建角色对话框
const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑角色
const editRole = (role: Role) => {
  isEdit.value = true
  roleForm.id = role.id
  roleForm.name = role.name
  roleForm.description = role.description || ''
  roleForm.permissions = [...role.permissions]
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  roleForm.id = undefined
  roleForm.name = ''
  roleForm.description = ''
  roleForm.permissions = []
  roleFormRef.value?.resetFields()
}

// 提交角色表单
const submitRole = async () => {
  if (!roleFormRef.value) return

  const valid = await roleFormRef.value.validate()
  if (!valid) return

  submitting.value = true
  try {
    if (isEdit.value) {
      // 更新角色
      const updateData: RoleUpdate = {
        name: roleForm.name,
        description: roleForm.description,
        permissions: roleForm.permissions
      }

      const response = await roleApi.updateRole(roleForm.id!, updateData)
      if (response.code === 200) {
        ElMessage.success('角色更新成功')
        dialogVisible.value = false
        await fetchRoleList()
      } else {
        ElMessage.error(response.msg || '角色更新失败')
      }
    } else {
      // 创建角色
      const response = await roleApi.createRole({
        name: roleForm.name,
        description: roleForm.description,
        permissions: roleForm.permissions
      })

      if (response.code === 200) {
        ElMessage.success('角色创建成功')
        dialogVisible.value = false
        await fetchRoleList()
      } else {
        ElMessage.error(response.msg || '角色创建失败')
      }
    }
  } catch (error) {
    ElMessage.error(isEdit.value ? '角色更新失败' : '角色创建失败')
    console.error('角色操作失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除角色
const deleteRole = async (role: Role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await roleApi.deleteRole(role.id)
    if (response.code === 200) {
      ElMessage.success('角色删除成功')
      await fetchRoleList()
    } else {
      ElMessage.error(response.msg || '角色删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('角色删除失败')
      console.error('角色删除失败:', error)
    }
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.page_size = val
  pagination.page = 1
  fetchRoleList()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchRoleList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchRoleList()
  fetchPermissions()
})
</script>

<style scoped>
.sub-page {
  padding: 10px;
}

.box-card {
  margin-top: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.el-checkbox {
  margin-right: 0;
}
</style>