<template>
  <div class="sub-page">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" size="small" @click="showCreateDialog">新增用户</el-button>
        </div>
      </template>

      <!-- 用户列表表格 -->
      <el-table
        :data="userList"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="nickname" label="昵称" width="120" />
        <el-table-column prop="remark" label="备注" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteUser(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit || userForm.password">
          <el-input v-model="userForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="userForm.nickname" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="userForm.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUser" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { userApi, type User, type UserCreate, type UserUpdate } from '@/api'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const userList = ref<User[]>([])
const userFormRef = ref<FormInstance>()

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 10,
  total: 0
})

// 用户表单数据
const userForm = reactive<UserCreate & { id?: number }>({
  username: '',
  password: '',
  nickname: '',
  remark: ''
})

// 表单验证规则
const userFormRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
  ]
}

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const response = await userApi.getUserList({
      page: pagination.page,
      page_size: pagination.page_size
    })

    if (response.code === 200) {
      userList.value = response.data.items
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 显示创建用户对话框
const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑用户
const editUser = (user: User) => {
  isEdit.value = true
  userForm.id = user.id
  userForm.username = user.username
  userForm.nickname = user.nickname || ''
  userForm.remark = user.remark || ''
  userForm.password = '' // 编辑时密码为空，表示不修改
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  userForm.id = undefined
  userForm.username = ''
  userForm.password = ''
  userForm.nickname = ''
  userForm.remark = ''
  userFormRef.value?.resetFields()
}

// 提交用户表单
const submitUser = async () => {
  if (!userFormRef.value) return

  const valid = await userFormRef.value.validate()
  if (!valid) return

  submitting.value = true
  try {
    if (isEdit.value) {
      // 更新用户
      const updateData: UserUpdate = {
        nickname: userForm.nickname,
        remark: userForm.remark
      }
      if (userForm.password) {
        updateData.password = userForm.password
      }

      const response = await userApi.updateUser(userForm.id!, updateData)
      if (response.code === 200) {
        ElMessage.success('用户更新成功')
        dialogVisible.value = false
        await fetchUserList()
      } else {
        ElMessage.error(response.msg || '用户更新失败')
      }
    } else {
      // 创建用户
      const response = await userApi.createUser({
        username: userForm.username,
        password: userForm.password,
        nickname: userForm.nickname,
        remark: userForm.remark
      })

      if (response.code === 200) {
        ElMessage.success('用户创建成功')
        dialogVisible.value = false
        await fetchUserList()
      } else {
        ElMessage.error(response.msg || '用户创建失败')
      }
    }
  } catch (error) {
    ElMessage.error(isEdit.value ? '用户更新失败' : '用户创建失败')
    console.error('用户操作失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除用户
const deleteUser = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await userApi.deleteUser(user.id)
    if (response.code === 200) {
      ElMessage.success('用户删除成功')
      await fetchUserList()
    } else {
      ElMessage.error(response.msg || '用户删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('用户删除失败')
      console.error('用户删除失败:', error)
    }
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.page_size = val
  pagination.page = 1
  fetchUserList()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  fetchUserList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUserList()
})
</script>

<style scoped>
.sub-page {
  padding: 10px;
}

.box-card {
  margin-top: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>